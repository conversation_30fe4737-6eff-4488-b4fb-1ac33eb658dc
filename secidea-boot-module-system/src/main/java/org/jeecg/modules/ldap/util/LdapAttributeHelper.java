package org.jeecg.modules.ldap.util;

import org.jeecg.modules.ldap.config.LdapAttributeMapping;
import org.jeecg.modules.ldap.config.LdapConfigurationManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.naming.NamingException;
import javax.naming.directory.Attribute;
import javax.naming.directory.Attributes;
import javax.naming.directory.SearchControls;

/**
 * 用于LDAP属性操作的工具类，支持可配置的映射。
 *
 * <p>该辅助类提供方法，用于使用可配置的属性名获取LDAP属性，并支持不同类型的LDAP服务器及其各自不同的属性命名约定。</p>
 */
@Component
public class LdapAttributeHelper {

    private final LdapConfigurationManager configManager;

    public static final String[] ALL_ATTR = {"*", "+"};

    @Autowired
    public LdapAttributeHelper(LdapConfigurationManager configManager) {
        this.configManager = configManager;
    }

    /**
     * 获取一个字符串属性值，支持fallback机制。
     *
     * @param attributes   LDAP 属性
     * @param primaryAttr  主属性名称
     * @param fallbackAttr fallback属性名称（可为null）
     * @return 属性值，如果未找到则为null
     * @throws NamingException 如果在检索属性时发生错误
     */
    public String getStringAttribute(Attributes attributes, String primaryAttr, String fallbackAttr)
            throws NamingException {

        if (primaryAttr != null) {
            Attribute attr = attributes.get(primaryAttr);
            if (attr != null && attr.get() != null) {
                return attr.get().toString();
            }
        }

        if (fallbackAttr != null) {
            Attribute attr = attributes.get(fallbackAttr);
            if (attr != null && attr.get() != null) {
                return attr.get().toString();
            }
        }

        return null;
    }

    public String getDistinguishedName(Attributes attributes, String entityType) throws NamingException {
        String primaryDnAttr = configManager.getDistinguishedNameAttribute(entityType);
        String fallbackDnAttr = configManager.getAlternativeDistinguishedNameAttribute(entityType);

        return getStringAttribute(attributes, primaryDnAttr, fallbackDnAttr);
    }


    public String getUsername(Attributes attributes) throws NamingException {
        LdapAttributeMapping.UserAttributes userAttrs = configManager.getAttributeMapping().getUser();
        return getStringAttribute(attributes, userAttrs.getUsername(), userAttrs.getAlternativeUsername());
    }

    public String getCommonName(Attributes attributes) throws NamingException {
        LdapAttributeMapping.UserAttributes userAttrs = configManager.getAttributeMapping().getUser();
        return getStringAttribute(attributes, userAttrs.getCommonName(), null);
    }

    public String getFullName(Attributes attributes) throws NamingException {
        LdapAttributeMapping.UserAttributes userAttrs = configManager.getAttributeMapping().getUser();

        String fullName = getStringAttribute(attributes, userAttrs.getDisplayName(), userAttrs.getCommonName());

        // 如果仍然为空，尝试通过givenName和surname构建
        if (fullName == null) {
            String givenName = getStringAttribute(attributes, userAttrs.getGivenName(), null);
            String surname = getStringAttribute(attributes, userAttrs.getSurname(), null);

            if (givenName != null && surname != null) {
                fullName = givenName + " " + surname;
            } else if (givenName != null) {
                fullName = givenName;
            } else if (surname != null) {
                fullName = surname;
            }
        }

        return fullName;
    }


    public String getEmail(Attributes attributes) throws NamingException {
        LdapAttributeMapping.UserAttributes userAttrs = configManager.getAttributeMapping().getUser();
        return getStringAttribute(attributes, userAttrs.getMail(), null);
    }


    public String getDepartment(Attributes attributes) throws NamingException {
        LdapAttributeMapping.UserAttributes userAttrs = configManager.getAttributeMapping().getUser();

        // 首先尝试 department，然后是 departmentNumber，最后以 ou 作为备选。
        String department = getStringAttribute(attributes, userAttrs.getDepartment(),
                                               userAttrs.getAlternativeDepartment());

        if (department == null) {
            department = getStringAttribute(attributes, userAttrs.getOrganizationalUnit(), null);
        }

        return department;
    }

    public String getTitle(Attributes attributes) throws NamingException {
        LdapAttributeMapping.UserAttributes userAttrs = configManager.getAttributeMapping().getUser();
        return getStringAttribute(attributes, userAttrs.getTitle(), null);
    }

    public String getTelephoneNumber(Attributes attributes) throws NamingException {
        LdapAttributeMapping.UserAttributes userAttrs = configManager.getAttributeMapping().getUser();
        return getStringAttribute(attributes, userAttrs.getTelephoneNumber(), null);
    }

    public String getCompany(Attributes attributes) throws NamingException {
        LdapAttributeMapping.UserAttributes userAttrs = configManager.getAttributeMapping().getUser();
        return getStringAttribute(attributes, userAttrs.getCompany(), null);
    }

    public String getUserDescription(Attributes attributes) throws NamingException {
        LdapAttributeMapping.UserAttributes userAttrs = configManager.getAttributeMapping().getUser();
        return getStringAttribute(attributes, userAttrs.getDescription(), null);
    }

    public String getGroupName(Attributes attributes) throws NamingException {
        LdapAttributeMapping.GroupAttributes groupAttrs = configManager.getAttributeMapping().getGroup();
        return getStringAttribute(attributes, groupAttrs.getCommonName(), groupAttrs.getName());
    }

    public String getDescription(Attributes attributes) throws NamingException {
        LdapAttributeMapping.GroupAttributes groupAttrs = configManager.getAttributeMapping().getGroup();
        return getStringAttribute(attributes, groupAttrs.getDescription(), null);
    }

    public String getOrganizationName(Attributes attributes) throws NamingException {
        LdapAttributeMapping.OrganizationAttributes orgAttrs = configManager.getAttributeMapping().getOrganization();
        return getStringAttribute(attributes, orgAttrs.getOrganization(), null);
    }

    public String getOrganizationalUnitName(Attributes attributes) throws NamingException {
        LdapAttributeMapping.OrganizationAttributes orgAttrs = configManager.getAttributeMapping().getOrganization();
        return getStringAttribute(attributes, orgAttrs.getOrganizationalUnit(), null);
    }


    public boolean isUserAccountActive(Attributes attributes) throws NamingException {
        if (configManager.isActiveDirectory()) {
            LdapAttributeMapping.UserAttributes userAttrs = configManager.getAttributeMapping().getUser();
            String accountControl = getStringAttribute(attributes, userAttrs.getUserAccountControl(), null);

            if (accountControl != null) {
                try {
                    int controlValue = Integer.parseInt(accountControl);
                    // 在 Active Directory 中，位 2（值 2）表示禁用账户。
                    return (controlValue & 2) == 0;
                } catch (NumberFormatException e) {
                    // 如果解析失败，假定为活动状态
                    return true;
                }
            }
        }

        // 对于非 AD 服务器，或如果缺少 userAccountControl 属性，则假定为启用状态。
        return true;
    }

    public SearchControls getSearchControls() {
        SearchControls searchControls = new SearchControls();
        searchControls.setSearchScope(SearchControls.SUBTREE_SCOPE);
        searchControls.setReturningAttributes(ALL_ATTR);
        return searchControls;
    }
}
