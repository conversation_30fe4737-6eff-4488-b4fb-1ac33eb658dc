package org.jeecg.modules.ldap.service;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.ldap.exception.LdapSynchronizationException;
import org.jeecg.modules.ldap.model.OrganizationUnit;
import org.jeecg.modules.ldap.util.LdapRepositoryManager;
import org.jeecg.modules.ldap.util.OrganizationTreeBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ldap.core.support.LdapContextSource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用于LDAP组织（Organization）和组织单元（Organizational Unit）操作的服务类。
 *
 * <p>此服务提供发现和管理LDAP目录中组织（o）和组织单元（ou）条目的业务逻辑。
 */
@Slf4j
@Service
public class LdapOrganizationService {

    private final LdapRepositoryManager repositoryManager;
    private final OrganizationTreeBuilder treeBuilder;

    @Autowired
    public LdapOrganizationService(LdapRepositoryManager repositoryManager,
                                   OrganizationTreeBuilder treeBuilder) {
        this.repositoryManager = repositoryManager;
        this.treeBuilder = treeBuilder;
    }

    /**
     * 使用提供的LDAP上下文源配置服务。
     * 此方法必须在任何服务操作之前调用。
     *
     * @param contextSource 要使用的LDAP上下文源
     * @throws IllegalArgumentException 如果contextSource为null
     */
    public void configureLdapConnection(LdapContextSource contextSource) {
        repositoryManager.configureRepositories(contextSource);
    }

    /**
     * 获取指定基本DN下的所有组织单位。
     *
     * <p>此方法在给定基本DN下查找组织 (o) 和组织单位 (ou) 条目，
     * 从而提供组织结构的统一视图。</p>
     *
     * @param baseDn 用于搜索的基础可分辨名称
     * @return 带有类型区分的组织单位列表
     * @throws LdapSynchronizationException 如果操作失败
     * @throws IllegalArgumentException     如果baseDn为null或为空
     */
    public List<OrganizationUnit> discoverOrganizationalUnits(String baseDn) {
        if (baseDn == null) {
            throw new IllegalArgumentException("Base DN cannot be null");
        }

        try {
            repositoryManager.validateRepositoryConfiguration();

            List<OrganizationUnit> organizationalUnits =
                    repositoryManager.getOrganizationRepository().findAllOrganizationalUnits(baseDn);

            log.info("Successfully discovered {} organizational units under base DN: {}",
                     organizationalUnits.size(), baseDn);

            return organizationalUnits;

        } catch (Exception e) {
            log.error("Failed to discover organizational units under base DN: {}", baseDn, e);
            throw new LdapSynchronizationException(
                    "Failed to discover organizational units under base DN: " + baseDn, e);
        }
    }

    /**
     * 获取具有筛选功能的组织单元。
     *
     * <p>此方法允许使用LDAP筛选表达式进行更具体的搜索，
     * 以查找符合特定条件的组织单元。</p>
     *
     * @param baseDn 要在其下搜索的基本专有名称
     * @param filter 用于筛选结果的LDAP筛选表达式
     * @return 符合筛选条件的组织单元列表
     * @throws LdapSynchronizationException 如果操作失败
     * @throws IllegalArgumentException     如果baseDn为空或为空字符串
     */
    public List<OrganizationUnit> discoverOrganizationalUnitsWithFilter(String baseDn, String filter) {
        if (baseDn == null) {
            throw new IllegalArgumentException("Base DN cannot be null");
        }

        try {
            repositoryManager.validateRepositoryConfiguration();

            List<OrganizationUnit> organizationalUnits =
                    repositoryManager.getOrganizationRepository().findOrganizationalUnitsWithFilter(baseDn, filter);

            log.debug("Successfully discovered {} organizational units under base DN: {} with filter: {}",
                      organizationalUnits.size(), baseDn, filter);

            return organizationalUnits;

        } catch (Exception e) {
            log.error("Failed to discover organizational units under base DN: {} with filter: {}",
                      baseDn, filter, e);
            throw new LdapSynchronizationException(
                    "Failed to discover organizational units under base DN: " + baseDn +
                            " with filter: " + filter, e);
        }
    }

    /**
     * 仅检索指定基本DN下的组织 (o) 条目。
     *
     * @param baseDn 要在其下搜索的基本专有名称
     * @return 组织条目列表
     * @throws LdapSynchronizationException 如果操作失败
     * @throws IllegalArgumentException     如果 baseDn 为 null 或为空
     */
    public List<OrganizationUnit> discoverOrganizations(String baseDn) {

        List<OrganizationUnit> allUnits = discoverOrganizationalUnits(baseDn);

        List<OrganizationUnit> organizations = allUnits.stream()
                .filter(ou -> ou.getType() == OrganizationUnit.Type.ORGANIZATION)
                .collect(Collectors.toList());

        log.debug("Found {} organizations under base DN: {}", organizations.size(), baseDn);

        return organizations;
    }

    /**
     * 只检索指定基本 DN 下的组织单元 (ou) 条目。
     *
     * @param baseDn 用于搜索的基本可分辨名称
     * @return 组织单元条目列表
     * @throws LdapSynchronizationException 如果操作失败
     * @throws IllegalArgumentException     如果 baseDn 为 null 或为空
     */
    public List<OrganizationUnit> discoverOrganizationalUnitsOnly(String baseDn) {
        List<OrganizationUnit> allUnits = discoverOrganizationalUnits(baseDn);

        List<OrganizationUnit> organizationalUnits = allUnits.stream()
                .filter(ou -> ou.getType() == OrganizationUnit.Type.ORGANIZATIONAL_UNIT)
                .collect(Collectors.toList());

        log.debug("Found {} organizational units (ou only) under base DN: {}",
                  organizationalUnits.size(), baseDn);

        return organizationalUnits;
    }

    /**
     * 通过判别名查找特定的组织单元。
     *
     * @param dn 组织单元的判别名
     * @return 如果找到，返回该组织单元
     * @throws LdapSynchronizationException 如果操作失败或未找到该单元
     * @throws IllegalArgumentException     如果dn为null或为空
     */
    public OrganizationUnit findOrganizationalUnitByDn(String dn) {
        if (!StringUtils.hasText(dn)) {
            throw new IllegalArgumentException("Distinguished name cannot be null or empty");
        }

        log.debug("Finding organizational unit by DN: {}", dn);

        try {
            repositoryManager.validateRepositoryConfiguration();

            OrganizationUnit organizationalUnit =
                    repositoryManager.getOrganizationRepository().findByDistinguishedName(dn);

            if (organizationalUnit == null) {
                throw new LdapSynchronizationException("Organizational unit not found with DN: " + dn);
            }

            log.debug("Successfully found organizational unit: {} ({})",
                      organizationalUnit.getName(), organizationalUnit.getType());

            return organizationalUnit;

        } catch (Exception e) {
            log.error("Failed to find organizational unit by DN: {}", dn, e);
            throw new LdapSynchronizationException(
                    "Failed to find organizational unit by DN: " + dn, e);
        }
    }

    /**
     * 验证给定DN是否表示有效的组织单元。
     *
     * @param dn 待验证的可分辨名称
     * @return 如果DN表示有效的组织单元，则返回true；否则返回false
     */
    public boolean isValidOrganizationalUnit(String dn) {
        if (!StringUtils.hasText(dn)) {
            return false;
        }

        try {
            OrganizationUnit ou = repositoryManager.getOrganizationRepository().findByDistinguishedName(dn);
            return ou != null;
        } catch (Exception e) {
            log.debug("DN validation failed for: {}", dn, e);
            return false;
        }
    }

    /**
     * 获取指定基本 DN 下的组织单位统计信息。
     *
     * @param baseDn 要分析的基本专有名称
     * @return 包含统计信息的格式化字符串
     * @throws LdapSynchronizationException 如果操作失败
     * @throws IllegalArgumentException     如果 baseDn 为 null 或为空
     */
    public String getOrganizationalUnitStatistics(String baseDn) {

        List<OrganizationUnit> allUnits = discoverOrganizationalUnits(baseDn);

        long organizationCount = allUnits.stream()
                .filter(ou -> ou.getType() == OrganizationUnit.Type.ORGANIZATION)
                .count();

        long organizationalUnitCount = allUnits.stream()
                .filter(ou -> ou.getType() == OrganizationUnit.Type.ORGANIZATIONAL_UNIT)
                .count();

        String statistics = String.format(
                "Organizational Unit Statistics for base DN '%s':\n" +
                        "- Total entries: %d\n" +
                        "- Organizations (o): %d\n" +
                        "- Organizational Units (ou): %d",
                baseDn, allUnits.size(), organizationCount, organizationalUnitCount
        );

        log.debug("Generated statistics: {}", statistics);

        return statistics;
    }

    /**
     * 发现组织单位并将其构建为树形结构。
     *
     * <p>此方法从指定的基可分辨名称(base DN)中检索所有组织单位，
     * 并根据它们的可分辨名称构建分层树形结构。</p>
     *
     * @param baseDn 要搜索的基可分辨名称
     * @return 包含完整子树的根组织单位列表
     * @throws LdapSynchronizationException 如果发现操作失败
     */
    public List<OrganizationUnit> discoverOrganizationalUnitsAsTree(String baseDn) {
        try {

            // 获取组织单元的扁平列表
            List<OrganizationUnit> flatList = discoverOrganizationalUnits(baseDn);

            // 构建树结构
            List<OrganizationUnit> treeRoots = treeBuilder.buildTree(flatList);

            log.debug("Successfully built tree structure with {} root nodes from {} total units",
                      treeRoots.size(), flatList.size());

            // 验证树结构
            OrganizationTreeBuilder.TreeValidationResult validation = treeBuilder.validateTree(treeRoots);
            if (validation.hasErrors()) {
                log.warn("Tree validation found {} errors: {}",
                         validation.getErrors().size(), validation.getErrors());
            }
            if (validation.hasWarnings()) {
                log.info("Tree validation found {} warnings: {}",
                         validation.getWarnings().size(), validation.getWarnings());
            }

            return treeRoots;

        } catch (Exception e) {
            log.error("Failed to discover organizational units as tree from base DN: {}", baseDn, e);
            throw new LdapSynchronizationException(
                    "Failed to discover organizational units as tree from base DN: " + baseDn, e);
        }
    }

    /**
     * 根据过滤器发现组织单位，并将其构建为树形结构。
     *
     * @param baseDn 用于搜索的基准专有名称
     * @param filter 要应用的LDAP过滤器（可为null或空字符串）
     * @return 包含完整子树的根组织单位列表
     * @throws LdapSynchronizationException 如果发现操作失败
     */
    public List<OrganizationUnit> discoverOrganizationalUnitsAsTreeWithFilter(String baseDn, String filter) {
        try {
            // 获取带筛选的组织单位扁平列表
            List<OrganizationUnit> flatList = discoverOrganizationalUnitsWithFilter(baseDn, filter);

            // 构建树结构
            List<OrganizationUnit> treeRoots = treeBuilder.buildTree(flatList);

            log.debug("Successfully built filtered tree structure with {} root nodes from {} total units",
                      treeRoots.size(), flatList.size());

            return treeRoots;

        } catch (Exception e) {
            log.error("Failed to discover organizational units as tree with filter from base DN: {}", baseDn, e);
            throw new LdapSynchronizationException(
                    "Failed to discover organizational units as tree with filter from base DN: " + baseDn, e);
        }
    }

    /**
     * 发现组织单元并将其作为单根树返回。
     * 如果存在多个根，则将它们置于一个虚拟根下。
     *
     * @param baseDn          要从中搜索的基本专有名称
     * @param virtualRootName 如果存在多个根，虚拟根的名称
     * @return 包含完整树的单根组织单元
     * @throws LdapSynchronizationException 如果发现操作失败
     */
    public OrganizationUnit discoverOrganizationalUnitsAsSingleTree(String baseDn, String virtualRootName) {
        try {
            log.debug("Discovering organizational units as single tree structure from base DN: {}", baseDn);

            // 获取组织单元的扁平列表
            List<OrganizationUnit> flatList = discoverOrganizationalUnits(baseDn);

            // 构建单根树结构
            OrganizationUnit singleRoot = treeBuilder.buildSingleRootTree(flatList, virtualRootName);

            log.debug("Successfully built single root tree structure with {} total descendants",
                      singleRoot.getDescendantCount());

            return singleRoot;

        } catch (Exception e) {
            log.error("Failed to discover organizational units as single tree from base DN: {}", baseDn, e);
            throw new LdapSynchronizationException(
                    "Failed to discover organizational units as single tree from base DN: " + baseDn, e);
        }
    }

    /**
     * 从树中获取所有叶子组织单元（即没有子单元的单元）。
     *
     * @param baseDn 开始搜索的基础专有名称
     * @return 所有叶子组织单元的列表
     * @throws LdapSynchronizationException 如果操作失败
     */
    public List<OrganizationUnit> getLeafOrganizationalUnits(String baseDn) {
        try {
            log.debug("Getting leaf organizational units from base DN: {}", baseDn);

            // 构建树结构
            List<OrganizationUnit> treeRoots = discoverOrganizationalUnitsAsTree(baseDn);

            // 获取叶节点
            List<OrganizationUnit> leafNodes = treeBuilder.getLeafNodes(treeRoots);

            log.debug("Found {} leaf organizational units", leafNodes.size());

            return leafNodes;

        } catch (Exception e) {
            log.error("Failed to get leaf organizational units from base DN: {}", baseDn, e);
            throw new LdapSynchronizationException(
                    "Failed to get leaf organizational units from base DN: " + baseDn, e);
        }
    }
}
