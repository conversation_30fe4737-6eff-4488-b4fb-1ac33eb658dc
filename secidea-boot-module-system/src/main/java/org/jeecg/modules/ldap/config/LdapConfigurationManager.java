package org.jeecg.modules.ldap.config;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * LDAP配置管理器处理不同的LDAP服务器类型及其属性映射
 *
 * <p>此组件会根据指定的
 * 服务器类型，在允许的同时为常见的LDAP服务器提供预定义的配置
 * 通过应用程序属性自定义覆盖。</p>
 */
@Component
@Slf4j
@Getter
public class LdapConfigurationManager {

    private LdapServerType serverType;

    private LdapAttributeMapping attributeMapping;

    public void configure(LdapServerType serverType, LdapAttributeMapping attributeMapping) {
        this.serverType = serverType;
        this.attributeMapping = attributeMapping;
        configureAttributeMappings();
        log.debug("LDAP configuration initialized successfully");
    }

    /**
     * 根据LDAP类型，配置相关属性映射
     */
    private void configureAttributeMappings() {
        switch (serverType) {
            case ACTIVE_DIRECTORY:
                configureActiveDirectoryMappings();
                break;
            case OPENLDAP:
                configureOpenLdapMappings();
                break;
            case GENERIC:
            default:
                log.info("使用通用/自定义LDAP属性映射");
                break;
        }
    }

    /**
     * AD 域属性映射
     */
    private void configureActiveDirectoryMappings() {
        log.debug("AD 域属性映射");
        LdapAttributeMapping.UserAttributes userAttrs = attributeMapping.getUser();
        if ("uid".equals(userAttrs.getUsername())) {
            userAttrs.setUsername("sAMAccountName");
        }
        if ("entryDN".equals(userAttrs.getAlternativeDistinguishedName())) {
            userAttrs.setAlternativeDistinguishedName("distinguishedName");
        }

        // AD 域组属性
        LdapAttributeMapping.GroupAttributes groupAttrs = attributeMapping.getGroup();
        if ("entryDN".equals(groupAttrs.getAlternativeDistinguishedName())) {
            groupAttrs.setAlternativeDistinguishedName("distinguishedName");
        }

        // o/ou 属性
        LdapAttributeMapping.OrganizationAttributes orgAttrs = attributeMapping.getOrganization();
        if ("entryDN".equals(orgAttrs.getAlternativeDistinguishedName())) {
            orgAttrs.setAlternativeDistinguishedName("distinguishedName");
        }

        // Object classes 属性
        LdapAttributeMapping.ObjectClasses objClasses = attributeMapping.getObjectClasses();
        if ("person".equals(objClasses.getPerson())) {
            objClasses.setPerson("user");
        }
    }

    /**
     * OpenLDAP 属性配置
     */
    private void configureOpenLdapMappings() {
        log.debug("OpenLDAP 域属性映射");
        LdapAttributeMapping.UserAttributes userAttrs = attributeMapping.getUser();
        if ("sAMAccountName".equals(userAttrs.getUsername())) {
            userAttrs.setUsername("uid");
        }
        LdapAttributeMapping.UserAttributes userAttributes = attributeMapping.getUser();
        if ("distinguishedName".equals(userAttributes.getDistinguishedName())) {
            userAttributes.setDistinguishedName("entryDN");
        }

        LdapAttributeMapping.GroupAttributes groupAttrs = attributeMapping.getGroup();
        if ("distinguishedName".equals(groupAttrs.getDistinguishedName())) {
            groupAttrs.setDistinguishedName("entryDN");
        }

        LdapAttributeMapping.OrganizationAttributes orgAttrs = attributeMapping.getOrganization();
        if ("distinguishedName".equals(orgAttrs.getDistinguishedName())) {
            orgAttrs.setDistinguishedName("entryDN");
        }
    }


    public boolean isActiveDirectory() {
        return serverType == LdapServerType.ACTIVE_DIRECTORY;
    }


    public boolean isOpenLdap() {
        return serverType == LdapServerType.OPENLDAP;
    }

    /**
     * 根据服务器类型获取主用户名属性。
     *
     * @return 用户名属性名称
     */
    public String getUsernameAttribute() {
        return attributeMapping.getUser().getUsername();
    }

    /**
     * 根据服务器类型获取备用用户名属性。
     *
     * @return 备用用户名属性名称
     */
    public String getAlternativeUsernameAttribute() {
        return attributeMapping.getUser().getAlternativeUsername();
    }

    /**
     * 根据class类型获取DistinguishedName属性。
     *
     * @param entityType 实体类型（用户、群组、组织）
     * @return 专有名称属性名
     */
    public String getDistinguishedNameAttribute(String entityType) {
        switch (entityType.toLowerCase()) {
            case "user":
                return attributeMapping.getUser().getDistinguishedName();
            case "group":
                return attributeMapping.getGroup().getDistinguishedName();
            case "organization":
                return attributeMapping.getOrganization().getDistinguishedName();
            default:
                return "distinguishedName";
        }
    }

    /**
     * 根据class类型获取AlternativeDistinguishedName属性。
     *
     * @param entityType 实体类型（用户、组、组织）
     * @return AlternativeDistinguishedName属性
     */
    public String getAlternativeDistinguishedNameAttribute(String entityType) {
        switch (entityType.toLowerCase()) {
            case "user":
                return attributeMapping.getUser().getAlternativeDistinguishedName();
            case "group":
                return attributeMapping.getGroup().getAlternativeDistinguishedName();
            case "organization":
                return attributeMapping.getOrganization().getAlternativeDistinguishedName();
            default:
                return "entryDN";
        }
    }
}
