package org.jeecg.modules.ldap.service;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.ldap.config.LdapAttributeMapping;
import org.jeecg.modules.ldap.config.LdapConfigurationManager;
import org.jeecg.modules.ldap.config.LdapServerType;
import org.jeecg.modules.ldap.exception.LdapSynchronizationException;
import org.jeecg.modules.ldap.model.LdapUser;
import org.jeecg.modules.ldap.model.OrganizationUnit;
import org.jeecg.modules.ldap.repository.LdapUserRepository;
import org.jeecg.modules.ldap.util.LdapDnUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ldap.core.support.LdapContextSource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * LDAP 同步操作的主要服务类。
 *
 * <p>此服务提供了 LDAP 同步功能的主要接口，将组织发现和用户检索与全面的群组支持相结合。
 * 它作为所有 LDAP 同步操作的主要入口点。</p>
 *
 * <p>主要功能：</p>
 * <ul>
 *   <li>具备过滤功能的组织/组织单元发现</li>
 *   <li>支持嵌套群组的用户检索</li>
 *   <li>支持多种群组类型，包括 groupOfUniqueNames</li>
 *   <li>全面的错误处理和日志记录</li>
 * </ul>
 */
@Slf4j
@Service
public class LdapSynchronizationService {

    private final LdapOrganizationService organizationService;
    private final LdapUserService userService;
    private final LdapConfigurationManager ldapConfigurationManager;

    @Autowired
    public LdapSynchronizationService(LdapOrganizationService organizationService, LdapUserService userService,
                                      LdapConfigurationManager ldapConfigurationManager) {
        this.organizationService = organizationService;
        this.userService = userService;
        this.ldapConfigurationManager = ldapConfigurationManager;
    }

    /**
     * 使用提供的LDAP上下文源配置服务。
     * 此方法必须在任何服务操作之前调用。
     *
     * @param contextSource 要使用的LDAP上下文源
     * @throws IllegalArgumentException 如果contextSource为null
     */
    public void configureLdapConnection(LdapContextSource contextSource,
                                        LdapServerType ldapServerType,
                                        LdapAttributeMapping attributeMapping) {
        organizationService.configureLdapConnection(contextSource);
        userService.configureLdapConnection(contextSource);
        ldapConfigurationManager.configure(ldapServerType, attributeMapping);
    }

    /**
     * 发现指定基本DN下的所有组织单位。
     *
     * <p>此方法实现了组织/组织单元发现的核心要求。
     * 它检索指定基本DN下的所有组织（o）和组织单元（ou），并明确区分这两种类型。</p>
     *
     * @param baseDn 要在其下搜索的基本专有名称
     * @return 区分类型的组织单位列表
     * @throws LdapSynchronizationException 如果发现操作失败
     * @throws IllegalArgumentException     如果baseDn为空或为空字符串
     */
    public List<OrganizationUnit> discoverOrganizationalUnits(String baseDn) {
        if (!StringUtils.hasText(baseDn)) {
            throw new IllegalArgumentException("Base DN cannot be null or empty");
        }

        log.debug("Starting organizational unit discovery for base DN: {}", baseDn);

        try {
            List<OrganizationUnit> organizationalUnits = organizationService.discoverOrganizationalUnits(baseDn);

            log.debug("Successfully discovered {} organizational units under base DN: {}",
                      organizationalUnits.size(), baseDn);

            // 按类型汇总获取的o/ou数量
            long organizationCount = organizationalUnits.stream()
                    .filter(ou -> ou.getType() == OrganizationUnit.Type.ORGANIZATION)
                    .count();
            long ouCount = organizationalUnits.stream()
                    .filter(ou -> ou.getType() == OrganizationUnit.Type.ORGANIZATIONAL_UNIT)
                    .count();

            log.info("Discovery summary - Organizations (o): {}, Organizational Units (ou): {}",
                     organizationCount, ouCount);

            return organizationalUnits;

        } catch (Exception e) {
            log.error("Failed to discover organizational units for base DN: {}", baseDn, e);
            throw new LdapSynchronizationException(
                    "Organizational unit discovery failed for base DN: " + baseDn, e);
        }
    }

    /**
     * 通过自定义过滤发现组织单元。
     *
     * <p>此方法支持使用LDAP过滤器表达式对o/ou对象进行过滤，从而实现更有针对性的发现操作。</p>
     *
     * @param baseDn 用于搜索的基础判别名
     * @param filter 用于过滤结果的LDAP过滤器表达式
     * @return 匹配过滤条件的组织单元列表
     * @throws LdapSynchronizationException 如果发现操作失败
     * @throws IllegalArgumentException     如果baseDn为null或为空
     */
    public List<OrganizationUnit> discoverOrganizationalUnitsWithFilter(String baseDn, String filter) {
        if (!StringUtils.hasText(baseDn)) {
            throw new IllegalArgumentException("Base DN cannot be null or empty");
        }

        log.debug("Starting filtered organizational unit discovery for base DN: {} with filter: {}",
                  baseDn, filter);

        try {
            List<OrganizationUnit> organizationalUnits =
                    organizationService.discoverOrganizationalUnitsWithFilter(baseDn, filter);

            log.info("Successfully discovered {} filtered organizational units under base DN: {}",
                     organizationalUnits.size(), baseDn);

            // 按类型汇总获取的o/ou数量
            long organizationCount = organizationalUnits.stream()
                    .filter(ou -> ou.getType() == OrganizationUnit.Type.ORGANIZATION)
                    .count();
            long ouCount = organizationalUnits.stream()
                    .filter(ou -> ou.getType() == OrganizationUnit.Type.ORGANIZATIONAL_UNIT)
                    .count();

            log.info("Discovery summary - Organizations (o): {}, Organizational Units (ou): {}",
                     organizationCount, ouCount);

            return organizationalUnits;

        } catch (Exception e) {
            log.error("Failed to discover filtered organizational units for base DN: {} with filter: {}",
                      baseDn, filter, e);
            throw new LdapSynchronizationException(
                    "Filtered organizational unit discovery failed for base DN: " + baseDn +
                            " with filter: " + filter, e);
        }
    }

    /**
     * 通过自定义过滤发现组织单元。
     *
     * <p>此方法支持使用LDAP过滤器表达式对o/ou对象进行过滤，从而实现更有针对性的发现操作。</p>
     *
     * @param baseDn 用于搜索的基础判别名
     * @param filter 用于过滤结果的LDAP过滤器表达式
     * @return 匹配过滤条件的组织单元列表
     * @throws LdapSynchronizationException 如果发现操作失败
     * @throws IllegalArgumentException     如果baseDn为null或为空
     */
    public List<OrganizationUnit> discoverOrganizationalUnitsAsTreeWithFilter(String baseDn, String filter) {
        if (Objects.isNull(baseDn)) {
            throw new IllegalArgumentException("Base DN cannot be null or empty");
        }

        log.debug("Starting filtered organizational unit discovery for base DN: {} with filter: {}",
                  baseDn, filter);

        try {
            List<OrganizationUnit> organizationalUnits =
                    organizationService.discoverOrganizationalUnitsAsTreeWithFilter(baseDn, filter);

            log.info("Successfully discovered {} root filtered organizational units under base DN: {}",
                     organizationalUnits.size(), baseDn);

            return organizationalUnits;

        } catch (Exception e) {
            log.error("Failed to discover filtered organizational units for base DN: {} with filter: {}",
                      baseDn, filter, e);
            throw new LdapSynchronizationException(
                    "Filtered organizational unit discovery failed for base DN: " + baseDn +
                            " with filter: " + filter, e);
        }
    }

    /**
     * 从指定组织单元检索所有用户，并提供全面的组支持。
     *
     * <p>此方法实现了用户检索（带组支持）的核心需求。它以特定的组织（o）或组织单元（ou）作为输入，
     * 并返回该组织结构内的所有用户，处理嵌套组场景，并支持多种组类型，
     * 包括 OpenLDAP 的 groupOfUniqueNames。</p>
     *
     * @param organizationalUnitDn 组织单元的区分名
     * @param userFilter           用于用户过滤的可选 LDAP 过滤器表达式（可为空）
     * @return 来自组织单元及其组的唯一用户的整合列表
     * @throws LdapSynchronizationException 如果检索操作失败
     * @throws IllegalArgumentException     如果 organizationalUnitDn 为空或为空字符串
     */
    public List<LdapUser> getUsersFromOrganizationalUnit(String organizationalUnitDn, String userFilter) {
        if (!StringUtils.hasText(organizationalUnitDn)) {
            throw new IllegalArgumentException("Organizational unit DN cannot be null or empty");
        }

        log.info("Starting user retrieval from organizational unit: {} with filter: {}",
                 organizationalUnitDn, userFilter);

        try {
            // 首先，验证并获取组织单位
            OrganizationUnit organizationalUnit = organizationService.findOrganizationalUnitByDn(organizationalUnitDn);

            // 检索组下的用户
            List<LdapUser> users = userService.getUsersFromOrganizationalUnit(organizationalUnit, userFilter);

            log.info("Successfully retrieved {} users from organizational unit: {}",
                     users.size(), organizationalUnitDn);

            // 记录额外统计数据
            long activeUsers = users.stream().filter(LdapUser::isActive).count();
            log.info("User retrieval summary - Total: {}, Active: {}, Inactive: {}",
                     users.size(), activeUsers, users.size() - activeUsers);

            return users;

        } catch (Exception e) {
            log.error("Failed to retrieve users from organizational unit: {} with filter: {}",
                      organizationalUnitDn, userFilter, e);
            throw new LdapSynchronizationException(
                    "User retrieval failed for organizational unit: " + organizationalUnitDn +
                            " with filter: " + userFilter, e);
        }
    }

    public String getUserDN(String username, String baseDn) {
        if (LdapDnUtils.isDnFormat(username)) {
            return username;
        }
        boolean activeDirectory = ldapConfigurationManager.isActiveDirectory();
        if (activeDirectory) {
            return LdapDnUtils.extractAdUPNName(username, baseDn);
        }
        // 从LDAP服务器中搜索
        LdapUser user = userService.findUsersByUsername(username, "");
        if (Objects.nonNull(user)) {
            return user.getDistinguishedName();
        }
        return null;
    }

    /**
     * 从特定组（支持嵌套组）中检索用户。
     *
     * <p>此方法提供基于组的直接用户检索，支持嵌套组结构和各种组类型。</p>
     *
     * @param groupDn    组的专有名称 (distinguished name)
     * @param userFilter 用于用户过滤的可选 LDAP 过滤表达式（可为 null）
     * @return 来自该组及其嵌套组的用户列表
     * @throws LdapSynchronizationException 如果检索操作失败
     * @throws IllegalArgumentException     如果 groupDn 为 null 或为空
     */
    public List<LdapUser> getUsersFromGroup(String groupDn, String userFilter) {
        if (!StringUtils.hasText(groupDn)) {
            throw new IllegalArgumentException("Group DN cannot be null or empty");
        }

        log.debug("Starting user retrieval from group: {} with filter: {}", groupDn, userFilter);

        try {
            List<LdapUser> users = userService.getUsersFromGroup(groupDn, userFilter);

            log.debug("Successfully retrieved {} users from group: {}", users.size(), groupDn);

            return users;

        } catch (Exception e) {
            log.error("Failed to retrieve users from group: {} with filter: {}", groupDn, userFilter, e);
            throw new LdapSynchronizationException(
                    "User retrieval failed for group: " + groupDn + " with filter: " + userFilter, e);
        }
    }

    /**
     * 执行针对给定基础DN的全面同步操作。
     *
     * <p>此方法结合了组织单元发现与用户检索，
     * 以提供LDAP目录结构的完整同步视图。</p>
     *
     * @param baseDn     要同步的基础专有名称
     * @param ouFilter   用于组织单元发现的可选过滤器（可为空）
     * @param userFilter 用于用户检索的可选过滤器（可为空）
     * @return 包含组织单元和用户数量的同步摘要
     * @throws LdapSynchronizationException 如果同步操作失败
     * @throws IllegalArgumentException     如果baseDn为空或为空字符串
     */
    public String performComprehensiveSync(String baseDn, String ouFilter, String userFilter) {
        if (!StringUtils.hasText(baseDn)) {
            throw new IllegalArgumentException("Base DN cannot be null or empty");
        }

        log.debug("Starting comprehensive synchronization for base DN: {}", baseDn);

        try {
            // 步骤 1: 发现组织单位
            List<OrganizationUnit> organizationalUnits = StringUtils.hasText(ouFilter)
                    ? discoverOrganizationalUnitsWithFilter(baseDn, ouFilter)
                    : discoverOrganizationalUnits(baseDn);

            // 步骤2：从每个组织单位检索用户
            int totalUsers = 0;
            StringBuilder syncReport = new StringBuilder();
            syncReport.append("LDAP Synchronization Report\n");
            syncReport.append("==========================\n");
            syncReport.append(String.format("Base DN: %s\n", baseDn));
            syncReport.append(String.format("OU Filter: %s\n", ouFilter != null ? ouFilter : "None"));
            syncReport.append(String.format("User Filter: %s\n", userFilter != null ? userFilter : "None"));
            syncReport.append(String.format("Discovered Organizational Units: %d\n\n", organizationalUnits.size()));

            for (OrganizationUnit ou : organizationalUnits) {
                try {
                    List<LdapUser> users = getUsersFromOrganizationalUnit(ou.getDistinguishedName(), userFilter);
                    totalUsers += users.size();

                    syncReport.append(String.format("OU: %s (%s)\n", ou.getName(), ou.getType()));
                    syncReport.append(String.format("  DN: %s\n", ou.getDistinguishedName()));
                    syncReport.append(String.format("  Users: %d\n\n", users.size()));

                } catch (Exception e) {
                    log.warn("Failed to retrieve users from OU: {}", ou.getDistinguishedName(), e);
                    syncReport.append(String.format("OU: %s - ERROR: %s\n\n", ou.getName(), e.getMessage()));
                }
            }

            syncReport.append(String.format("Total Users Retrieved: %d\n", totalUsers));

            String report = syncReport.toString();
            log.info("Comprehensive synchronization completed for base DN: {}", baseDn);

            return report;

        } catch (Exception e) {
            log.error("Comprehensive synchronization failed for base DN: {}", baseDn, e);
            throw new LdapSynchronizationException(
                    "Comprehensive synchronization failed for base DN: " + baseDn, e);
        }
    }

    /**
     * 验证 LDAP 连接和基本功能。
     *
     * @param baseDn 用于测试的基准 DN
     * @return 验证结果消息
     * @throws LdapSynchronizationException 如果验证失败
     */
    public String validateLdapConnection(String baseDn) {
        log.debug("Validating LDAP connection with base DN: {}", baseDn);

        try {
            List<OrganizationUnit> testUnits = organizationService.discoverOrganizationalUnits(baseDn);

            String result = String.format(
                    "LDAP Connection Validation Successful\n" +
                            "Base DN: %s\n" +
                            "Organizational Units Found: %d\n" +
                            "Connection Status: ACTIVE",
                    baseDn, testUnits.size()
            );

            log.debug("LDAP connection validation successful for base DN: {}", baseDn);
            return result;

        } catch (Exception e) {
            log.error("LDAP connection validation failed for base DN: {}", baseDn, e);
            throw new LdapSynchronizationException("LDAP connection validation failed: " + e.getMessage(), e);
        }
    }
}
