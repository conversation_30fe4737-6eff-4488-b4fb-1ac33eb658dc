package org.jeecg.modules.ldap.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 表示 LDAP 组织或组织单元的数据传输对象。
 *
 * <p>该类封装了 LDAP 目录中组织 (o) 和组织单元 (ou) 条目的基本属性。它为两种类型的组织结构提供了统一的表示。</p>
 */
@Getter
@Setter
@EqualsAndHashCode
public class OrganizationUnit {

    /**
     * 枚举，表示组织条目的类型。
     */
    public enum Type {
        /**
         * 组织条目 (o)
         */
        ORGANIZATION("o"),
        /**
         * 组织单位条目 (ou)
         */
        ORGANIZATIONAL_UNIT("ou");

        public final String value;

        Type(String value) {
            this.value = value;
        }
    }

    private String distinguishedName;
    private String name;
    private String description;
    private Type type;
    private String parentDn;

    // 树结构字段
    private OrganizationUnit parent;
    private List<OrganizationUnit> children;
    private int level;

    /**
     * 默认构造函数。
     */
    public OrganizationUnit() {
        this.children = new ArrayList<>();
        this.level = 0;
    }

    /**
     * 必需参数构造方法。
     *
     * @param distinguishedName 组织单元的完整区分名
     * @param name              组织单元的名称
     * @param type              组织条目的类型（组织或组织单元）
     */
    public OrganizationUnit(String distinguishedName, String name, Type type) {
        this();
        this.distinguishedName = distinguishedName;
        this.name = name;
        this.type = type;
    }

    /**
     * 设置子组织单位列表。
     *
     * @param children 要设置的子列表
     */
    public void setChildren(List<OrganizationUnit> children) {
        this.children = children != null ? children : new ArrayList<>();
    }

    /**
     * 将一个子组织单元添加到此节点。
     * 此方法还会设置子节点的父引用和层级。
     *
     * @param child 要添加的子组织单元
     */
    public void addChild(OrganizationUnit child) {
        if (child != null) {
            this.children.add(child);
            child.setParent(this);
            child.setLevel(this.level + 1);
        }
    }

    /**
     * 从此节点移除子组织单元。
     *
     * @param child 要移除的子组织单元。
     * @return 如果子单元被移除，则返回true；否则返回false。
     */
    public boolean removeChild(OrganizationUnit child) {
        if (child != null && this.children.remove(child)) {
            child.setParent(null);
            child.setLevel(0);
            return true;
        }
        return false;
    }

    /**
     * 检查此组织单元是否包含子节点。
     *
     * @return 如果此节点包含子节点，则返回 true；否则返回 false。
     */
    public boolean hasChildren() {
        return !children.isEmpty();
    }

    /**
     * 检查此组织单元是否是根节点（没有父节点）。
     *
     * @return 如果这是根节点，则返回 true，否则返回 false
     */
    public boolean isRoot() {
        return parent == null;
    }

    /**
     * 检查此组织单元是否为叶节点（没有子节点）。
     *
     * @return 如果这是叶节点，则为 true，否则为 false
     */
    public boolean isLeaf() {
        return children.isEmpty();
    }

    /**
     * 获取此组织单位的直接子级数量。
     *
     * @return 直接子级数量
     */
    public int getChildCount() {
        return children.size();
    }

    /**
     * 获取此组织单元的后代（包括子级、孙级等）总数。
     *
     * @return 后代总数
     */
    public int getDescendantCount() {
        int count = children.size();
        for (OrganizationUnit child : children) {
            count += child.getDescendantCount();
        }
        return count;
    }

    /**
     * 获取从根到此组织单元的路径，该路径以名称列表形式表示。
     *
     * @return 路径，表示为组织单元名称列表
     */
    public List<String> getPath() {
        List<String> path = new ArrayList<>();
        OrganizationUnit current = this;
        while (current != null) {
            path.add(0, current.getName()); //添加到开头以保持顺序
            current = current.getParent();
        }
        return path;
    }

    /**
     * 获取从根到此组织单位的路径字符串。
     *
     * @param separator 用于路径元素之间的分隔符
     * @return 路径字符串
     */
    public String getPathString(String separator) {
        return String.join(separator, getPath());
    }

    /**
     * 获取从根目录到此组织单元的路径，使用“/”作为分隔符。
     *
     * @return 路径字符串，使用“/”作为分隔符
     */
    public String getPathString() {
        return getPathString("/");
    }


    @Override
    public String toString() {
        return "OrganizationUnit{" +
                "distinguishedName='" + distinguishedName + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", type=" + type +
                ", parentDn='" + parentDn + '\'' +
                ", level=" + level +
                ", childCount=" + children.size() +
                ", path='" + getPathString() + '\'' +
                '}';
    }

    /**
     * 返回此组织单位及其所有后代的树状表示。
     *
     * @return 树结构的字符串表示
     */
    public String toTreeString() {
        return toTreeString("", true);
    }

    /**
     * 创建树形字符串表示的辅助方法。
     *
     * @param prefix 当前行的前缀
     * @param isLast 这是否是其层级的最后一个子节点
     * @return 树形字符串表示
     */
    private String toTreeString(String prefix, boolean isLast) {
        StringBuilder sb = new StringBuilder();
        sb.append(prefix);
        sb.append(isLast ? "└── " : "├── ");
        sb.append(name).append(" (").append(type).append(", Level: ").append(level).append(")");
        sb.append("\n");

        for (int i = 0; i < children.size(); i++) {
            OrganizationUnit child = children.get(i);
            boolean isLastChild = (i == children.size() - 1);
            String childPrefix = prefix + (isLast ? "    " : "│   ");
            sb.append(child.toTreeString(childPrefix, isLastChild));
        }

        return sb.toString();
    }
}
