package org.jeecg.modules.ldap.config;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * LDAP 属性映射关系
 */
@Getter
@Setter
@Builder
public class LdapAttributeMapping {

    /**
     * User 映射关系
     */
    @Builder.Default
    private UserAttributes user = UserAttributes.builder().build();

    /**
     * Group 映射关系
     */
    @Builder.Default
    private GroupAttributes group = GroupAttributes.builder().build();

    /**
     * Organization/OU 映射关系
     */
    @Builder.Default
    private OrganizationAttributes organization = OrganizationAttributes.builder().build();

    /**
     * Object class 映射关系
     */
    @Builder.Default
    private ObjectClasses objectClasses = ObjectClasses.builder().build();


    /**
     * User 映射关系
     */
    @Getter
    @Setter
    @Builder
    public static class UserAttributes {
        @Builder.Default
        private String username = "uid";
        @Builder.Default
        private String alternativeUsername = "sAMAccountName";
        @Builder.Default
        private String distinguishedName = "distinguishedName";
        @Builder.Default
        private String alternativeDistinguishedName = "entryDN";
        @Builder.Default
        private String commonName = "cn";
        @Builder.Default
        private String surname = "sn";
        @Builder.Default
        private String givenName = "givenName";
        @Builder.Default
        private String displayName = "displayName";
        @Builder.Default
        private String mail = "mail";
        @Builder.Default
        private String telephoneNumber = "telephoneNumber";
        @Builder.Default
        private String company = "company";
        @Builder.Default
        private String description = "description";
        @Builder.Default
        private String title = "title";
        @Builder.Default
        private String department = "department";
        @Builder.Default
        private String alternativeDepartment = "departmentNumber";
        @Builder.Default
        private String organizationalUnit = "ou";
        @Builder.Default
        private String memberOf = "memberOf";
        @Builder.Default
        private String employeeNumber = "employeeNumber";
        @Builder.Default
        private String employeeType = "employeeType";
        @Builder.Default
        private String manager = "manager";
        @Builder.Default
        private String userAccountControl = "userAccountControl";
    }

    /**
     * Group 映射关系
     */
    @Getter
    @Setter
    @Builder
    public static class GroupAttributes {
        @Builder.Default
        private String distinguishedName = "distinguishedName";
        @Builder.Default
        private String alternativeDistinguishedName = "entryDN";
        @Builder.Default
        private String commonName = "cn";
        @Builder.Default
        private String name = "name";
        @Builder.Default
        private String description = "description";
        @Builder.Default
        private String member = "member";
        @Builder.Default
        private String uniqueMember = "uniqueMember";
        @Builder.Default
        private String memberUid = "memberUid";
    }

    /**
     * Organization/OU 映射关系
     */
    @Getter
    @Setter
    @Builder
    public static class OrganizationAttributes {
        @Builder.Default
        private String distinguishedName = "distinguishedName";
        @Builder.Default
        private String alternativeDistinguishedName = "entryDN";
        @Builder.Default
        private String organization = "o";
        @Builder.Default
        private String organizationalUnit = "ou";
        @Builder.Default
        private String description = "description";
    }

    /**
     * Object class 映射关系
     */
    @Getter
    @Setter
    @Builder
    public static class ObjectClasses {
        @Builder.Default
        private String person = "person";
        @Builder.Default
        private String inetOrgPerson = "inetOrgPerson";
        @Builder.Default
        private String user = "user";
        @Builder.Default
        private String organization = "organization";
        @Builder.Default
        private String organizationalUnit = "organizationalUnit";
        @Builder.Default
        private String group = "group";
        @Builder.Default
        private String groupOfNames = "groupOfNames";
        @Builder.Default
        private String groupOfUniqueNames = "groupOfUniqueNames";
        @Builder.Default
        private String posixGroup = "posixGroup";
    }
}
