package org.jeecg.modules.ldap.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jeecg.modules.ldap.config.LdapConfigurationManager;
import org.jeecg.modules.ldap.exception.LdapSynchronizationException;
import org.jeecg.modules.ldap.model.LdapGroup;
import org.jeecg.modules.ldap.model.LdapUser;
import org.jeecg.modules.ldap.model.OrganizationUnit;
import org.jeecg.modules.ldap.util.LdapRepositoryManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ldap.core.support.LdapContextSource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用于支持组的LDAP用户操作的服务类。
 *
 * <p>此服务提供从LDAP目录检索用户的业务逻辑，具有全面的组支持，包括嵌套组解析。它处理
 * 各种组类型并确保唯一的用户合并。</p>
 */
@Slf4j
@Service
public class LdapUserService {

    private final LdapRepositoryManager repositoryManager;

    private final LdapConfigurationManager ldapConfigurationManager;

    @Autowired
    public LdapUserService(LdapRepositoryManager repositoryManager, LdapConfigurationManager ldapConfigurationManager) {
        this.repositoryManager = repositoryManager;
        this.ldapConfigurationManager = ldapConfigurationManager;
    }

    /**
     * 使用提供的LDAP上下文源配置服务。
     * 此方法必须在任何服务操作之前调用。
     *
     * @param contextSource 要使用的LDAP上下文源
     * @throws IllegalArgumentException 如果contextSource为null
     */
    public void configureLdapConnection(LdapContextSource contextSource) {
        repositoryManager.configureRepositories(contextSource);
    }

    /**
     * 从特定组织单位获取所有用户，支持组功能。
     *
     * <p>此方法接收一个组织单位作为输入，并返回该组织结构内的所有用户。如果该组织单位包含组，
     * 它会递归地从这些组中检索用户，并处理嵌套组场景。</p>
     *
     * @param organizationalUnit 要搜索的组织单位
     * @param userFilter         可选的 LDAP 用户过滤条件（可为 null）
     * @return 整合的唯一用户列表
     * @throws LdapSynchronizationException 如果操作失败
     * @throws IllegalArgumentException     如果组织单位为 null
     */
    public List<LdapUser> getUsersFromOrganizationalUnit(OrganizationUnit organizationalUnit, String userFilter) {
        if (organizationalUnit == null) {
            throw new IllegalArgumentException("Organizational unit cannot be null");
        }

        String ouDn = organizationalUnit.getDistinguishedName();
        log.debug("Retrieving users from organizational unit: {} ({})",
                  organizationalUnit.getName(), ouDn);

        try {
            repositoryManager.validateRepositoryConfiguration();

            Set<String> processedGroups = new HashSet<>();

            // 步骤1：从组织单位获取直接用户
            List<LdapUser> directUsers = repositoryManager.getUserRepository().findUsersWithFilter(ouDn, userFilter);
            Set<LdapUser> allUsers = new LinkedHashSet<>(directUsers);
            log.debug("Found {} direct users in OU: {}", directUsers.size(), ouDn);

            // 第二步：从组织单位获取组
            List<LdapGroup> groups = repositoryManager.getGroupRepository().findAllGroups(ouDn);
            log.debug("Found {} groups in OU: {}", groups.size(), ouDn);

            // 步骤3：递归处理组及其嵌套组
            for (LdapGroup group : groups) {
                Set<LdapUser> groupUsers = getUsersFromGroupRecursively(group, processedGroups, userFilter);
                allUsers.addAll(groupUsers);
            }

            List<LdapUser> result = new ArrayList<>(allUsers);
            log.debug("Successfully retrieved {} unique users from organizational unit: {}",
                      result.size(), ouDn);

            return result;

        } catch (Exception e) {
            log.error("Failed to retrieve users from organizational unit: {}", ouDn, e);
            throw new LdapSynchronizationException(
                    "Failed to retrieve users from organizational unit: " + ouDn, e);
        }
    }

    /**
     * 从指定组中检索用户，支持嵌套组。
     *
     * @param groupDn    组的专有名称（Distinguished Name）
     * @param userFilter 用于用户过滤的可选LDAP过滤器（可为null）
     * @return 来自该组及其嵌套组的用户列表
     * @throws LdapSynchronizationException 如果操作失败
     * @throws IllegalArgumentException     如果groupDn为null或为空
     */
    public List<LdapUser> getUsersFromGroup(String groupDn, String userFilter) {
        if (!StringUtils.hasText(groupDn)) {
            throw new IllegalArgumentException("Group DN cannot be null or empty");
        }

        log.debug("Retrieving users from group: {}", groupDn);

        try {
            repositoryManager.validateRepositoryConfiguration();

            LdapGroup group = repositoryManager.getGroupRepository().findByDistinguishedName(groupDn);
            if (group == null) {
                throw new LdapSynchronizationException("Group not found: " + groupDn);
            }

            Set<String> processedGroups = new HashSet<>();
            Set<LdapUser> allUsers = getUsersFromGroupRecursively(group, processedGroups, userFilter);

            List<LdapUser> result = new ArrayList<>(allUsers);
            log.debug("Successfully retrieved {} unique users from group: {}", result.size(), groupDn);

            return result;

        } catch (Exception e) {
            log.error("Failed to retrieve users from group: {}", groupDn, e);
            throw new LdapSynchronizationException("Failed to retrieve users from group: " + groupDn, e);
        }
    }

    /**
     * 从一个组及其嵌套组中递归地检索用户。
     *
     * @param group           要处理的组
     * @param processedGroups 已处理的组 DN 集合，以避免无限循环
     * @param userFilter      可选的用户过滤器
     * @return 来自组层级的唯一用户集合
     */
    private Set<LdapUser> getUsersFromGroupRecursively(LdapGroup group, Set<String> processedGroups,
                                                       String userFilter) {
        Set<LdapUser> users = new LinkedHashSet<>();
        String groupDn = group.getDistinguishedName();

        // 防止循环组引用引起的无限循环
        if (processedGroups.contains(groupDn)) {
            log.debug("Group already processed, skipping: {}", groupDn);
            return users;
        }

        processedGroups.add(groupDn);
        log.debug("Processing group: {} (type: {})", groupDn, group.getGroupType());

        try {
            // 第一步：获取群组中的直接用户成员
            List<String> members = group.getMembers();
            for (String memberDn : members) {
                try {
                    List<LdapUser> findUsers = repositoryManager.getUserRepository().findUsersWithFilter(memberDn,
                                                                                                         userFilter);
                    if (CollectionUtils.isNotEmpty(findUsers)) {
                        if (findUsers.size() > 1) {
                            log.warn("Found more than one user entry for user {} in group {}: {}",
                                     memberDn, groupDn, findUsers);
                        }
                        users.add(findUsers.get(0));
                        log.debug("Added user from group {}: {}", groupDn, findUsers.get(0).getUsername());
                    }
                } catch (Exception e) {
                    log.debug("Failed to retrieve user {}, might not be a user entry: {}", memberDn, e.getMessage());
                }
            }

            // 步骤2：处理嵌套组
            List<String> nestedGroups = group.getNestedGroups();
            for (String nestedGroupDn : nestedGroups) {
                try {
                    LdapGroup nestedGroup =
                            repositoryManager.getGroupRepository().findByDistinguishedName(nestedGroupDn);
                    if (nestedGroup != null) {
                        Set<LdapUser> nestedUsers = getUsersFromGroupRecursively(nestedGroup, processedGroups,
                                                                                 userFilter);
                        users.addAll(nestedUsers);
                        log.debug("Added {} users from nested group: {}", nestedUsers.size(), nestedGroupDn);
                    }
                } catch (Exception e) {
                    log.warn("Failed to process nested group {}: {}", nestedGroupDn, e.getMessage());
                }
            }

        } catch (Exception e) {
            log.error("Error processing group {}: {}", groupDn, e.getMessage(), e);
        }

        log.debug("Retrieved {} users from group: {}", users.size(), groupDn);
        return users;
    }

    /**
     * 根据用户名查找用户，支持可选的过滤条件。
     *
     * @param username 要搜索的用户名
     * @param baseDn   搜索的基准DN（可为null，表示全局搜索）
     * @return 匹配用户名的用户列表
     * @throws LdapSynchronizationException 如果操作失败
     * @throws IllegalArgumentException     如果用户名为空或为null
     */
    public LdapUser findUsersByUsername(String username, String baseDn) {
        if (!StringUtils.hasText(username)) {
            throw new IllegalArgumentException("Username cannot be null or empty");
        }

        log.debug("Finding users by username: {} in base DN: {}", username, baseDn);

        try {
            repositoryManager.validateRepositoryConfiguration();

            String searchBase = StringUtils.hasText(baseDn) ? baseDn : "";

            String usernameAttrKey = ldapConfigurationManager.getAttributeMapping().getUser().getUsername();
            String filter = String.format("(%s=%s)", usernameAttrKey, username);
            List<LdapUser> users = repositoryManager.getUserRepository().findUsersWithFilter(searchBase, filter);
            if (CollectionUtils.isNotEmpty(users)) {
                if (users.size() > 1) {
                    log.warn("用户[{}]检索结果不唯一，使用第一个匹配项作为检索结果", username);
                }
                return users.get(0);
            }
        } catch (Exception e) {
            log.error("Failed to find users by username: {}", username, e);
            throw new LdapSynchronizationException("Failed to find users by username: " + username, e);
        }
        return null;
    }

    /**
     * 获取指定组织单元的用户统计信息。
     *
     * @param organizationalUnit 要分析的组织单元
     * @return 格式化的统计信息字符串
     * @throws LdapSynchronizationException 如果操作失败
     * @throws IllegalArgumentException     如果 organizationalUnit 为空
     */
    public String getUserStatistics(OrganizationUnit organizationalUnit) {
        if (organizationalUnit == null) {
            throw new IllegalArgumentException("Organizational unit cannot be null");
        }

        log.debug("Generating user statistics for OU: {}", organizationalUnit.getDistinguishedName());

        try {
            List<LdapUser> allUsers = getUsersFromOrganizationalUnit(organizationalUnit, null);

            long activeUsers = allUsers.stream().filter(LdapUser::isActive).count();
            long inactiveUsers = allUsers.size() - activeUsers;

            Map<String, Long> departmentCounts = allUsers.stream()
                    .filter(user -> StringUtils.hasText(user.getDepartment()))
                    .collect(Collectors.groupingBy(LdapUser::getDepartment, Collectors.counting()));

            String statistics = String.format(
                    "User Statistics for OU '%s':\n" +
                            "- Total users: %d\n" +
                            "- Active users: %d\n" +
                            "- Inactive users: %d\n" +
                            "- Departments: %s",
                    organizationalUnit.getName(), allUsers.size(), activeUsers, inactiveUsers,
                    departmentCounts.isEmpty() ? "None" : departmentCounts.toString()
            );

            log.debug("Generated user statistics for OU: {}", organizationalUnit.getDistinguishedName());
            return statistics;

        } catch (Exception e) {
            log.error("Failed to generate user statistics for OU: {}",
                      organizationalUnit.getDistinguishedName(), e);
            throw new LdapSynchronizationException(
                    "Failed to generate user statistics for OU: " + organizationalUnit.getDistinguishedName(), e);
        }
    }
}
