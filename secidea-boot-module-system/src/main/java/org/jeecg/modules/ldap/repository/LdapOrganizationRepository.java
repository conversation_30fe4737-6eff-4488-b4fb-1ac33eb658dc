package org.jeecg.modules.ldap.repository;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.ldap.exception.LdapSynchronizationException;
import org.jeecg.modules.ldap.model.OrganizationUnit;
import org.jeecg.modules.ldap.util.LdapAttributeHelper;
import org.jeecg.modules.ldap.util.LdapDnUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ldap.core.AttributesMapper;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.filter.*;
import org.springframework.stereotype.Repository;

import javax.naming.NamingException;
import javax.naming.directory.Attributes;
import java.util.List;

/**
 * 用于LDAP组织和组织单元操作的存储库类。
 *
 * <p>此存储库提供数据访问方法，用于从LDAP目录中检索组织（o）和组织单元（ou）条目。它处理LDAP属性和OrganizationUnit对象之间的映射。</p>
 */
@Repository
@Slf4j
public class LdapOrganizationRepository {

    private LdapTemplate ldapTemplate;
    private final LdapAttributeHelper attributeHelper;

    /**
     * 构造函数，仅用于配置组件的依赖注入。
     * LdapTemplate 将通过 setLdapTemplate 方法动态设置。
     *
     * @param attributeHelper LDAP 属性辅助器
     */
    @Autowired
    public LdapOrganizationRepository(LdapAttributeHelper attributeHelper) {
        this.attributeHelper = attributeHelper;
    }

    /**
     * 设置用于目录操作的 LDAP 模板。
     * 此方法应在任何仓库操作之前调用。
     *
     * @param ldapTemplate 用于操作的 LDAP 模板
     * @throws IllegalArgumentException 如果 ldapTemplate 为 null
     */
    public void setLdapTemplate(LdapTemplate ldapTemplate) {
        if (ldapTemplate == null) {
            throw new IllegalArgumentException("LdapTemplate cannot be null");
        }
        this.ldapTemplate = ldapTemplate;
    }

    /**
     * 获取当前LDAP模板。
     *
     * @return 当前LDAP模板
     * @throws IllegalStateException 如果LDAP模板尚未设置
     */
    public LdapTemplate getLdapTemplate() {
        if (this.ldapTemplate == null) {
            throw new IllegalStateException("LdapTemplate has not been set. Call setLdapTemplate() first.");
        }
        return this.ldapTemplate;
    }

    /**
     * 查找指定基本DN下的所有组织单位。
     *
     * <p>此方法在给定基本DN下搜索组织（o）和组织单位（ou）条目。
     * 它返回一个统一的组织单位（OrganizationUnit）对象列表，并进行适当的类型区分。</p>
     *
     * @param baseDn 要搜索的基本区分名
     * @return 找到的组织单位列表
     * @throws LdapSynchronizationException 如果搜索操作失败
     */
    public List<OrganizationUnit> findAllOrganizationalUnits(String baseDn) {
        try {
            log.debug("Searching for organizational units under base DN: {}", baseDn);

            // 创建针对组织和组织单位条目的筛选器
            OrFilter filter = new OrFilter();
            filter.or(new EqualsFilter("objectClass", "organization"));
            filter.or(new EqualsFilter("objectClass", "organizationalUnit"));

            LdapTemplate template = getLdapTemplate();
            String relativePath = LdapDnUtils.getSubPathFromDN(baseDn, template);
            List<OrganizationUnit> results = template.search(
                    relativePath,
                    filter.encode(),
                    attributeHelper.getSearchControls(),
                    new OrganizationUnitAttributesMapper()
            );

            log.debug("Found {} organizational units under base DN: {}", results.size(), baseDn);
            return results;

        } catch (Exception e) {
            log.error("Failed to search for organizational units under base DN: {}", baseDn, e);
            throw new LdapSynchronizationException(
                    "Failed to retrieve organizational units from base DN: " + baseDn, e);
        }
    }

    /**
     * 使用自定义过滤器查找组织单元。
     *
     * <p>此方法允许使用自定义 LDAP 过滤器表达式进行更精确的搜索。该过滤器将与基本组织单元过滤器结合使用，以确保只返回相关的条目。</p>
     *
     * @param baseDn       要在其下进行搜索的基本可分辨名称
     * @param customFilter 自定义 LDAP 过滤器表达式
     * @return 与过滤器匹配的组织单元列表
     * @throws LdapSynchronizationException 如果搜索操作失败
     */
    public List<OrganizationUnit> findOrganizationalUnitsWithFilter(String baseDn, String customFilter) {
        try {
            log.debug("Searching for organizational units under base DN: {} with filter: {}", baseDn, customFilter);

            // 创建组织条目的基础过滤器
            OrFilter baseFilter = new OrFilter();
            baseFilter.or(new EqualsFilter("objectClass", "organization"));
            baseFilter.or(new EqualsFilter("objectClass", "organizationalUnit"));

            // 与自定义过滤器结合（如果提供）
            Filter finalFilter;
            if (customFilter != null && !customFilter.trim().isEmpty()) {
                AndFilter combinedFilter = new AndFilter();
                combinedFilter.and(baseFilter);
                combinedFilter.and(new HardcodedFilter(customFilter));
                finalFilter = combinedFilter;
            } else {
                finalFilter = baseFilter;
            }

            LdapTemplate template = getLdapTemplate();
            String relativePath = LdapDnUtils.getSubPathFromDN(baseDn, template);
            List<OrganizationUnit> results = template.search(
                    relativePath,
                    finalFilter.encode(),
                    attributeHelper.getSearchControls(),
                    new OrganizationUnitAttributesMapper()
            );

            log.debug("Found {} organizational units under base DN: {} with filter: {}",
                      results.size(), baseDn, customFilter);
            return results;

        } catch (Exception e) {
            log.error("Failed to search for organizational units under base DN: {} with filter: {}",
                      baseDn, customFilter, e);
            throw new LdapSynchronizationException(
                    "Failed to retrieve organizational units from base DN: " + baseDn +
                            " with filter: " + customFilter, e);
        }
    }

    /**
     * 根据识别名查找特定的组织单元。
     *
     * @param dn 组织单元的识别名
     * @return 如果找到则返回组织单元，否则返回 null
     * @throws LdapSynchronizationException 如果查找操作失败
     */
    public OrganizationUnit findByDistinguishedName(String dn) {
        try {
            log.debug("Looking up organizational unit by DN: {}", dn);

            LdapTemplate template = getLdapTemplate();
            return template.lookup(dn, new String[]{"*", "+"}, new OrganizationUnitAttributesMapper());

        } catch (Exception e) {
            log.error("Failed to lookup organizational unit by DN: {}", dn, e);
            throw new LdapSynchronizationException(
                    "Failed to lookup organizational unit by DN: " + dn, e);
        }
    }

    /**
     * 用于将 LDAP 属性映射到组织单元对象的 AttributesMapper 实现。
     */
    private class OrganizationUnitAttributesMapper implements AttributesMapper<OrganizationUnit> {

        @Override
        public OrganizationUnit mapFromAttributes(Attributes attributes) throws NamingException {
            OrganizationUnit ou = new OrganizationUnit();

            String dn = attributeHelper.getDistinguishedName(attributes, "organization");
            ou.setDistinguishedName(dn);

            // 根据对象类确定类型并命名
            if (attributes.get("objectClass") != null) {
                String objectClass = attributes.get("objectClass").get().toString().toLowerCase();
                if (objectClass.contains("organization") && !objectClass.contains("organizationalunit")) {
                    ou.setType(OrganizationUnit.Type.ORGANIZATION);
                    // 对于组织条目，使用已配置的组织属性
                    ou.setName(attributeHelper.getOrganizationName(attributes));
                } else {
                    ou.setType(OrganizationUnit.Type.ORGANIZATIONAL_UNIT);
                    // 对于组织单位条目，请使用已配置的OU属性
                    ou.setName(attributeHelper.getOrganizationalUnitName(attributes));
                }
            }

            ou.setDescription(attributeHelper.getDescription(attributes));

            // 从可分辨名称中提取父级DN
            if (dn != null && dn.contains(",")) {
                String parentDn = dn.substring(dn.indexOf(",") + 1);
                ou.setParentDn(parentDn);
            }

            return ou;
        }
    }
}
