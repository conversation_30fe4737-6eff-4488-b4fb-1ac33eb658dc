package org.jeecg.modules.ldap.util;

import lombok.experimental.UtilityClass;
import org.jeecg.common.util.StringUtils;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.core.support.LdapContextSource;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@UtilityClass
public class LdapDnUtils {

    /**
     * 匹配DN中的o(或ou)和名称
     */
    private static final Pattern ouPattern = Pattern.compile("^(?i)(o|ou)=([^,]+)");

    /**
     * 匹配DN,忽略大小写 如（cn=test,ou=ou1,dc=test,dc=xxx,dc=com）或（CN=test,OU=ou1,DC=test,DC=xxx,DC=com）
     */
    public static final Pattern dnPattern = Pattern.compile("(?i)cn=");

    /**
     * 获取用户DN, 若配置DN不为DN格式，拼接为AD域UPN形式
     *
     * @param inputName 用户填入的登录名
     * @param baseDN    baseDN
     * @return 管理员DN
     */
    public static String extractAdminLoginName(String inputName, String baseDN) {
        if (isDnFormat(inputName)) {
            // 如果管理员DN属性为DN格式，则直接使用其作为登录DN
            return inputName;
        }
        // 不为DN格式，兼容AD域的UPN方式登录
        return extractAdUPNName(inputName, baseDN);
    }

    public static String extractAdUPNName(String inputName, String baseDN) {
        StringBuilder suffix = new StringBuilder();
        String[] mainName = baseDN.split(",");
        for (String main : mainName) {
            String[] key = main.split("=");
            if (key.length == 2 && ("dc".equalsIgnoreCase(key[0]))) {
                suffix.append(key[1]).append('.');
            }
        }
        if (suffix.lastIndexOf(".") != -1) {
            suffix = new StringBuilder(suffix.substring(0, suffix.lastIndexOf(".")));
        }
        return inputName + '@' + suffix;
    }

    public static boolean isDnFormat(String dn) {
        return dnPattern.matcher(dn).find();
    }

    /**
     * 返回 base相对于fullBaseDN的ou路径，如： fullBaseDN: ou=root,ad=secidea,ad=com base: ou=test,ou=root
     * 则方法返回ou=test 当base中不以fullBaseDN中的ou结尾时，返回空，该情况为选择ldapBaseDN中的上级路径
     *
     * @param fullBaseDN fullBaseDN
     * @param base       选择OU到顶层OU的路径
     * @return 去除fullBaseDN中包含ou后的base路径
     */
    public static String relativePath(String fullBaseDN, String base) {
        // fullBaseDN中已经包含的ou
        List<String> domainNameList = Arrays.asList(fullBaseDN.split(","));
        String domainNameOU = domainNameList.stream()
                .filter(item -> ouPattern.matcher(item).matches()).collect(
                        Collectors.joining(",")).toLowerCase();
        // 移除base中已经包含在fullBaseDN中的ou
        if (base.endsWith(domainNameOU)) {
            int index = base.lastIndexOf(domainNameOU);
            base = base.substring(0, index);
            if (base.endsWith(",")) {
                base = base.substring(0, base.length() - 1);
            }
        } else {
            base = "";
        }
        return base;
    }

    /**
     * 从给定的 baseDn 和 LdapTemplate 中解析出相对于 LDAP 基础路径的子路径。
     *
     * @param baseDn   完整的 DN 路径
     * @param template LdapTemplate 对象，用于获取基础 LDAP 名称
     * @return 返回从 baseDn 中解析出的子路径。如果 baseDn 等于基础路径，则返回空字符串；
     * 如果 baseDn 不以基础路径结尾，则原样返回 baseDn。
     */
    public static String getSubPathFromDN(String baseDn, LdapTemplate template) {
        String base = ((LdapContextSource) template.getContextSource()).getBaseLdapName().toString();
        if (StringUtils.isNotBlank(baseDn) && StringUtils.isNotBlank(base) && baseDn.endsWith(base)) {
            if (baseDn.length() > base.length()) {
                String prefix = baseDn.substring(0, baseDn.length() - base.length());
                // 移除末尾的逗号
                baseDn = prefix.endsWith(",") ? prefix.substring(0, prefix.length() - 1) : prefix;
            } else if (baseDn.equals(base)) {
                baseDn = "";
            }
        }
        return baseDn;
    }
}
