package org.jeecg.modules.ldap.util;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.ldap.model.OrganizationUnit;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用于从扁平的组织单元（OrganizationUnit）列表中构建树形结构的实用类。
 *
 * <p>此类提供方法，根据LDAP组织单元的判别名（distinguished names）及其父子关系，
 * 从扁平列表中构建分层树形结构。</p>
 */
@Slf4j
@Component
public class OrganizationTreeBuilder {

    /**
     * 从扁平的组织单位列表中构建树形结构。
     *
     * <p>本方法分析组织单位的专有名称，以确定父子关系并构建一个层级树。</p>
     *
     * @param flatList 扁平的组织单位列表
     * @return 包含其完整子树的根组织单位列表
     */
    public List<OrganizationUnit> buildTree(List<OrganizationUnit> flatList) {
        if (flatList == null || flatList.isEmpty()) {
            return new ArrayList<>();
        }

        log.debug("Building tree from {} organizational units", flatList.size());

        // 创建一个用于按DN快速查找的映射。
        Map<String, OrganizationUnit> dnMap = flatList.stream()
                .collect(Collectors.toMap(
                        OrganizationUnit::getDistinguishedName,
                        ou -> ou,
                        (existing, replacement) -> existing // Keep existing if duplicate
                ));

        // 清除现有树形关系
        flatList.forEach(ou -> {
            ou.setParent(null);
            ou.getChildren().clear();
            ou.setLevel(0);
        });

        // 建立父子关系
        for (OrganizationUnit ou : flatList) {
            String parentDn = determineParentDn(ou.getDistinguishedName());
            if (StringUtils.hasText(parentDn)) {
                OrganizationUnit parent = dnMap.get(parentDn);
                if (parent != null) {
                    parent.addChild(ou);
                    log.trace("Added {} as child of {}", ou.getName(), parent.getName());
                }
            }
        }

        // 查找并返回根节点
        List<OrganizationUnit> roots = flatList.stream()
                .filter(OrganizationUnit::isRoot)
                .sorted(Comparator.comparing(OrganizationUnit::getName, Comparator.nullsLast(String::compareTo)))
                .collect(Collectors.toList());

        log.debug("Built tree with {} root nodes", roots.size());
        return roots;
    }

    /**
     * 构建树形结构并将其作为单个根节点返回。
     * 如果存在多个根节点，它们将被放置在一个虚拟根节点下。
     *
     * @param flatList        组织单元的扁平列表
     * @param virtualRootName 如果存在多个根节点，则为虚拟根节点的名称
     * @return 一个包含完整树的单个根组织单元
     */
    public OrganizationUnit buildSingleRootTree(List<OrganizationUnit> flatList, String virtualRootName) {
        List<OrganizationUnit> roots = buildTree(flatList);

        if (roots.isEmpty()) {
            return new OrganizationUnit("virtual://root", virtualRootName, OrganizationUnit.Type.ORGANIZATION);
        }

        if (roots.size() == 1) {
            return roots.get(0);
        }

        // 为多个根创建虚拟根
        OrganizationUnit virtualRoot = new OrganizationUnit("virtual://root", virtualRootName,
                                                            OrganizationUnit.Type.ORGANIZATION);
        for (OrganizationUnit root : roots) {
            virtualRoot.addChild(root);
        }

        return virtualRoot;
    }

    /**
     * 根据给定的专有名称（distinguished name）确定其父级专有名称。
     *
     * @param dn 专有名称
     * @return 父级专有名称，如果无法确定父级则返回 null
     */
    private String determineParentDn(String dn) {
        if (!StringUtils.hasText(dn)) {
            return null;
        }

        int commaIndex = dn.indexOf(',');
        if (commaIndex > 0 && commaIndex < dn.length() - 1) {
            return dn.substring(commaIndex + 1).trim();
        }

        return null;
    }

    /**
     * 验证树结构的一致性
     *
     * @param roots 待验证的根节点列表
     * @return 包含发现的任何问题的验证结果
     */
    public TreeValidationResult validateTree(List<OrganizationUnit> roots) {
        TreeValidationResult result = new TreeValidationResult();
        Set<String> visitedDns = new HashSet<>();

        for (OrganizationUnit root : roots) {
            validateNode(root, visitedDns, result);
        }

        return result;
    }

    /**
     * 递归验证单个节点及其后代。
     *
     * @param node       要验证的节点
     * @param visitedDns 已访问DNs的集合，用于检测循环
     * @param result     要更新的验证结果
     */
    private void validateNode(OrganizationUnit node, Set<String> visitedDns, TreeValidationResult result) {
        if (node == null) {
            result.addError("Found null node in tree");
            return;
        }

        String dn = node.getDistinguishedName();
        if (!StringUtils.hasText(dn)) {
            result.addError("Node has empty distinguished name: " + node.getName());
            return;
        }

        if (visitedDns.contains(dn)) {
            result.addError("Circular reference detected for DN: " + dn);
            return;
        }

        visitedDns.add(dn);

        // 验证父子一致性
        for (OrganizationUnit child : node.getChildren()) {
            if (child.getParent() != node) {
                result.addError("Parent-child relationship inconsistency for: " + child.getDistinguishedName());
            }
            if (child.getLevel() != node.getLevel() + 1) {
                result.addError("Level inconsistency for: " + child.getDistinguishedName() +
                                        " (expected: " + (node.getLevel() + 1) + ", actual: " + child.getLevel() + ")");
            }
            validateNode(child, visitedDns, result);
        }

        visitedDns.remove(dn);
    }

    /**
     * 将树结构扁平化回列表。
     *
     * @param roots 根节点的列表
     * @return 包含树中所有节点的扁平列表
     */
    public List<OrganizationUnit> flattenTree(List<OrganizationUnit> roots) {
        List<OrganizationUnit> flatList = new ArrayList<>();
        for (OrganizationUnit root : roots) {
            flattenNode(root, flatList);
        }
        return flatList;
    }

    /**
     * 递归地展平单个节点及其后代。
     *
     * @param node     要展平的节点
     * @param flatList 要添加节点的列表
     */
    private void flattenNode(OrganizationUnit node, List<OrganizationUnit> flatList) {
        if (node != null) {
            flatList.add(node);
            for (OrganizationUnit child : node.getChildren()) {
                flattenNode(child, flatList);
            }
        }
    }

    /**
     * 根据其可分辨名称在树中查找节点。
     *
     * @param roots 要搜索的根节点列表
     * @param dn    要查找的可分辨名称
     * @return 具有指定DN的组织单元，如果未找到则为null
     */
    public OrganizationUnit findByDn(List<OrganizationUnit> roots, String dn) {
        if (!StringUtils.hasText(dn)) {
            return null;
        }

        for (OrganizationUnit root : roots) {
            OrganizationUnit found = findByDnInSubtree(root, dn);
            if (found != null) {
                return found;
            }
        }
        return null;
    }

    /**
     * 在子树中递归地按DN搜索节点。
     *
     * @param node 要搜索的子树的根
     * @param dn   要查找的专有名称
     * @return 具有指定DN的组织单元，如果未找到则为null
     */
    private OrganizationUnit findByDnInSubtree(OrganizationUnit node, String dn) {
        if (node == null) {
            return null;
        }

        if (dn.equals(node.getDistinguishedName())) {
            return node;
        }

        for (OrganizationUnit child : node.getChildren()) {
            OrganizationUnit found = findByDnInSubtree(child, dn);
            if (found != null) {
                return found;
            }
        }

        return null;
    }

    /**
     * 从树中获取所有叶节点（没有子节点的节点）。
     *
     * @param roots 根节点列表
     * @return 所有叶节点的列表
     */
    public List<OrganizationUnit> getLeafNodes(List<OrganizationUnit> roots) {
        List<OrganizationUnit> leafNodes = new ArrayList<>();
        for (OrganizationUnit root : roots) {
            collectLeafNodes(root, leafNodes);
        }
        return leafNodes;
    }

    /**
     * 递归地从子树中收集叶节点。
     *
     * @param node      子树的根节点
     * @param leafNodes 用于添加叶节点的列表
     */
    private void collectLeafNodes(OrganizationUnit node, List<OrganizationUnit> leafNodes) {
        if (node == null) {
            return;
        }

        if (node.isLeaf()) {
            leafNodes.add(node);
        } else {
            for (OrganizationUnit child : node.getChildren()) {
                collectLeafNodes(child, leafNodes);
            }
        }
    }

    /**
     * 用于树验证操作的结果类
     */
    public static class TreeValidationResult {
        private final List<String> errors = new ArrayList<>();
        private final List<String> warnings = new ArrayList<>();

        public void addError(String error) {
            errors.add(error);
        }

        public void addWarning(String warning) {
            warnings.add(warning);
        }

        public List<String> getErrors() {
            return new ArrayList<>(errors);
        }

        public List<String> getWarnings() {
            return new ArrayList<>(warnings);
        }

        public boolean hasErrors() {
            return !errors.isEmpty();
        }

        public boolean hasWarnings() {
            return !warnings.isEmpty();
        }

        public boolean isValid() {
            return !hasErrors();
        }

        @Override
        public String toString() {
            return "TreeValidationResult{" +
                    "errors=" + errors.size() +
                    ", warnings=" + warnings.size() +
                    ", valid=" + isValid() +
                    "}";
        }
    }
}
