package org.jeecg.modules.ldap.repository;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.StringUtils;
import org.jeecg.modules.ldap.exception.LdapSynchronizationException;
import org.jeecg.modules.ldap.model.LdapGroup;
import org.jeecg.modules.ldap.util.LdapAttributeHelper;
import org.jeecg.modules.ldap.util.LdapDnUtils;
import org.jeecg.modules.ldap.util.LdapObjectTypeDetector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ldap.core.AttributesMapper;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.filter.*;
import org.springframework.stereotype.Repository;

import javax.naming.NamingEnumeration;
import javax.naming.NamingException;
import javax.naming.directory.Attribute;
import javax.naming.directory.Attributes;
import java.util.ArrayList;
import java.util.List;

/**
 * 用于LDAP组操作的存储库类。
 *
 * <p>此存储库提供用于从LDAP目录检索组条目的数据访问方法。它处理各种组类型，包括groupOfUniqueNames，
 * 并支持嵌套组关系。</p>
 */
@Repository
@Slf4j
public class LdapGroupRepository {

    private LdapTemplate ldapTemplate;

    private final LdapAttributeHelper attributeHelper;

    @Autowired
    public LdapGroupRepository(LdapAttributeHelper attributeHelper) {
        this.attributeHelper = attributeHelper;
    }

    /**
     * 设置用于目录操作的 LDAP 模板。
     * 此方法应在任何仓库操作之前调用。
     *
     * @param ldapTemplate 用于操作的 LDAP 模板
     * @throws IllegalArgumentException 如果 ldapTemplate 为 null
     */
    public void setLdapTemplate(LdapTemplate ldapTemplate) {
        if (ldapTemplate == null) {
            throw new IllegalArgumentException("LdapTemplate cannot be null");
        }
        this.ldapTemplate = ldapTemplate;
    }

    /**
     * 获取当前LDAP模板。
     *
     * @return 当前LDAP模板
     * @throws IllegalStateException 如果LDAP模板尚未设置
     */
    public LdapTemplate getLdapTemplate() {
        if (this.ldapTemplate == null) {
            throw new IllegalStateException("LdapTemplate has not been set. Call setLdapTemplate() first.");
        }
        return this.ldapTemplate;
    }

    /**
     * 查找指定基本可分辨名称（base DN）下的所有组。
     *
     * <p>此方法搜索各种类型的组条目，包括 group、groupOfNames、groupOfUniqueNames 和 posixGroup。</p>
     *
     * @param baseDn 用于进行搜索的基本可分辨名称
     * @return 找到的组列表
     * @throws LdapSynchronizationException 如果搜索操作失败
     */
    public List<LdapGroup> findAllGroups(String baseDn) {
        try {
            log.debug("Searching for groups under base DN: {}", baseDn);

            LdapTemplate template = getLdapTemplate();

            OrFilter filter = new OrFilter();
            filter.or(new EqualsFilter("objectClass", "group"));
            filter.or(new EqualsFilter("objectClass", "groupOfNames"));
            filter.or(new EqualsFilter("objectClass", "groupOfUniqueNames"));
            filter.or(new EqualsFilter("objectClass", "posixGroup"));

            String relativePath = LdapDnUtils.getSubPathFromDN(baseDn, template);
            List<LdapGroup> results = template.search(
                    relativePath,
                    filter.encode(),
                    attributeHelper.getSearchControls(),
                    new GroupAttributesMapper(template)
            );

            log.debug("Found {} groups under base DN: {}", results.size(), baseDn);
            return results;

        } catch (Exception e) {
            log.error("Failed to search for groups under base DN: {}", baseDn, e);
            throw new LdapSynchronizationException(
                    "Failed to retrieve groups from base DN: " + baseDn, e);
        }
    }

    /**
     * 查找使用自定义过滤器的组。
     *
     * @param baseDn       要在其下搜索的基本专有名称
     * @param customFilter 自定义LDAP过滤器表达式
     * @return 匹配过滤器的组列表
     * @throws LdapSynchronizationException 如果搜索操作失败
     */
    public List<LdapGroup> findGroupsWithFilter(String baseDn, String customFilter) {
        try {
            log.debug("Searching for groups under base DN: {} with filter: {}", baseDn, customFilter);

            // 创建组条目基础过滤器
            OrFilter baseFilter = new OrFilter();
            baseFilter.or(new EqualsFilter("objectClass", "group"));
            baseFilter.or(new EqualsFilter("objectClass", "groupOfNames"));
            baseFilter.or(new EqualsFilter("objectClass", "groupOfUniqueNames"));
            baseFilter.or(new EqualsFilter("objectClass", "posixGroup"));

            // 与自定义过滤器结合（如果提供）
            Filter finalFilter;
            if (customFilter != null && !customFilter.trim().isEmpty()) {
                AndFilter combinedFilter = new AndFilter();
                combinedFilter.and(baseFilter);
                combinedFilter.and(new HardcodedFilter(customFilter));
                finalFilter = combinedFilter;
            } else {
                finalFilter = baseFilter;
            }
            LdapTemplate template = getLdapTemplate();

            String relativePath = LdapDnUtils.getSubPathFromDN(baseDn, template);
            List<LdapGroup> results = template.search(
                    relativePath,
                    finalFilter.encode(),
                    attributeHelper.getSearchControls(),
                    new GroupAttributesMapper(template)
            );

            log.debug("Found {} groups under base DN: {} with filter: {}",
                      results.size(), baseDn, customFilter);
            return results;

        } catch (Exception e) {
            log.error("Failed to search for groups under base DN: {} with filter: {}",
                      baseDn, customFilter, e);
            throw new LdapSynchronizationException(
                    "Failed to retrieve groups from base DN: " + baseDn +
                            " with filter: " + customFilter, e);
        }
    }

    /**
     * 根据可分辨名称查找特定组。
     *
     * @param dn 组的可分辨名称
     * @return 如果找到则返回组，否则返回 null
     * @throws LdapSynchronizationException 如果查找操作失败
     */
    public LdapGroup findByDistinguishedName(String dn) {
        try {
            log.debug("Looking up group by DN: {}", dn);
            LdapTemplate template = getLdapTemplate();
            return template.lookup(dn, new String[]{"*", "+"}, new GroupAttributesMapper(template));

        } catch (Exception e) {
            log.error("Failed to lookup group by DN: {}", dn, e);
            throw new LdapSynchronizationException(
                    "Failed to lookup group by DN: " + dn, e);
        }
    }

    /**
     * 查找包含指定成员的组。
     *
     * @param memberDn 成员的专有名称
     * @return 包含该成员的组列表
     * @throws LdapSynchronizationException 如果搜索操作失败
     */
    public List<LdapGroup> findGroupsByMember(String memberDn) {
        try {
            log.debug("Searching for groups containing member: {}", memberDn);

            // 为包含指定成员的群组创建筛选器
            OrFilter memberFilter = new OrFilter();
            memberFilter.or(new EqualsFilter("member", memberDn));
            memberFilter.or(new EqualsFilter("uniqueMember", memberDn));
            memberFilter.or(new EqualsFilter("memberUid", extractUidFromDn(memberDn)));

            // 结合群组类型过滤器
            OrFilter groupTypeFilter = new OrFilter();
            groupTypeFilter.or(new EqualsFilter("objectClass", "group"));
            groupTypeFilter.or(new EqualsFilter("objectClass", "groupOfNames"));
            groupTypeFilter.or(new EqualsFilter("objectClass", "groupOfUniqueNames"));
            groupTypeFilter.or(new EqualsFilter("objectClass", "posixGroup"));

            AndFilter combinedFilter = new AndFilter();
            combinedFilter.and(groupTypeFilter);
            combinedFilter.and(memberFilter);

            // Ensure LDAP template is set
            LdapTemplate template = getLdapTemplate();
            List<LdapGroup> results = template.search(
                    "",
                    combinedFilter.encode(),
                    attributeHelper.getSearchControls(),
                    new GroupAttributesMapper(template)
            );

            log.debug("Found {} groups containing member: {}", results.size(), memberDn);
            return results;

        } catch (Exception e) {
            log.error("Failed to search for groups containing member: {}", memberDn, e);
            throw new LdapSynchronizationException(
                    "Failed to retrieve groups containing member: " + memberDn, e);
        }
    }

    /**
     * 从区分名中提取UID，用于posixGroup搜索。
     *
     * @param dn 区分名
     * @return UID值；如果无法提取UID，则返回完整的区分名。
     */
    private String extractUidFromDn(String dn) {
        if (dn != null && dn.toLowerCase().contains("uid=")) {
            String[] parts = dn.split(",");
            for (String part : parts) {
                if (part.trim().toLowerCase().startsWith("uid=")) {
                    return part.trim().substring(4);
                }
            }
        }
        return dn;
    }

    /**
     * AttributesMapper 实现，用于将 LDAP 属性映射到 LdapGroup 对象。
     */
    private class GroupAttributesMapper implements AttributesMapper<LdapGroup> {

        private final LdapTemplate ldapTemplate;

        public GroupAttributesMapper(LdapTemplate ldapTemplate) {
            this.ldapTemplate = ldapTemplate;
        }

        @Override
        public LdapGroup mapFromAttributes(Attributes attributes) throws NamingException {
            LdapGroup group = new LdapGroup();

            String dn = attributeHelper.getDistinguishedName(attributes, "organization");
            group.setDistinguishedName(dn);

            // 设置名称（尝试多个常用属性）
            String groupName = attributeHelper.getGroupName(attributes);
            group.setName(groupName);

            String description = attributeHelper.getDescription(attributes);
            if (description != null) {
                group.setDescription(attributes.get("description").get().toString());
            }

            // 根据对象类确定组类型
            if (attributes.get("objectClass") != null) {
                Attribute objectClassAttr = attributes.get("objectClass");
                NamingEnumeration<?> objectClasses = objectClassAttr.getAll();
                group.setGroupType(getGroupTypeFromObjectClasses(objectClasses));
            }

            // 根据组类型设置成员
            List<String> members = new ArrayList<>();
            List<String> nestedGroups = new ArrayList<>();

            // 根据群组类型处理不同的成员属性
            if (group.getGroupType() == LdapGroup.GroupType.GROUP_OF_UNIQUE_NAMES) {
                addMembersFromAttribute(attributes, "uniqueMember", members, nestedGroups);
            } else if (group.getGroupType() == LdapGroup.GroupType.GROUP_OF_NAMES) {
                addMembersFromAttribute(attributes, "member", members, nestedGroups);
            } else if (group.getGroupType() == LdapGroup.GroupType.POSIX_GROUP) {
                addMembersFromAttribute(attributes, "memberUid", members, nestedGroups);
            } else {
                // 对于通用组，同时尝试 member 和 uniqueMember。
                addMembersFromAttribute(attributes, "member", members, nestedGroups);
                addMembersFromAttribute(attributes, "uniqueMember", members, nestedGroups);
            }

            group.setMembers(members);
            group.setNestedGroups(nestedGroups);

            // 从 DN 设置组织单位
            if (dn != null && dn.contains(",")) {
                String parentDn = dn.substring(dn.indexOf(",") + 1);
                group.setOrganizationalUnit(parentDn);
            }

            return group;
        }

        private LdapGroup.GroupType getGroupTypeFromObjectClasses(NamingEnumeration<?> objectClasses) throws NamingException {
            while (objectClasses.hasMore()) {
                String objectClass = objectClasses.next().toString().toLowerCase();
                switch (objectClass) {
                    case "groupofuniquenames":
                        return LdapGroup.GroupType.GROUP_OF_UNIQUE_NAMES;
                    case "groupofnames":
                        return LdapGroup.GroupType.GROUP_OF_NAMES;
                    case "posixgroup":
                        return LdapGroup.GroupType.POSIX_GROUP;
                    case "group":
                        return LdapGroup.GroupType.GROUP;
                }
            }
            return LdapGroup.GroupType.GROUP;
        }

        private void addMembersFromAttribute(Attributes attributes, String attributeName,
                                             List<String> members, List<String> nestedGroups)
                throws NamingException {
            if (attributes.get(attributeName) != null) {
                Attribute memberAttr = attributes.get(attributeName);
                NamingEnumeration<?> memberValues = memberAttr.getAll();
                while (memberValues.hasMore()) {
                    String memberDn = memberValues.next().toString();
                    // 区分用户与组内成员
                    if (isGroupDn(memberDn)) {
                        nestedGroups.add(memberDn);
                    } else {
                        members.add(memberDn);
                    }
                }
            }
        }

        private boolean isGroupDn(String dn) {
            return StringUtils.isNotBlank(dn) && LdapObjectTypeDetector.INSTANCE.isGroup(ldapTemplate, dn);
        }
    }
}
