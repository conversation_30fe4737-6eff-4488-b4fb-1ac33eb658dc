package org.jeecg.modules.ldap.repository;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.StringUtils;
import org.jeecg.modules.ldap.config.LdapAttributeMapping;
import org.jeecg.modules.ldap.config.LdapConfigurationManager;
import org.jeecg.modules.ldap.exception.LdapSynchronizationException;
import org.jeecg.modules.ldap.model.LdapUser;
import org.jeecg.modules.ldap.util.LdapAttributeHelper;
import org.jeecg.modules.ldap.util.LdapDnUtils;
import org.jeecg.modules.system.entity.Person;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ldap.control.PagedResultsDirContextProcessor;
import org.springframework.ldap.core.AttributesMapper;
import org.springframework.ldap.core.LdapOperations;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.core.support.LdapContextSource;
import org.springframework.ldap.core.support.LdapOperationsCallback;
import org.springframework.ldap.core.support.SingleContextSource;
import org.springframework.ldap.filter.*;
import org.springframework.stereotype.Repository;

import javax.naming.NamingEnumeration;
import javax.naming.NamingException;
import javax.naming.directory.Attribute;
import javax.naming.directory.Attributes;
import javax.naming.directory.SearchControls;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * 用于LDAP用户操作的仓库类。
 *
 * <p>此仓库提供从LDAP目录检索用户条目的数据访问方法。它处理LDAP属性与LdapUser对象之间的映射，包括组员信息。</p>
 */
@Repository
@Slf4j
public class LdapUserRepository {

    private LdapTemplate ldapTemplate;
    private final LdapConfigurationManager configManager;
    private final LdapAttributeHelper attributeHelper;

    /**
     * 构造函数，用于配置组件的依赖注入。
     *
     * @param configManager   LDAP 配置管理器
     * @param attributeHelper LDAP 属性辅助器
     */
    @Autowired
    public LdapUserRepository(LdapConfigurationManager configManager,
                              LdapAttributeHelper attributeHelper) {
        this.configManager = configManager;
        this.attributeHelper = attributeHelper;
    }

    /**
     * 设置用于目录操作的 LDAP 模板。
     * 此方法应在任何仓库操作之前调用。
     *
     * @param ldapTemplate 用于操作的 LDAP 模板
     * @throws IllegalArgumentException 如果 ldapTemplate 为 null
     */
    public void setLdapTemplate(LdapTemplate ldapTemplate) {
        if (ldapTemplate == null) {
            throw new IllegalArgumentException("LdapTemplate cannot be null");
        }
        this.ldapTemplate = ldapTemplate;
    }

    /**
     * 获取当前LDAP模板。
     *
     * @return 当前LDAP模板
     * @throws IllegalStateException 如果LDAP模板尚未设置
     */
    public LdapTemplate getLdapTemplate() {
        if (this.ldapTemplate == null) {
            throw new IllegalStateException("LdapTemplate has not been set. Call setLdapTemplate() first.");
        }
        return this.ldapTemplate;
    }

    /**
     * 使用自定义过滤器查找用户。
     *
     * <p>此方法允许使用自定义 LDAP 过滤器表达式进行更具体的查找。该过滤器将与基本用户过滤器结合使用。</p>
     *
     * @param baseDn       用于搜索的基准识别名
     * @param customFilter 自定义 LDAP 过滤器表达式
     * @return 匹配该过滤器的用户列表
     * @throws LdapSynchronizationException 如果搜索操作失败
     */
    public List<LdapUser> findUsersWithFilter(String baseDn, String customFilter) {
        try {
            log.debug("Searching for users under base DN: {} with filter: {}", baseDn, customFilter);

            LdapTemplate template = getLdapTemplate();

            // 创建用于用户条目的基础过滤器，使用配置的对象类。
            Filter baseFilter = createUserObjectClassFilter();

            // 与自定义过滤器结合（如果提供）
            Filter finalFilter;
            if (customFilter != null && !customFilter.trim().isEmpty()) {
                AndFilter combinedFilter = new AndFilter();
                combinedFilter.and(baseFilter);
                combinedFilter.and(new HardcodedFilter(customFilter));
                finalFilter = combinedFilter;
            } else {
                finalFilter = baseFilter;
            }
            String relativePath = LdapDnUtils.getSubPathFromDN(baseDn, template);
            PagedResultsDirContextProcessor processor = new PagedResultsDirContextProcessor(500);
            // 检索忽略 PartialResultException 异常，避免当检索AD域而未指定OU时检索失败
            // 参考 https://stackoverflow.com/questions/16412236/how-to-resolve-javax-naming-partialresultexception
            List<LdapUser> users = SingleContextSource.doWithSingleContext(
                    template.getContextSource(), operations -> {
                        List<LdapUser> allResults = new LinkedList<>();
                        do {
                            List<LdapUser> result = template.search(
                                    relativePath,
                                    finalFilter.encode(),
                                    attributeHelper.getSearchControls(),
                                    new UserAttributesMapper(),
                                    processor
                            );
                            allResults.addAll(result);
                        } while (processor.hasMore());

                        return allResults;
                    }, false, true, true);

            log.debug("Found {} users under base DN: {} with filter: {}",
                      users.size(), baseDn, customFilter);
            return users;

        } catch (Exception e) {
            log.error("Failed to search for users under base DN: {} with filter: {}",
                      baseDn, customFilter, e);
            throw new LdapSynchronizationException(
                    "Failed to retrieve users from base DN: " + baseDn +
                            " with filter: " + customFilter, e);
        }
    }

    /**
     * 根据用户所属的组查找用户。
     *
     * @param groupDn 组的区分名
     * @return 属于指定组的用户列表
     * @throws LdapSynchronizationException 如果搜索操作失败
     */
    public List<LdapUser> findUsersByGroupMembership(String groupDn) {
        try {
            log.debug("Searching for users by group membership: {}", groupDn);

            LdapTemplate template = getLdapTemplate();

            AndFilter filter = new AndFilter();
            filter.and(createUserObjectClassFilter());

            LdapAttributeMapping.UserAttributes userAttrs = configManager.getAttributeMapping().getUser();
            filter.and(new EqualsFilter(userAttrs.getMemberOf(), groupDn));

            List<LdapUser> results = template.search(
                    "",
                    filter.encode(),
                    attributeHelper.getSearchControls(),
                    new UserAttributesMapper()
            );

            log.debug("Found {} users in group: {}", results.size(), groupDn);
            return results;

        } catch (Exception e) {
            log.error("Failed to search for users by group membership: {}", groupDn, e);
            throw new LdapSynchronizationException(
                    "Failed to retrieve users by group membership: " + groupDn, e);
        }
    }


    /**
     * 基于已配置的 LDAP 服务器类型，为用户对象类创建一个过滤器。
     *
     * @return 用户对象类过滤器
     */
    private Filter createUserObjectClassFilter() {
        LdapAttributeMapping.ObjectClasses objClasses = configManager.getAttributeMapping().getObjectClasses();

        OrFilter filter = new OrFilter();
        filter.or(new EqualsFilter("objectClass", objClasses.getPerson()));
        filter.or(new EqualsFilter("objectClass", objClasses.getInetOrgPerson()));

        // 对于 Active Directory，也包括“user”对象类
        if (configManager.isActiveDirectory()) {
            filter.or(new EqualsFilter("objectClass", objClasses.getUser()));
        }

        return filter;
    }

    /**
     * AttributesMapper 实现，用于将 LDAP 属性映射到 LdapUser 对象。
     */
    private class UserAttributesMapper implements AttributesMapper<LdapUser> {

        @Override
        public LdapUser mapFromAttributes(Attributes attributes) throws NamingException {
            LdapUser user = new LdapUser();

            user.setDistinguishedName(attributeHelper.getDistinguishedName(attributes, "user"));
            user.setUsername(attributeHelper.getUsername(attributes));
            LdapAttributeMapping.UserAttributes userAttrs = configManager.getAttributeMapping().getUser();
            user.setFirstName(attributeHelper.getStringAttribute(attributes, userAttrs.getGivenName(), null));
            user.setLastName(attributeHelper.getStringAttribute(attributes, userAttrs.getSurname(), null));
            user.setFullName(attributeHelper.getFullName(attributes));
            user.setEmail(attributeHelper.getEmail(attributes));
            user.setOrganizationalUnit(attributeHelper.getStringAttribute(attributes,
                                                                          userAttrs.getOrganizationalUnit(), null));
            user.setDepartment(attributeHelper.getDepartment(attributes));
            user.setTitle(attributeHelper.getTitle(attributes));
            user.setTelephoneNumber(attributeHelper.getTelephoneNumber(attributes));
            user.setCompany(attributeHelper.getCompany(attributes));
            user.setDescription(attributeHelper.getUserDescription(attributes));

            List<String> groupMemberships = new ArrayList<>();
            if (attributes.get(userAttrs.getMemberOf()) != null) {
                Attribute memberOfAttr = attributes.get(userAttrs.getMemberOf());
                NamingEnumeration<?> memberOfValues = memberOfAttr.getAll();
                while (memberOfValues.hasMore()) {
                    groupMemberships.add(memberOfValues.next().toString());
                }
            }
            user.setGroupMemberships(groupMemberships);
            user.setActive(attributeHelper.isUserAccountActive(attributes));
            return user;
        }
    }
}
