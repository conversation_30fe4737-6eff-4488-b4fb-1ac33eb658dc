package org.jeecg.modules.ldap.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 代表LDAP用户的数据传输对象。
 *
 * <p>此类封装了LDAP目录中用户条目的核心属性，包括个人信息、组织详情和组员身份。</p>
 */
@Getter
@Setter
public class LdapUser {

    private String distinguishedName;
    private String username;
    private String firstName;
    private String lastName;
    private String fullName;
    private String commonName;
    private String email;
    private String organizationalUnit;
    private String department;
    private String title;
    private String telephoneNumber;
    private String company;
    private String description;
    private List<String> groupMemberships;
    private boolean active;

    /**
     * 默认构造函数。
     */
    public LdapUser() {
        this.groupMemberships = new ArrayList<>();
        this.active = true;
    }

    /**
     * 带有必要参数的构造函数。
     *
     * @param distinguishedName 用户的完整可分辨名称
     * @param username          用户的用户名
     * @param email             用户的电子邮件地址
     */
    public LdapUser(String distinguishedName, String username, String email) {
        this();
        this.distinguishedName = distinguishedName;
        this.username = username;
        this.email = email;
    }

    /**
     * 设置用户的组成员资格列表。
     *
     * @param groupMemberships 要设置的组专有名称列表
     */
    public void setGroupMemberships(List<String> groupMemberships) {
        this.groupMemberships = groupMemberships != null ? groupMemberships : new ArrayList<>();
    }

    /**
     * 为用户添加群组成员身份。
     *
     * @param groupDn 要添加的群组的专有名称（distinguished name）
     */
    public void addGroupMembership(String groupDn) {
        if (groupDn != null && !this.groupMemberships.contains(groupDn)) {
            this.groupMemberships.add(groupDn);
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        LdapUser ldapUser = (LdapUser) o;
        return Objects.equals(distinguishedName, ldapUser.distinguishedName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(distinguishedName);
    }

    @Override
    public String toString() {
        return "LdapUser{" +
                "distinguishedName='" + distinguishedName + '\'' +
                ", username='" + username + '\'' +
                ", firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", fullName='" + fullName + '\'' +
                ", email='" + email + '\'' +
                ", organizationalUnit='" + organizationalUnit + '\'' +
                ", department='" + department + '\'' +
                ", title='" + title + '\'' +
                ", telephoneNumber='" + telephoneNumber + '\'' +
                ", groupMemberships=" + groupMemberships +
                ", active=" + active +
                '}';
    }
}
