package org.jeecg.modules.ldap.util;

import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ldap.core.LdapTemplate;

import javax.naming.NamingEnumeration;
import javax.naming.directory.Attribute;
import javax.naming.directory.Attributes;
import java.util.HashSet;
import java.util.Set;

/**
 * 用于基于objectClass属性检测LDAP对象类型的工具类
 */
@Slf4j
public class LdapObjectTypeDetector {

    public static final LdapObjectTypeDetector INSTANCE = new LdapObjectTypeDetector();

    // 通用组对象类
    private static final Set<String> GROUP_OBJECT_CLASSES = Sets.newHashSet(
            "groupofuniquenames",
            "groupofnames",
            "group",
            "posixgroup",
            "organizationalrole",
            "groupofentries",
            "dynamicgroup"
    );

    // 通用用户对象类
    private static final Set<String> USER_OBJECT_CLASSES = Sets.newHashSet(
            "person",
            "inetorgperson",
            "organizationalperson",
            "user",
            "posixaccount",
            "account",
            "simpleaccount"
    );

    // 常见的组织单元对象类
    private static final Set<String> OU_OBJECT_CLASSES = Sets.newHashSet(
            "organizationalunit",
            "organization",
            "domain",
            "dcobject",
            "locality",
            "organizationalrole"
    );

    /**
     * 判断给定的 DN 是否表示一个组对象。
     *
     * @param ldapTemplate 用于查询的 LDAP 模板
     * @param dn           要检查的专有名称
     * @return 如果对象是组，则返回 true，否则返回 false
     */
    public boolean isGroup(LdapTemplate ldapTemplate, String dn) {
        return hasObjectClass(ldapTemplate, dn, GROUP_OBJECT_CLASSES);
    }

    /**
     * 确定给定的DN是否代表用户对象。
     *
     * @param ldapTemplate 用于查询的LDAP模板
     * @param dn           要检查的专有名称
     * @return 如果对象是用户，则为true；否则为false
     */
    public boolean isUser(LdapTemplate ldapTemplate, String dn) {
        return hasObjectClass(ldapTemplate, dn, USER_OBJECT_CLASSES);
    }

    /**
     * 判断给定DN是否代表一个组织单元。
     *
     * @param ldapTemplate 用于查询的LDAP模板
     * @param dn           要检查的专有名称
     * @return 如果对象是组织单元，则返回true，否则返回false
     */
    public boolean isOrganizationalUnit(LdapTemplate ldapTemplate, String dn) {
        return hasObjectClass(ldapTemplate, dn, OU_OBJECT_CLASSES);
    }

    /**
     * 获取给定 DN 的所有对象类。
     *
     * @param ldapTemplate 用于查询的 LDAP 模板
     * @param dn           要检查的专有名称 (distinguished name)
     * @return 一组对象类名称（小写）
     */
    public Set<String> getObjectClasses(LdapTemplate ldapTemplate, String dn) {
        Set<String> objectClasses = new HashSet<>();

        if (dn == null || dn.trim().isEmpty()) {
            return objectClasses;
        }

        try {
            log.debug("Retrieving object classes for DN: {}", dn);

            Attributes attributes = ldapTemplate.lookup(dn, new String[]{"objectClass"},
                                                        (Attributes attrs) -> attrs);

            if (attributes != null) {
                Attribute objectClassAttr = attributes.get("objectClass");
                if (objectClassAttr != null) {
                    NamingEnumeration<?> values = objectClassAttr.getAll();
                    while (values.hasMore()) {
                        String objectClass = values.next().toString().toLowerCase();
                        objectClasses.add(objectClass);
                    }
                }
            }

            log.debug("Found object classes for DN {}: {}", dn, objectClasses);

        } catch (Exception e) {
            log.warn("Failed to retrieve object classes for DN {}: {}", dn, e.getMessage());
        }

        return objectClasses;
    }

    /**
     * 检查对象是否具有任何指定对象类。
     *
     * @param ldapTemplate        用于查询的LDAP模板
     * @param dn                  要检查的专有名称
     * @param targetObjectClasses 要检查的对象类集合
     * @return 如果对象具有任何目标对象类，则返回 true
     */
    private boolean hasObjectClass(LdapTemplate ldapTemplate, String dn, Set<String> targetObjectClasses) {
        if (dn == null || dn.trim().isEmpty()) {
            return false;
        }

        try {
            Set<String> objectClasses = getObjectClasses(ldapTemplate, dn);

            for (String objectClass : objectClasses) {
                if (targetObjectClasses.contains(objectClass)) {
                    log.debug("DN {} identified as target type with objectClass: {}", dn, objectClass);
                    return true;
                }
            }

            return false;

        } catch (Exception e) {
            log.warn("Failed to check object class for DN {}: {}", dn, e.getMessage());
            return false;
        }
    }

    /**
     * 确定 LDAP 对象的主要类型。
     *
     * @param ldapTemplate 用于查询的 LDAP 模板
     * @param dn           要检查的专有名称
     * @return 检测到的对象类型
     */
    public LdapObjectType detectObjectType(LdapTemplate ldapTemplate, String dn) {
        if (dn == null || dn.trim().isEmpty()) {
            return LdapObjectType.UNKNOWN;
        }

        Set<String> objectClasses = getObjectClasses(ldapTemplate, dn);

        // 按具体性顺序检查
        if (objectClasses.stream().anyMatch(GROUP_OBJECT_CLASSES::contains)) {
            return LdapObjectType.GROUP;
        }

        if (objectClasses.stream().anyMatch(USER_OBJECT_CLASSES::contains)) {
            return LdapObjectType.USER;
        }

        if (objectClasses.stream().anyMatch(OU_OBJECT_CLASSES::contains)) {
            return LdapObjectType.ORGANIZATIONAL_UNIT;
        }

        return LdapObjectType.UNKNOWN;
    }

    /**
     * LDAP 对象类型枚举。
     */
    public enum LdapObjectType {
        USER,
        GROUP,
        ORGANIZATIONAL_UNIT,
        UNKNOWN
    }
}
