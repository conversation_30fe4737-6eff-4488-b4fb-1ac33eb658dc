package org.jeecg.modules.ldap.util;

import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.core.support.LdapContextSource;

/**
 * 用于创建具有动态配置的 LdapTemplate 实例的工厂类。
 *
 * <p>此工厂提供从 LdapContextSource 对象创建 LdapTemplate 实例的方法，
 * 允许在运行时配置 LDAP 连接，而无需依赖 Spring 依赖注入。</p>
 */
public class LdapTemplateFactory {

    /**
     * 根据提供的 LdapContextSource 创建一个 LdapTemplate 实例。
     *
     * @param contextSource 要使用的 LDAP 上下文源
     * @return 配置好的 LdapTemplate 实例
     * @throws IllegalArgumentException 如果 contextSource 为 null
     */
    public static LdapTemplate createLdapTemplate(LdapContextSource contextSource) {
        if (contextSource == null) {
            throw new IllegalArgumentException("LdapContextSource cannot be null");
        }

        LdapTemplate ldapTemplate = new LdapTemplate(contextSource);

        ldapTemplate.setDefaultCountLimit(1000);

        ldapTemplate.setDefaultTimeLimit(30000);

        ldapTemplate.setIgnorePartialResultException(true);

        return ldapTemplate;
    }

    /**
     * 创建具有自定义配置的 LdapTemplate 实例。
     *
     * @param contextSource        要使用的 LDAP 上下文源
     * @param countLimit           搜索操作中返回的最大条目数
     * @param timeLimit            搜索操作的时间限制（毫秒）
     * @param ignorePartialResults 是否忽略部分结果异常
     * @return 配置好的 LdapTemplate 实例
     * @throws IllegalArgumentException 如果 contextSource 为 null 或限制无效
     */
    public static LdapTemplate createLdapTemplate(LdapContextSource contextSource,
                                                  int countLimit,
                                                  int timeLimit,
                                                  boolean ignorePartialResults) {
        if (contextSource == null) {
            throw new IllegalArgumentException("LdapContextSource cannot be null");
        }
        if (countLimit <= 0) {
            throw new IllegalArgumentException("Count limit must be positive");
        }
        if (timeLimit <= 0) {
            throw new IllegalArgumentException("Time limit must be positive");
        }

        LdapTemplate ldapTemplate = new LdapTemplate(contextSource);

        ldapTemplate.setDefaultCountLimit(countLimit);
        ldapTemplate.setDefaultTimeLimit(timeLimit);
        ldapTemplate.setIgnorePartialResultException(ignorePartialResults);

        return ldapTemplate;
    }

    /**
     * 创建一个配置最简单的基本 LdapTemplate 实例。
     *
     * <p>此方法创建一个具有基本设置的 LdapTemplate，适用于简单的 LDAP 操作或不需要自定义配置的情况。</p>
     *
     * @param contextSource 要使用的 LDAP 上下文源
     * @return 基本的 LdapTemplate 实例
     * @throws IllegalArgumentException 如果 contextSource 为 null
     */
    public static LdapTemplate createBasicLdapTemplate(LdapContextSource contextSource) {
        if (contextSource == null) {
            throw new IllegalArgumentException("LdapContextSource cannot be null");
        }

        return new LdapTemplate(contextSource);
    }
}
