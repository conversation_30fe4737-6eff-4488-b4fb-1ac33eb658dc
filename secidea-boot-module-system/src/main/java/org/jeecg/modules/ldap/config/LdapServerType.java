package org.jeecg.modules.ldap.config;

/**
 * 支持的 LDAP 服务器类型枚举。
 *
 * <p>本枚举定义了应用程序可以使用的不同类型的 LDAP 服务器，每种类型都具有不同的属性命名约定和模式。</p>
 */
public enum LdapServerType {

    /**
     * AD域
     */
    ACTIVE_DIRECTORY("MS ActiveDirectory"),

    /**
     * OpenLDAP
     */
    OPENLDAP("OpenLDAP"),

    /**
     * 通用 LDAP 服务器 (自定义配置)
     */
    GENERIC("Generic");

    public final String displayName;

    LdapServerType(String displayName) {
        this.displayName = displayName;
    }

    /**
     * 从字符串值获取LDAP服务器类型。
     *
     * @param value 字符串值
     * @return 对应的LdapServerType，如果未找到则返回GENERIC
     */
    public static LdapServerType fromString(String value) {
        if (value == null) {
            return GENERIC;
        }

        for (LdapServerType type : values()) {
            if (type.name().equalsIgnoreCase(value) ||
                    type.displayName.equalsIgnoreCase(value)) {
                return type;
            }
        }

        return GENERIC;
    }
}
