package org.jeecg.modules.ldap.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 表示 LDAP group的数据传输对象。
 *
 * <p>此类封装了 LDAP 目录中组条目的基本属性，包括组信息、成员列表和嵌套组关系。
 * 它支持各种组类型，包括 groupOfUniqueNames。</p>
 */
@Getter
@EqualsAndHashCode
@ToString
public class LdapGroup {

    /**
     * 表示 LDAP 组类型的枚举。
     */
    public enum GroupType {
        /**
         * 标准组
         */
        GROUP,
        /**
         * 唯一名称组 (OpenLDAP)
         */
        GROUP_OF_UNIQUE_NAMES,
        /**
         * name 组
         */
        GROUP_OF_NAMES,
        /**
         * Posix 组
         */
        POSIX_GROUP
    }

    @Setter
    private String distinguishedName;
    @Setter
    private String name;
    @Setter
    private String description;
    @Setter
    private GroupType groupType;
    /**
     * 成员专有名称列表
     */
    private List<String> members;
    /**
     * 嵌套组的专有名称列表
     */
    private List<String> nestedGroups;
    @Setter
    private String organizationalUnit;

    /**
     * 默认构造函数。
     */
    public LdapGroup() {
        this.members = new ArrayList<>();
        this.nestedGroups = new ArrayList<>();
        this.groupType = GroupType.GROUP;
    }

    /**
     * 包含基本参数的构造函数。
     *
     * @param distinguishedName 组的完整可分辨名称
     * @param name              组的名称
     * @param groupType         组的类型
     */
    public LdapGroup(String distinguishedName, String name, GroupType groupType) {
        this();
        this.distinguishedName = distinguishedName;
        this.name = name;
        this.groupType = groupType;
    }


    /**
     * 设置组成员列表。
     *
     * @param members 要设置的成员可分辨名称列表
     */
    public void setMembers(List<String> members) {
        this.members = members != null ? members : new ArrayList<>();
    }

    /**
     * 将成员添加到组中。
     *
     * @param memberDn 要添加的成员的专有名称
     */
    public void addMember(String memberDn) {
        if (memberDn != null && !this.members.contains(memberDn)) {
            this.members.add(memberDn);
        }
    }

    /**
     * 设置嵌套组列表。
     *
     * @param nestedGroups 要设置的嵌套组的专有名称列表
     */
    public void setNestedGroups(List<String> nestedGroups) {
        this.nestedGroups = nestedGroups != null ? nestedGroups : new ArrayList<>();
    }

    /**
     * 将嵌套组添加到此组。
     *
     * @param groupDn 要添加的嵌套组的专有名称
     */
    public void addNestedGroup(String groupDn) {
        if (groupDn != null && !this.nestedGroups.contains(groupDn)) {
            this.nestedGroups.add(groupDn);
        }
    }

    /**
     * 检查该组是否包含嵌套组。
     *
     * @return 如果该组包含嵌套组，则返回 true；否则返回 false
     */
    public boolean hasNestedGroups() {
        return nestedGroups != null && !nestedGroups.isEmpty();
    }

    /**
     * 获取直接成员的总数。
     *
     * @return 直接成员的数量
     */
    public int getMemberCount() {
        return members != null ? members.size() : 0;
    }
}
