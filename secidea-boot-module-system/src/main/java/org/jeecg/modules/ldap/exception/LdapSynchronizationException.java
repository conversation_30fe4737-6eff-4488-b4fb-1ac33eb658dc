package org.jeecg.modules.ldap.exception;

/**
 * 用于 LDAP 同步操作的自定义异常。
 *
 * <p>当 LDAP 同步操作遇到无法恢复的错误时（例如连接失败、认证错误或数据不一致），会抛出此异常。</p>
 */
public class LdapSynchronizationException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 使用指定的详细信息构造一个新的 LDAP 同步异常。
     *
     * @param message 解释异常原因的详细信息
     */
    public LdapSynchronizationException(String message) {
        super(message);
    }

    /**
     * 构造一个新的 LDAP 同步异常，并指定详细消息和原因。
     *
     * @param message 解释异常原因的详细消息
     * @param cause   导致异常的底层原因
     */
    public LdapSynchronizationException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造一个新的 LDAP 同步异常，并指定其原因。
     *
     * @param cause 异常的根本原因
     */
    public LdapSynchronizationException(Throwable cause) {
        super(cause);
    }
}
