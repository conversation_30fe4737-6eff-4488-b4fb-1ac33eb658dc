package org.jeecg.modules.ldap.util;

import lombok.Getter;
import org.jeecg.modules.ldap.repository.LdapGroupRepository;
import org.jeecg.modules.ldap.repository.LdapOrganizationRepository;
import org.jeecg.modules.ldap.repository.LdapUserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.core.support.LdapContextSource;
import org.springframework.stereotype.Component;

/**
 * 用于配置 LDAP 仓库的管理类，使用动态 LdapTemplate 实例。
 *
 * <p>此管理器提供了一种集中式方法，通过特定的 LdapTemplate 实例来配置所有 LDAP 仓库，
 * 从而实现在运行时配置 LDAP 连接，而无需依赖 Spring 依赖注入。</p>
 */
@Component
@Getter
public class LdapRepositoryManager {

    private final LdapUserRepository userRepository;
    private final LdapGroupRepository groupRepository;
    private final LdapOrganizationRepository organizationRepository;

    @Autowired
    public LdapRepositoryManager(LdapUserRepository userRepository,
                                 LdapGroupRepository groupRepository,
                                 LdapOrganizationRepository organizationRepository) {
        this.userRepository = userRepository;
        this.groupRepository = groupRepository;
        this.organizationRepository = organizationRepository;
    }

    /**
     * 使用提供的 LdapTemplate 配置所有存储库。
     *
     * <p>此方法为所有存储库实例设置 LdapTemplate，
     * 确保它们使用相同的 LDAP 连接配置。</p>
     *
     * @param ldapTemplate 用于所有存储库的 LdapTemplate
     * @throws IllegalArgumentException 如果 ldapTemplate 为 null
     */
    public void configureRepositories(LdapTemplate ldapTemplate) {
        if (ldapTemplate == null) {
            throw new IllegalArgumentException("LdapTemplate cannot be null");
        }

        userRepository.setLdapTemplate(ldapTemplate);
        groupRepository.setLdapTemplate(ldapTemplate);
        organizationRepository.setLdapTemplate(ldapTemplate);
    }

    /**
     * 使用由提供的上下文源创建的LdapTemplate配置所有存储库。
     *
     * <p>此方法使用工厂创建一个LdapTemplate，并用它配置所有存储库。</p>
     *
     * @param contextSource 要使用的LDAP上下文源
     * @throws IllegalArgumentException 如果contextSource为null
     */
    public void configureRepositories(LdapContextSource contextSource) {
        if (contextSource == null) {
            throw new IllegalArgumentException("LdapContextSource cannot be null");
        }

        LdapTemplate ldapTemplate = LdapTemplateFactory.createLdapTemplate(contextSource);
        ldapTemplate.setIgnorePartialResultException(true);
        configureRepositories(ldapTemplate);
    }

    /**
     * 配置所有存储库，使用自定义的 LdapTemplate 配置。
     *
     * @param contextSource        要使用的 LDAP 上下文源
     * @param countLimit           搜索操作中返回的最大条目数
     * @param timeLimit            搜索操作的时间限制，以毫秒为单位
     * @param ignorePartialResults 是否忽略部分结果异常
     * @throws IllegalArgumentException 如果参数无效
     */
    public void configureRepositories(LdapContextSource contextSource,
                                      int countLimit,
                                      int timeLimit,
                                      boolean ignorePartialResults) {
        if (contextSource == null) {
            throw new IllegalArgumentException("LdapContextSource cannot be null");
        }

        LdapTemplate ldapTemplate = LdapTemplateFactory.createLdapTemplate(
                contextSource, countLimit, timeLimit, ignorePartialResults);
        configureRepositories(ldapTemplate);
    }

    /**
     * 检查所有存储库是否都已配置 LdapTemplate。
     *
     * @return 如果所有存储库均已配置，则返回 true；否则返回 false。
     */
    public boolean areRepositoriesConfigured() {
        try {
            userRepository.getLdapTemplate();
            groupRepository.getLdapTemplate();
            organizationRepository.getLdapTemplate();
            return true;
        } catch (IllegalStateException e) {
            return false;
        }
    }

    /**
     * 验证所有仓库是否都已正确配置。
     *
     * @throws IllegalStateException 如果任何仓库未配置
     */
    public void validateRepositoryConfiguration() {
        if (!areRepositoriesConfigured()) {
            throw new IllegalStateException("Not all repositories have been configured with LdapTemplate. " +
                                                    "Call configureRepositories() first.");
        }
    }
}
