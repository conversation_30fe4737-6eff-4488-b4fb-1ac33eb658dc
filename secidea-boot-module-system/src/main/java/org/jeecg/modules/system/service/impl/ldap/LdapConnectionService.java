package org.jeecg.modules.system.service.impl.ldap;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.StringUtils;
import org.jeecg.modules.ldap.config.LdapAttributeMapping;
import org.jeecg.modules.ldap.config.LdapServerType;
import org.jeecg.modules.ldap.service.LdapSynchronizationService;
import org.jeecg.modules.ldap.util.LdapDnUtils;
import org.jeecg.modules.system.entity.SysLdap;
import org.jeecg.modules.system.entity.SysLdap.EncryptType;
import org.jeecg.modules.system.entity.SysLdap.ServerType;
import org.jeecg.modules.system.model.LdapConnectTestModel;
import org.jeecg.modules.system.util.ldap.NonVerifyingSSLSocketFactory;
import org.jeecg.modules.system.util.ldap.SSLLdapContextSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.core.support.LdapContextSource;
import org.springframework.stereotype.Service;

import javax.naming.AuthenticationException;
import javax.naming.Context;
import javax.naming.NamingException;
import javax.naming.directory.DirContext;
import javax.naming.directory.InitialDirContext;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Map;
import java.util.Optional;

import static org.jeecg.modules.system.service.impl.ldap.LdapConstants.*;

/**
 * LDAP连接服务
 * 负责LDAP连接的创建、配置和测试
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class LdapConnectionService {
    
    @Autowired
    private LdapConfigurationService configurationService;
    
    @Autowired
    private LdapSynchronizationService ldapSynchronizationService;
    
    /**
     * 创建LDAP上下文源
     * 
     * @return LDAP上下文源
     */
    public LdapContextSource createContextSource() {
        SysLdap sysLdap = configurationService.getLdapConfiguration();
        return createContextSource(sysLdap);
    }
    
    /**
     * 根据配置创建LDAP上下文源
     * 
     * @param sysLdap LDAP配置
     * @return LDAP上下文源
     */
    public LdapContextSource createContextSource(SysLdap sysLdap) {
        LdapContextSource contextSource = createContextSourceByEncryptType(sysLdap.getLdapEncryptType());
        
        String urlPrefix = sysLdap.getLdapEncryptType() == EncryptType.LDAP ? LDAP_URL_PREFIX : LDAPS_URL_PREFIX;
        contextSource.setUrl(urlPrefix + sysLdap.getAdAddress() + ":" + sysLdap.getAdPort());
        
        String addomainName = sysLdap.getAddomainName();
        contextSource.setBase(addomainName);
        
        String adName = sysLdap.getAdName();
        if (StringUtils.isNotBlank(addomainName)) {
            String userDN = LdapDnUtils.extractAdminLoginName(adName, addomainName);
            contextSource.setUserDn(userDN);
        }
        
        String password = Optional.ofNullable(sysLdap.getAdPasswd()).orElse("");
        contextSource.setPassword(password);
        contextSource.setPooled(true);
        contextSource.afterPropertiesSet();
        
        // 配置二进制属性以解决乱码问题
        Map<String, Object> config = new HashMap<>();
        config.put(LDAP_ATTR_BINARY, LDAP_ATTR_OBJECT_GUID);
        contextSource.setBaseEnvironmentProperties(config);
        
        return contextSource;
    }
    
    /**
     * 创建LDAP模板
     * 
     * @param contextSource LDAP上下文源
     * @return LDAP模板
     */
    public LdapTemplate createLdapTemplate(LdapContextSource contextSource) {
        return new LdapTemplate(contextSource);
    }
    
    /**
     * 创建LDAP模板
     * 
     * @return LDAP模板
     */
    public LdapTemplate createLdapTemplate() {
        return createLdapTemplate(createContextSource());
    }
    
    /**
     * 测试LDAP连接
     * 
     * @param connectProperties 连接属性
     * @return 测试结果
     */
    public Result<?> testConnection(LdapConnectTestModel connectProperties) {
        // 验证必填参数
        Result<?> validationResult = validateConnectionProperties(connectProperties);
        if (!validationResult.isSuccess()) {
            return validationResult;
        }
        
        DirContext ctx = null;
        String authResult = "";
        
        try {
            Hashtable<String, String> env = buildLdapEnvironment(connectProperties);
            ctx = new InitialDirContext(env);
        } catch (AuthenticationException e) {
            log.error("LDAP身份验证失败！", e);
            authResult = ERROR_LDAP_AUTH_FAILED;
        } catch (NamingException e) {
            log.error("LDAP连接失败！", e);
            authResult = ERROR_LDAP_CONNECT_FAILED;
        } catch (Exception e) {
            log.error("身份验证未知异常！", e);
            authResult = ERROR_LDAP_UNKNOWN;
        } finally {
            closeContext(ctx);
        }
        
        if (StringUtils.isNotBlank(authResult)) {
            return Result.error(authResult);
        } else {
            return Result.ok(SUCCESS_TEST_CONNECTION);
        }
    }
    
    /**
     * 更新LDAP配置
     * 
     * @param sysLdap LDAP配置
     */
    public void updateLdapConfiguration(SysLdap sysLdap) {
        LdapServerType ldapServerType = sysLdap.getLdapServerType() == ServerType.AD
                ? LdapServerType.ACTIVE_DIRECTORY
                : LdapServerType.OPENLDAP;
        
        LdapAttributeMapping mapping = LdapAttributeMapping.builder()
                .user(
                        LdapAttributeMapping.UserAttributes.builder()
                                .username(sysLdap.getAttrMapperField().getLdapLoginUserAttr())
                                .commonName(sysLdap.getAttrMapperField().getLdapLoginUserAttr())
                                .displayName(sysLdap.getAttrMapperField().getLdapUsernameAttr())
                                .mail(sysLdap.getAttrMapperField().getLdapEmailAttr())
                                .telephoneNumber(sysLdap.getAttrMapperField().getLdapPhoneAttr())
                                .description(sysLdap.getAttrMapperField().getLdapDescriptionAttr())
                                .build()
                )
                .build();
        
        ldapSynchronizationService.configureLdapConnection(createContextSource(sysLdap), ldapServerType, mapping);
    }
    
    /**
     * 根据加密类型创建上下文源
     * 
     * @param encryptType 加密类型
     * @return 上下文源
     */
    private LdapContextSource createContextSourceByEncryptType(EncryptType encryptType) {
        if (encryptType == EncryptType.LDAP) {
            return new LdapContextSource();
        } else {
            return new SSLLdapContextSource();
        }
    }
    
    /**
     * 验证连接属性
     * 
     * @param connectProperties 连接属性
     * @return 验证结果
     */
    private Result<?> validateConnectionProperties(LdapConnectTestModel connectProperties) {
        if (StringUtils.isEmpty(connectProperties.getAdAddress())) {
            return Result.error(ERROR_LDAP_ADDRESS_EMPTY);
        }
        if (StringUtils.isEmpty(connectProperties.getAdPort())) {
            return Result.error(ERROR_LDAP_PORT_EMPTY);
        }
        if (StringUtils.isNotEmpty(connectProperties.getAdName()) && 
            StringUtils.isEmpty(connectProperties.getAdPasswd())) {
            return Result.error(ERROR_LDAP_PASSWORD_EMPTY);
        }
        return Result.ok();
    }
    
    /**
     * 构建LDAP环境配置
     * 
     * @param connectProperties 连接属性
     * @return 环境配置
     */
    private Hashtable<String, String> buildLdapEnvironment(LdapConnectTestModel connectProperties) {
        Hashtable<String, String> env = new Hashtable<>();
        
        // 基本配置
        env.put(Context.INITIAL_CONTEXT_FACTORY, LDAP_CONTEXT_FACTORY);
        env.put(Context.SECURITY_AUTHENTICATION, LDAP_SECURITY_AUTHENTICATION);
        env.put(LDAP_CONNECT_TIMEOUT, LDAP_CONNECT_TIMEOUT_VALUE);
        
        // 用户认证信息
        if (StringUtils.isNotEmpty(connectProperties.getAdName())) {
            configurationService.getLdapConfiguration(); // 确保配置已更新
            String userDN = ldapSynchronizationService.getUserDN(connectProperties.getAdName(),
                    connectProperties.getAddomainName());
            if (StringUtils.isEmpty(userDN)) {
                throw new RuntimeException(ERROR_LDAP_AUTH_FAILED);
            }
            env.put(Context.SECURITY_PRINCIPAL, userDN);
            env.put(Context.SECURITY_CREDENTIALS, connectProperties.getAdPasswd());
        }
        
        // URL配置
        EncryptType encryptType = EncryptType.fromValue(Integer.parseInt(connectProperties.getLdapEncryptType()));
        if (encryptType == EncryptType.LDAP) {
            env.put(Context.PROVIDER_URL, LDAP_URL_PREFIX + connectProperties.getAdAddress() + ":" + connectProperties.getAdPort());
        } else {
            env.put(Context.PROVIDER_URL, LDAPS_URL_PREFIX + connectProperties.getAdAddress() + ":" + connectProperties.getAdPort());
            env.put("java.naming.security.protocol", LDAP_SECURITY_PROTOCOL);
            env.put("java.naming.ldap.factory.socket", NonVerifyingSSLSocketFactory.class.getName());
        }
        
        return env;
    }
    
    /**
     * 关闭LDAP上下文
     * 
     * @param ctx LDAP上下文
     */
    private void closeContext(DirContext ctx) {
        if (ctx != null) {
            try {
                ctx.close();
            } catch (Exception e) {
                log.error("关闭ldap上下文异常", e);
            }
        }
    }
}
