package org.jeecg.modules.system.service.impl.ldap;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.util.PasswordUtil;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.StringUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.ldap.model.LdapUser;
import org.jeecg.modules.ldap.service.LdapSynchronizationService;
import org.jeecg.modules.ldap.service.LdapUserService;
import org.jeecg.modules.ldap.util.LdapDnUtils;
import org.jeecg.modules.system.entity.*;
import org.jeecg.modules.system.model.LdapModel;
import org.jeecg.modules.system.service.ISysUserRoleService;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.system.service.impl.SysDepartServiceImpl;
import org.jeecg.modules.system.service.impl.SysRoleServiceImpl;
import org.jeecg.modules.system.service.impl.SysUserDepartServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

import static org.jeecg.modules.system.service.impl.ldap.LdapConstants.*;

/**
 * LDAP用户同步服务
 * 负责LDAP用户的同步、更新和管理操作
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class LdapUserSyncService {
    
    @Autowired
    private LdapSynchronizationService ldapSynchronizationService;
    
    @Autowired
    private LdapUserService ldapUserService;
    
    @Autowired
    private LdapConnectionService connectionService;
    
    @Autowired
    private LdapConfigurationService configurationService;
    
    @Autowired
    private LdapUserConverter userConverter;
    
    @Autowired
    private ISysUserService sysUserService;
    
    @Autowired
    private SysRoleServiceImpl sysRoleService;
    
    @Autowired
    private SysDepartServiceImpl sysDepartService;
    
    @Autowired
    private SysUserDepartServiceImpl sysUserDepartService;
    
    @Autowired
    private ISysUserRoleService sysUserRoleService;
    
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    
    @Autowired
    private RedisUtil redisUtil;
    
    /**
     * 从组织单元中查找用户
     * 
     * @param base 基准DN
     * @return 用户列表
     */
    public List<Person> findUsersFromOrganizationalUnit(String base) {
        SysLdap sysLdap = configurationService.getLdapConfiguration();
        connectionService.updateLdapConfiguration(sysLdap);
        
        List<LdapUser> ldapUsers = ldapSynchronizationService.getUsersFromOrganizationalUnit(base,
                sysLdap.getLdapSyncFilter());
        
        return ldapUsers.stream()
                .map(userConverter::convertToPerson)
                .collect(Collectors.toList());
    }
    
    /**
     * 验证部门用户同步
     * 
     * @param departId 部门ID
     * @return 验证结果
     */
    public Result<?> verifyDepartmentUserSync(String departId) {
        SysDepart sysDepart = sysDepartService.getById(departId);
        if (!THIRD_ID_LDAP.equals(sysDepart.getThirdId())) {
            return Result.error(ERROR_CANNOT_SYNC_LOCAL_DEPART);
        }
        
        if (configurationService.isUnSyncedLoginAllowed()) {
            return Result.error(ERROR_DISABLE_UNSYNCED_LOGIN);
        }
        
        SysLdap sysLdap = configurationService.getLdapConfiguration();
        List<SysRole> sysRoleList = getRolesByLdapConfig(sysLdap);
        if (sysRoleList == null) {
            return Result.error(ERROR_NO_DEFAULT_ROLE);
        }
        
        String base = buildDepartmentBaseDN(departId, sysLdap.getAddomainName());
        
        List<Person> personList;
        try {
            personList = findUsersFromOrganizationalUnit(base);
        } catch (Exception e) {
            log.error(LOG_SYNC_GROUP_EXCEPTION, e.getMessage());
            return Result.error(ERROR_LDAP_CONFIG);
        }
        
        LdapModel ldapModel = new LdapModel();
        ldapModel.setSysLdap(sysLdap);
        ldapModel.setSysRoleList(sysRoleList);
        ldapModel.setPersonList(personList);
        
        return Result.OK(ldapModel);
    }
    
    /**
     * 同步用户
     * 
     * @param status 用户状态
     * @param departId 部门ID
     * @param ldapModel LDAP模型
     */
    public void syncUsers(int status, String departId, LdapModel ldapModel) {
        List<Person> personList = ldapModel.getPersonList();
        List<SysRole> sysRoleList = ldapModel.getSysRoleList();
        SysDepart sysDepart = sysDepartService.getById(departId);
        
        // 获取部门现有用户
        List<SysUser> existingUsers = sysUserDepartService.queryUserByDepCode(sysDepart.getOrgCode(), null);
        List<String> existingUsernames = existingUsers.stream()
                .map(SysUser::getUsername)
                .collect(Collectors.toList());
        
        List<String> syncedUsernames = new ArrayList<>();
        boolean isOverwriteSysUser = configurationService.isOverwriteSysUser();
        
        // 同步用户
        for (Person person : personList) {
            if (StringUtils.isEmpty(person.getLoginName())) {
                continue;
            }
            
            try {
                String loginName = person.getLoginName();
                if (existingUsernames.contains(loginName)) {
                    syncedUsernames.add(loginName);
                }
                
                processUser(loginName, sysRoleList, status, person, isOverwriteSysUser);
            } catch (Exception e) {
                log.warn(LOG_SYNC_USER_FAILED, e.getMessage());
            }
        }
        
        // 删除不存在的用户
        removeNonExistentUsers(existingUsernames, syncedUsernames);
        
        // 清理逻辑删除的用户
        cleanupLogicallyDeletedUsers(sysDepart);
        
        log.info(LOG_SYNC_USER_COMPLETE);
        redisUtil.del(CACHE_LDAP_SYNC_USER);
    }
    
    /**
     * 搜索LDAP用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    public Person searchLdapUser(String username) {
        try {
            connectionService.updateLdapConfiguration(configurationService.getLdapConfiguration());
            LdapUser user = ldapUserService.findUsersByUsername(username, "");
            return userConverter.convertToPerson(user);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 处理用户（更新或创建）
     * 
     * @param loginName 登录名
     * @param sysRoleList 角色列表
     * @param status 状态
     * @param person 用户信息
     * @param isOverwriteSysUser 是否覆盖系统用户
     */
    private void processUser(String loginName, List<SysRole> sysRoleList, int status, 
                           Person person, boolean isOverwriteSysUser) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getUsername, loginName);
        
        // 处理逻辑删除的用户
        List<SysUser> deletedUsers = sysUserService.queryLogicDeleted(queryWrapper.clone());
        if (!deletedUsers.isEmpty()) {
            if (!THIRD_ID_LDAP.equals(deletedUsers.get(0).getThirdId()) && !isOverwriteSysUser) {
                return;
            }
            restoreDeletedUser(deletedUsers);
        }
        
        SysUser sysUser = sysUserService.getOne(queryWrapper);
        if (sysUser != null) {
            if (!THIRD_ID_LDAP.equals(sysUser.getThirdId()) && !isOverwriteSysUser) {
                return;
            }
            updateUser(loginName, sysUser, sysRoleList, status, person);
        } else {
            saveUser(sysRoleList, null, loginName, null, status, person);
        }
    }
    
    /**
     * 更新用户信息
     * 
     * @param userName 用户名
     * @param sysUser 系统用户
     * @param defaultRoles 默认角色
     * @param status 状态
     * @param person 用户信息
     */
    private void updateUser(String userName, SysUser sysUser, List<SysRole> defaultRoles,
                          int status, Person person) {
        if (person == null) {
            person = searchLdapUser(userName);
        }
        if (person == null) {
            log.warn(LOG_USER_NOT_EXIST_IN_LDAP, userName);
            return;
        }
        
        // 获取用户部门信息
        UserDepartment department = getDepartmentFromDN(person.getDistinguishedName());
        String adUserStatus = configurationService.getAdUserStatus();
        
        // 处理用户部门变化
        handleUserDepartmentChange(sysUser, department, adUserStatus);
        
        // 更新用户基本信息
        updateUserBasicInfo(sysUser, person, department, status);
        
        // 处理用户角色
        String roleIds = handleUserRoles(sysUser, defaultRoles);
        
        sysUser.setThirdId(THIRD_ID_LDAP);
        sysUser.setThirdType(THIRD_TYPE_LDAP);
        sysUserService.editUser(sysUser, roleIds, department.getDepartmentId());
    }
    
    /**
     * 保存新用户
     *
     * @param roleList 角色列表
     * @param ldapDepart LDAP部门
     * @param userName 用户名
     * @param password 密码
     * @param status 状态
     * @param person 用户信息
     * @return 保存的用户
     */
    public SysUser saveUser(List<SysRole> roleList, String ldapDepart, String userName,
                           String password, int status, Person person) {
        String selectedRoles = roleList.stream()
                .map(SysRole::getId)
                .distinct()
                .collect(Collectors.joining(","));
        
        SysUser sysUser = createNewUser(userName, password, status);
        
        if (person == null) {
            person = searchLdapUser(userName);
        }
        
        UserDepartment department = getDepartmentInfo(ldapDepart, person);
        
        if (StringUtils.isNotBlank(department.getDepartmentId())) {
            sysUser.setOrgCode(department.getOrgCode());
            updateUserFromPerson(sysUser, person);
            sysUserService.saveUser(sysUser, selectedRoles, department.getDepartmentId());
            return sysUser;
        }
        
        return null;
    }
    
    /**
     * 根据DN获取部门信息
     * 
     * @param distinguishedName 区分名
     * @return 用户部门信息
     */
    private UserDepartment getDepartmentFromDN(String distinguishedName) {
        UserDepartment department = new UserDepartment();
        List<String> dnParts = Arrays.asList(distinguishedName.split(","));
        
        for (int i = dnParts.size() - 1; i >= 0; i--) {
            String part = dnParts.get(i);
            Matcher matcher = OU_PATTERN.matcher(part);
            if (!matcher.find()) {
                continue;
            }
            
            LambdaQueryWrapper<SysDepart> query = new LambdaQueryWrapper<>();
            addDepartmentNameCondition(matcher, query);
            query.eq(SysDepart::getThirdId, THIRD_ID_LDAP);
            
            if (StringUtils.isNotBlank(department.getDepartmentId())) {
                query.eq(SysDepart::getParentId, department.getDepartmentId());
            } else {
                query.eq(SysDepart::getOrgType, ORG_TYPE_TOP);
            }
            
            List<SysDepart> departments = sysDepartService.list(query);
            if (!departments.isEmpty()) {
                SysDepart dept = departments.get(0);
                department.setDepartmentId(dept.getId());
                department.setDepartmentName(dept.getDepartName());
                department.setOrgCode(dept.getOrgCode());
            }
        }
        
        return department;
    }
    
    /**
     * 添加部门名称查询条件
     * 
     * @param matcher 匹配器
     * @param query 查询条件
     */
    private void addDepartmentNameCondition(Matcher matcher, LambdaQueryWrapper<SysDepart> query) {
        String name = matcher.group(2);
        String suffix = OU_SUFFIX_MAP.get(matcher.group(1).toLowerCase());
        query.and(q -> q.eq(SysDepart::getDepartName, name + suffix)
                .or()
                .eq(SysDepart::getDepartName, name + ORIGINAL_OU_SUFFIX));
    }
    
    /**
     * 根据LDAP配置获取角色列表
     * 
     * @param sysLdap LDAP配置
     * @return 角色列表
     */
    private List<SysRole> getRolesByLdapConfig(SysLdap sysLdap) {
        LambdaQueryWrapper<SysRole> roleQuery = new LambdaQueryWrapper<>();
        roleQuery.in(SysRole::getRoleCode, Arrays.asList(sysLdap.getLdapRole().split(",")));
        return sysRoleService.list(roleQuery);
    }
    
    /**
     * 构建部门基准DN
     * 
     * @param departId 部门ID
     * @param domainName 域名
     * @return 基准DN
     */
    private String buildDepartmentBaseDN(String departId, String domainName) {
        String base = recursionBase(departId, "").toLowerCase();
        return LdapDnUtils.relativePath(domainName, base);
    }
    
    /**
     * 递归构建基准DN
     * 
     * @param departId 部门ID
     * @param base 基准路径
     * @return 构建的DN
     */
    private String recursionBase(String departId, String base) {
        SysDepart sysDepart = sysDepartService.getById(departId);
        if (sysDepart != null) {
            String departName = sysDepart.getDepartName()
                    .replace(ORIGINAL_OU_SUFFIX, "")
                    .replace(OU_SUFFIX, "")
                    .replace(O_SUFFIX, "");
            
            String prefix = O_PREFIX.equals(sysDepart.getDepartNameAbbr()) ? O_PREFIX : OU_PREFIX;
            base = "".equals(base) ? prefix + "=" + departName : base + "," + prefix + "=" + departName;
            
            if (StringUtils.isNotBlank(sysDepart.getParentId())) {
                base = recursionBase(sysDepart.getParentId(), base);
            }
        }
        return base;
    }
    
    /**
     * 恢复逻辑删除的用户
     *
     * @param deletedUsers 已删除的用户列表
     */
    private void restoreDeletedUser(List<SysUser> deletedUsers) {
        List<String> userIds = deletedUsers.stream().map(SysUser::getId).collect(Collectors.toList());
        SysUser updateEntity = new SysUser();
        updateEntity.setUpdateTime(new Date());
        sysUserService.revertLogicDeleted(userIds, updateEntity);
    }

    /**
     * 处理用户部门变化
     *
     * @param sysUser 系统用户
     * @param department 部门信息
     * @param adUserStatus AD用户状态
     */
    private void handleUserDepartmentChange(SysUser sysUser, UserDepartment department, String adUserStatus) {
        List<SysDepart> userDeparts = sysDepartService.queryUserDeparts(sysUser.getId());
        List<String> userSysDepartIds = new ArrayList<>();

        if (userDeparts != null) {
            for (SysDepart depart : userDeparts) {
                if (THIRD_ID_LDAP.equals(depart.getThirdId())) {
                    if (!department.getDepartmentName().equals(depart.getDepartName())) {
                        sysUser.setStatus(Integer.valueOf(adUserStatus));
                    }
                } else {
                    userSysDepartIds.add(depart.getId());
                }
            }
        } else {
            sysUser.setStatus(Integer.valueOf(adUserStatus));
        }

        if (CollectionUtils.isNotEmpty(userSysDepartIds)) {
            userSysDepartIds.add(department.getDepartmentId());
            String userSysDepartIdStr = String.join(",", userSysDepartIds);
            department.setDepartmentId(userSysDepartIdStr);
        }
    }

    /**
     * 更新用户基本信息
     *
     * @param sysUser 系统用户
     * @param person 用户信息
     * @param department 部门信息
     * @param status 状态
     */
    private void updateUserBasicInfo(SysUser sysUser, Person person, UserDepartment department, int status) {
        sysUser.setUpdateTime(new Date());
        sysUser.setRealname(person.getUserName());
        sysUser.setDescription(person.getDescription());
        sysUser.setEmail(person.getEmail());
        sysUser.setPhone(person.getTelephoneNumber());
        sysUser.setOrgCode(department.getOrgCode());

        if (!THIRD_ID_LDAP.equals(sysUser.getThirdId())) {
            String salt = oConvertUtils.randomGen(SALT_LENGTH);
            sysUser.setSalt("");
            String passwordEncode = PasswordUtil.encrypt(sysUser.getUsername(), sysUser.getPassword(), salt);
            sysUser.setPassword(passwordEncode);
            sysUser.setStatus(status);
        }
    }

    /**
     * 处理用户角色
     *
     * @param sysUser 系统用户
     * @param defaultRoles 默认角色
     * @return 角色ID字符串
     */
    private String handleUserRoles(SysUser sysUser, List<SysRole> defaultRoles) {
        LambdaQueryWrapper<SysUserRole> userRoleQuery = new LambdaQueryWrapper<>();
        userRoleQuery.eq(SysUserRole::getUserId, sysUser.getId());
        userRoleQuery.select(SysUserRole::getRoleId);
        List<SysUserRole> userRoles = sysUserRoleService.list(userRoleQuery);

        if (userRoles == null || userRoles.isEmpty() || !THIRD_ID_LDAP.equals(sysUser.getThirdId())) {
            return defaultRoles.stream().map(SysRole::getId).distinct().collect(Collectors.joining(","));
        } else {
            return userRoles.stream().map(SysUserRole::getRoleId).distinct().collect(Collectors.joining(","));
        }
    }

    /**
     * 创建新用户
     *
     * @param userName 用户名
     * @param password 密码
     * @param status 状态
     * @return 新用户
     */
    private SysUser createNewUser(String userName, String password, int status) {
        SysUser sysUser = new SysUser();
        sysUser.setUsername(userName);

        if (StringUtils.isNotBlank(password)) {
            String salt = oConvertUtils.randomGen(SALT_LENGTH);
            sysUser.setSalt(salt);
            String passwordEncode = PasswordUtil.encrypt(userName, password + PASSWORD_SUFFIX, salt);
            sysUser.setPassword(passwordEncode);
        }

        sysUser.setActivitiSync(1);
        sysUser.setUserIdentity(1);

        if (configurationService.isUnSyncedLoginAllowed()) {
            sysUser.setThirdId(THIRD_ID_LDAP);
        }

        sysUser.setThirdType(THIRD_TYPE_LDAP);
        sysUser.setStatus(status);
        sysUser.setDelFlag(CommonConstant.DEL_FLAG_0);
        sysUser.setCreateTime(new Date());
        sysUser.setUpdateTime(new Date());
        sysUser.setRealname(userName);

        return sysUser;
    }

    /**
     * 获取部门信息
     *
     * @param ldapDepart LDAP部门
     * @param person 用户信息
     * @return 部门信息
     */
    private UserDepartment getDepartmentInfo(String ldapDepart, Person person) {
        UserDepartment department = new UserDepartment();

        if (StringUtils.isNotBlank(ldapDepart)) {
            String[] orgCodes = ldapDepart.split(",");
            SysDepart sysDepart = sysDepartService.getById(orgCodes[0]);
            if (sysDepart != null) {
                department.setOrgCode(sysDepart.getOrgCode());
            }
            department.setDepartmentId(ldapDepart);
        } else if (person != null) {
            String distinguishedName = person.getDistinguishedName();
            department = getDepartmentFromDN(distinguishedName);
        }

        return department;
    }

    /**
     * 从Person对象更新用户信息
     *
     * @param sysUser 系统用户
     * @param person 用户信息
     */
    private void updateUserFromPerson(SysUser sysUser, Person person) {
        if (person != null) {
            sysUser.setRealname(person.getUserName());
            sysUser.setDescription(person.getDescription());
            sysUser.setEmail(person.getEmail());
            sysUser.setPhone(person.getTelephoneNumber());
        }
    }

    /**
     * 移除不存在的用户
     *
     * @param existingUsernames 现有用户名列表
     * @param syncedUsernames 已同步用户名列表
     */
    private void removeNonExistentUsers(List<String> existingUsernames, List<String> syncedUsernames) {
        existingUsernames.removeAll(syncedUsernames);
        if (CollectionUtils.isNotEmpty(existingUsernames)) {
            QueryWrapper<SysUser> removeQuery = new QueryWrapper<>();
            removeQuery.lambda().in(SysUser::getUsername, existingUsernames);
            sysUserService.remove(removeQuery);
        }
    }

    /**
     * 清理逻辑删除的用户
     *
     * @param sysDepart 系统部门
     */
    private void cleanupLogicallyDeletedUsers(SysDepart sysDepart) {
        List<SysUser> deletedUsers = sysUserDepartService.queryLogicDeletedUserByDepCode(sysDepart.getOrgCode(), null);
        List<String> deletedUserIds = deletedUsers.stream().map(SysUser::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deletedUserIds)) {
            sysUserService.removeLogicDeleted(deletedUserIds);
        }
    }

    /**
     * 用户所属的部门
     */
    @Getter
    @Setter
    static class UserDepartment {
        private String departmentId;
        private String departmentName;
        private String orgCode;
    }
}
