package org.jeecg.modules.system.service.impl.ldap;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.vo.SysConfigParam;
import org.jeecg.modules.system.entity.SysLdap;
import org.jeecg.modules.system.entity.SysLdap.AttrMapperField;
import org.jeecg.modules.system.entity.SysLdap.EncryptType;
import org.jeecg.modules.system.entity.SysLdap.ServerType;
import org.jeecg.modules.system.service.ISysConfigParamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.jeecg.modules.system.service.impl.ldap.LdapConstants.*;

/**
 * LDAP配置服务
 * 负责LDAP相关配置的获取和管理
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class LdapConfigurationService {
    
    @Autowired
    private ISysConfigParamService sysConfigParamService;
    
    /**
     * 获取LDAP配置参数
     * 
     * @return LDAP配置对象
     */
    public SysLdap getLdapConfiguration() {
        SysLdap sysLdap = new SysLdap();
        AttrMapperField attrMapperField = new AttrMapperField();
        
        QueryWrapper<SysConfigParam> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(SysConfigParam::getParamKey, 
            AD_ADDRESS_KEY, AD_PORT_KEY, AD_DOMAIN_NAME_KEY, AD_NAME_KEY,
            AD_PASSWD_KEY, LDAP_SYNC_FILTER_KEY, LDAP_SERVER_TYPE_KEY, LDAP_ENCRYPT_TYPE_KEY,
            LDAP_LOGIN_USER_ATTR_KEY, LDAP_USERNAME_ATTR_KEY, LDAP_EMAIL_ATTR_KEY, LDAP_PHONE_ATTR_KEY,
            LDAP_DESCRIPTION_ATTR_KEY, LDAP_ROLE_KEY, LDAP_DEPART_KEY);
        
        List<SysConfigParam> configParams = sysConfigParamService.list(queryWrapper);
        
        for (SysConfigParam param : configParams) {
            setConfigValue(sysLdap, attrMapperField, param.getParamKey(), param.getParamValue());
        }
        
        attrMapperField.setDefaultFieldIfEmpty(sysLdap.getLdapServerType());
        sysLdap.setAttrMapperField(attrMapperField);
        
        return sysLdap;
    }
    
    /**
     * 获取指定的配置参数值
     * 
     * @param keys 配置参数键列表
     * @return 配置参数映射
     */
    public Map<String, String> getConfigParams(String... keys) {
        QueryWrapper<SysConfigParam> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(SysConfigParam::getParamKey, (Object[]) keys);
        
        List<SysConfigParam> configParams = sysConfigParamService.list(queryWrapper);
        Map<String, String> configMap = new HashMap<>();
        
        for (SysConfigParam param : configParams) {
            configMap.put(param.getParamKey(), param.getParamValue());
        }
        
        return configMap;
    }
    
    /**
     * 获取单个配置参数值
     * 
     * @param key 配置参数键
     * @return 配置参数值
     */
    public String getConfigParam(String key) {
        return sysConfigParamService.getSysConfigParamByKey(key);
    }
    
    /**
     * 获取单个配置参数值，如果为空则返回默认值
     * 
     * @param key 配置参数键
     * @param defaultValue 默认值
     * @return 配置参数值
     */
    public String getConfigParam(String key, String defaultValue) {
        String value = getConfigParam(key);
        return value != null ? value : defaultValue;
    }
    
    /**
     * 检查LDAP是否已开启
     * 
     * @return true如果已开启，false否则
     */
    public boolean isLdapEnabled() {
        return STATUS_ENABLED.equals(getConfigParam(AD_OPEN_KEY));
    }
    
    /**
     * 检查是否允许未同步用户登录
     * 
     * @return true如果允许，false否则
     */
    public boolean isUnSyncedLoginAllowed() {
        return !STATUS_ENABLED.equals(getConfigParam(AD_VERIFICATION_KEY));
    }
    
    /**
     * 检查是否覆盖重名用户
     * 
     * @return true如果覆盖，false否则
     */
    public boolean isOverwriteSysUser() {
        return STATUS_ENABLED.equals(getConfigParam(LDAP_OVERWRITE_SYS_USER_KEY));
    }
    
    /**
     * 获取AD用户状态配置
     * 
     * @return AD用户状态
     */
    public String getAdUserStatus() {
        return getConfigParam(AD_USER_STATUS_KEY, DEFAULT_AD_USER_STATUS);
    }
    
    /**
     * 设置配置值到对应的对象属性
     * 
     * @param sysLdap LDAP配置对象
     * @param attrMapperField 属性映射字段对象
     * @param paramKey 参数键
     * @param paramValue 参数值
     */
    private void setConfigValue(SysLdap sysLdap, AttrMapperField attrMapperField, String paramKey, String paramValue) {
        switch (paramKey) {
            case AD_ADDRESS_KEY:
                sysLdap.setAdAddress(paramValue);
                break;
            case AD_PORT_KEY:
                sysLdap.setAdPort(paramValue);
                break;
            case AD_DOMAIN_NAME_KEY:
                sysLdap.setAddomainName(paramValue);
                break;
            case AD_NAME_KEY:
                sysLdap.setAdName(paramValue);
                break;
            case AD_PASSWD_KEY:
                sysLdap.setAdPasswd(paramValue);
                break;
            case LDAP_SYNC_FILTER_KEY:
                sysLdap.setLdapSyncFilter(paramValue);
                break;
            case LDAP_SERVER_TYPE_KEY:
                sysLdap.setLdapServerType(ServerType.fromValue(Integer.parseInt(paramValue)));
                break;
            case LDAP_ENCRYPT_TYPE_KEY:
                sysLdap.setLdapEncryptType(EncryptType.fromValue(Integer.parseInt(paramValue)));
                break;
            case LDAP_ROLE_KEY:
                sysLdap.setLdapRole(paramValue);
                break;
            case LDAP_DEPART_KEY:
                sysLdap.setLdapDepart(paramValue);
                break;
            case LDAP_LOGIN_USER_ATTR_KEY:
                attrMapperField.setLdapLoginUserAttr(paramValue);
                break;
            case LDAP_USERNAME_ATTR_KEY:
                attrMapperField.setLdapUsernameAttr(paramValue);
                break;
            case LDAP_EMAIL_ATTR_KEY:
                attrMapperField.setLdapEmailAttr(paramValue);
                break;
            case LDAP_PHONE_ATTR_KEY:
                attrMapperField.setLdapPhoneAttr(paramValue);
                break;
            case LDAP_DESCRIPTION_ATTR_KEY:
                attrMapperField.setLdapDescriptionAttr(paramValue);
                break;
            default:
                log.debug("未知的配置参数键: {}", paramKey);
                break;
        }
    }
}
