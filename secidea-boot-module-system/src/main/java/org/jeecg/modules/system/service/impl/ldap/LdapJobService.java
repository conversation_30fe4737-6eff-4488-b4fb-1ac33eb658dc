package org.jeecg.modules.system.service.impl.ldap;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.dto.message.XxlJobInfo;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.StringUtils;
import org.jeecg.common.xxljob.entity.Resp;
import org.jeecg.common.xxljob.service.XxlJobService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static org.jeecg.common.system.util.AuthCommonUtil.getLoginUser;
import static org.jeecg.modules.system.service.impl.ldap.LdapConstants.*;

/**
 * LDAP定时任务服务
 * 负责LDAP相关定时任务的管理
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class LdapJobService {
    
    @Autowired
    private XxlJobService xxlJobService;
    
    /**
     * 管理定时部门用户同步任务
     * 
     * @param adTimingOpen 定时任务开关
     * @param adCronExpression cron表达式
     * @param adParentId 父级ID
     * @param adXxlJobInfoId 任务ID
     * @return 操作结果
     */
    public Result<?> manageTimingDepartUser(int adTimingOpen, String adCronExpression, 
                                          String adParentId, Integer adXxlJobInfoId) {
        if (adTimingOpen == 1 && StringUtils.isNotBlank(adCronExpression) && StringUtils.isNotBlank(adParentId)) {
            // 新增或编辑定时任务
            Result<?> jobResult = createOrUpdateTimingJob(adParentId, adCronExpression, adXxlJobInfoId);
            if (!jobResult.isSuccess()) {
                return Result.error(jobResult.getMessage());
            }
            
            if (adXxlJobInfoId == null) {
                adXxlJobInfoId = (Integer) jobResult.getResult();
            }
            return Result.OK(adXxlJobInfoId);
            
        } else if (adTimingOpen == 2 && adXxlJobInfoId != null) {
            // 关闭定时任务
            Resp deleteJob = xxlJobService.delete(adXxlJobInfoId);
            if (deleteJob.getCode() != HTTP_CODE_SUCCESS) {
                return Result.error(ERROR_JOB_CLOSE_FAILED);
            }
        } else {
            return Result.error(ERROR_FILL_INFO);
        }
        
        return Result.OK();
    }
    
    /**
     * 创建或更新定时任务
     * 
     * @param departId 部门ID
     * @param cronExpression cron表达式
     * @param xxlJobInfoId 任务ID（更新时使用）
     * @return 操作结果
     */
    private Result<?> createOrUpdateTimingJob(String departId, String cronExpression, Integer xxlJobInfoId) {
        LoginUser sysUser = getLoginUser();
        
        // 获取应用组ID
        Resp appNameResponse = xxlJobService.getAppNameIdByAppname();
        if (appNameResponse.getCode() == HTTP_CODE_NOT_FOUND) {
            return Result.error(ERROR_JOB_SERVICE_NOT_STARTED);
        }
        
        int groupId = extractGroupId(appNameResponse);
        if (groupId == 0) {
            return Result.error(ERROR_JOB_SERVICE_NOT_STARTED);
        }
        
        // 构建任务信息
        XxlJobInfo xxlJobInfo = buildJobInfo(departId, cronExpression, xxlJobInfoId, groupId, sysUser);
        
        // 保存任务
        Resp saveResponse = xxlJobService.saveXxl(xxlJobInfo);
        return processJobSaveResponse(saveResponse);
    }
    
    /**
     * 从响应中提取组ID
     * 
     * @param appNameResponse 应用名称响应
     * @return 组ID
     */
    private int extractGroupId(Resp appNameResponse) {
        if (appNameResponse.getRespBody() == null) {
            return 0;
        }
        
        try {
            JSONObject jsonObject = (JSONObject) JSONObject.toJSON(appNameResponse.getRespBody());
            Integer code = jsonObject.getInteger("code");
            if (code != null && code == HTTP_CODE_SUCCESS) {
                JSONObject content = jsonObject.getJSONObject("content");
                if (content != null) {
                    return content.getInteger("id");
                }
            }
        } catch (Exception e) {
            log.error("解析应用组ID失败", e);
        }
        
        return 0;
    }
    
    /**
     * 构建任务信息
     * 
     * @param departId 部门ID
     * @param cronExpression cron表达式
     * @param xxlJobInfoId 任务ID
     * @param groupId 组ID
     * @param sysUser 系统用户
     * @return 任务信息
     */
    private XxlJobInfo buildJobInfo(String departId, String cronExpression, Integer xxlJobInfoId, 
                                   int groupId, LoginUser sysUser) {
        XxlJobInfo xxlJobInfo = new XxlJobInfo();
        
        if (xxlJobInfoId != null) {
            xxlJobInfo.setId(xxlJobInfoId);
        }
        
        xxlJobInfo.setJobGroup(groupId);
        xxlJobInfo.setJobCron(cronExpression);
        xxlJobInfo.setJobDesc(JOB_DESC_SYNC_DEPART_USER);
        xxlJobInfo.setAuthor(sysUser.getRealname());
        xxlJobInfo.setExecutorRouteStrategy(JOB_EXECUTOR_ROUTE_STRATEGY);
        xxlJobInfo.setExecutorHandler(JOB_EXECUTOR_HANDLER);
        xxlJobInfo.setExecutorParam(departId);
        xxlJobInfo.setExecutorBlockStrategy(JOB_EXECUTOR_BLOCK_STRATEGY);
        xxlJobInfo.setExecutorTimeout(JOB_EXECUTOR_TIMEOUT);
        xxlJobInfo.setExecutorFailRetryCount(JOB_EXECUTOR_FAIL_RETRY_COUNT);
        xxlJobInfo.setGlueType(JOB_GLUE_TYPE);
        xxlJobInfo.setGlueRemark(JOB_GLUE_REMARK);
        xxlJobInfo.setTriggerStatus(JOB_TRIGGER_STATUS);
        xxlJobInfo.setTriggerLastTime(JOB_TRIGGER_LAST_TIME);
        xxlJobInfo.setTriggerNextTime(JOB_TRIGGER_NEXT_TIME);
        
        return xxlJobInfo;
    }
    
    /**
     * 处理任务保存响应
     * 
     * @param saveResponse 保存响应
     * @return 处理结果
     */
    private Result<?> processJobSaveResponse(Resp saveResponse) {
        if (saveResponse.getRespBody() == null) {
            return Result.OK();
        }
        
        try {
            JSONObject jsonBody = (JSONObject) JSONObject.toJSON(saveResponse.getRespBody());
            Integer code = jsonBody.getInteger("code");
            
            if (code != null && code == HTTP_CODE_SUCCESS) {
                return Result.OK(jsonBody.getInteger("content"));
            } else {
                return Result.error(jsonBody.getString("msg"));
            }
        } catch (Exception e) {
            log.error("处理任务保存响应失败", e);
            return Result.error("处理任务保存响应失败");
        }
    }
}
