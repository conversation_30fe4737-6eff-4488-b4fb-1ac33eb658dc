package org.jeecg.modules.system.service.impl.ldap;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.StringUtils;
import org.jeecg.modules.api.IOscaBaseAPI;
import org.jeecg.modules.api.IScapBaseAPI;
import org.jeecg.modules.ldap.model.OrganizationUnit;
import org.jeecg.modules.ldap.service.LdapSynchronizationService;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.entity.SysUserDepart;
import org.jeecg.modules.system.service.impl.SysDepartServiceImpl;
import org.jeecg.modules.system.service.impl.SysUserDepartServiceImpl;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.jeecg.modules.system.service.impl.ldap.LdapConstants.*;

/**
 * LDAP部门同步服务
 * 负责LDAP部门/组织单元的同步操作
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class LdapDepartmentSyncService {
    
    @Autowired
    private LdapSynchronizationService ldapSynchronizationService;
    
    @Autowired
    private LdapConnectionService connectionService;
    
    @Autowired
    private LdapConfigurationService configurationService;
    
    @Autowired
    private SysDepartServiceImpl sysDepartService;
    
    @Autowired
    private SysUserDepartServiceImpl sysUserDepartService;
    
    @Autowired
    private ISysUserService sysUserService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private IScapBaseAPI scapBaseAPI;
    
    @Autowired
    private IOscaBaseAPI oscaBaseAPI;
    
    /**
     * 同步公司/组织结构
     * 
     * @return 同步结果
     */
    public Result<?> syncCompany() {
        try {
            // 1. 从LDAP中获取组织单元列表
            List<OrganizationUnit> organizationUnits = getOrganizationUnits();
            
            // 2. 检查顶级群组是否存在重名
            Result<String> checkResult = checkDepartmentNameConflicts(organizationUnits);
            if (!checkResult.isSuccess()) {
                return Result.error(ERROR_DUPLICATE_DEPART_NAME + checkResult.getMessage() + "，群组同步失败");
            }
            
            // 3. 将LDAP的群组添加到系统中
            Set<String> syncedDepartIds = addOrganizationUnitsToSystem(organizationUnits);
            
            // 4. 删除LDAP中不存在的域群组和用户
            removeUnusedDepartmentsAndUsers(syncedDepartIds);
            
            // 5. 清空群组缓存
            clearDepartmentCache();
            
            return Result.OK();
            
        } catch (Exception e) {
            log.error(LOG_SYNC_DEPART_EXCEPTION, e);
            return Result.error(ERROR_LDAP_CONFIG);
        }
    }
    
    /**
     * 检查部门名称冲突
     * 
     * @param organizationUnits 组织单元列表
     * @return 检查结果
     */
    public Result<String> checkDepartmentNameConflicts(List<OrganizationUnit> organizationUnits) {
        // 获取系统中现有的顶级部门名称
        LambdaQueryWrapper<SysDepart> topDepartWrapper = new LambdaQueryWrapper<SysDepart>()
                .and(wrapper -> wrapper.ne(SysDepart::getThirdId, THIRD_ID_LDAP).or().isNull(SysDepart::getThirdId))
                .and(wrapper -> wrapper.eq(SysDepart::getParentId, "").or().isNull(SysDepart::getParentId))
                .select(SysDepart::getDepartName);
        
        List<SysDepart> topSysDeparts = sysDepartService.list(topDepartWrapper);
        Set<String> existingTopDepartNames = topSysDeparts.stream()
                .map(SysDepart::getDepartName)
                .collect(Collectors.toSet());
        
        // 检查是否有重名
        for (OrganizationUnit organizationUnit : organizationUnits) {
            String name = organizationUnit.getName() + OU_SUFFIX_MAP.get(organizationUnit.getType().value);
            if (existingTopDepartNames.contains(name)) {
                return Result.error(name);
            }
        }
        
        return Result.ok();
    }
    
    /**
     * 将组织单元添加到系统部门
     * 
     * @param organizationUnits 组织单元列表
     * @return 已同步的部门ID集合
     */
    public Set<String> addOrganizationUnitsToSystem(List<OrganizationUnit> organizationUnits) {
        // 查询系统中已有的域群组
        QueryWrapper<SysDepart> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(SysDepart::getThirdId, THIRD_ID_LDAP)
                .select(SysDepart::getId, SysDepart::getParentId,
                        SysDepart::getDepartNameAbbr, SysDepart::getDepartName);
        List<SysDepart> existingLdapDeparts = sysDepartService.list(queryWrapper);
        
        // 构建父部门-子部门映射
        Map<String, Set<SysDepart>> parentDepartMap = buildParentDepartmentMap(existingLdapDeparts);
        
        // 递归添加群组
        Set<String> departIds = new HashSet<>();
        for (OrganizationUnit unit : organizationUnits) {
            addOrganizationUnitToSystem(unit, "", parentDepartMap, departIds);
        }
        
        return departIds;
    }
    
    /**
     * 删除未使用的部门和用户
     * 
     * @param syncedDepartIds 已同步的部门ID集合
     */
    public void removeUnusedDepartmentsAndUsers(Set<String> syncedDepartIds) {
        QueryWrapper<SysDepart> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysDepart::getThirdId, THIRD_ID_LDAP);
        List<SysDepart> allLdapDeparts = sysDepartService.list(queryWrapper);
        
        if (CollectionUtils.isEmpty(allLdapDeparts)) {
            return;
        }
        
        List<String> unusedDepartIds = allLdapDeparts.stream()
                .map(SysDepart::getId)
                .filter(id -> !syncedDepartIds.contains(id))
                .collect(Collectors.toList());
        
        if (CollectionUtils.isEmpty(unusedDepartIds)) {
            return;
        }
        
        // 1. 移除同步定时任务
        sysDepartService.cleanSyncLdapUserTiming(unusedDepartIds);
        
        // 2. 移除同步中不存在的群组下的用户
        removeUsersInUnusedDepartments(unusedDepartIds);
        
        // 3. 彻底移除被删除群组下存在的逻辑删除用户
        removeLogicallyDeletedUsers(allLdapDeparts, syncedDepartIds);
        
        // 4. 移除本次同步不存在的域群组
        removeUnusedDepartments(unusedDepartIds);
        
        // 5. 移除项目与群组关联
        deleteRelations(String.join(",", unusedDepartIds));
    }
    
    /**
     * 获取组织单元列表
     * 
     * @return 组织单元列表
     */
    private List<OrganizationUnit> getOrganizationUnits() {
        connectionService.updateLdapConfiguration(configurationService.getLdapConfiguration());
        return ldapSynchronizationService.discoverOrganizationalUnitsAsTreeWithFilter("", "");
    }
    
    /**
     * 构建父部门映射
     * 
     * @param existingDeparts 现有部门列表
     * @return 父部门映射
     */
    private Map<String, Set<SysDepart>> buildParentDepartmentMap(List<SysDepart> existingDeparts) {
        Map<String, Set<SysDepart>> parentDepartMap = new HashMap<>();
        for (SysDepart depart : existingDeparts) {
            String parentId = StringUtils.isNotBlank(depart.getParentId()) ? depart.getParentId() : "";
            Set<SysDepart> departIds = parentDepartMap.computeIfAbsent(parentId, k -> new HashSet<>());
            departIds.add(depart);
        }
        return parentDepartMap;
    }
    
    /**
     * 递归添加组织单元到系统
     * 
     * @param organizationUnit 组织单元
     * @param parentDepartId 父部门ID
     * @param parentDepartMap 父部门映射
     * @param departIds 部门ID集合
     */
    private void addOrganizationUnitToSystem(OrganizationUnit organizationUnit,
                                           String parentDepartId,
                                           Map<String, Set<SysDepart>> parentDepartMap,
                                           Set<String> departIds) {
        String username = getCurrentUsername();
        Set<SysDepart> departs = parentDepartMap.getOrDefault(parentDepartId, new HashSet<>());
        Map<String, SysDepart> nameToDepartMapping = departs.stream()
                .collect(Collectors.toMap(SysDepart::getDepartName, Function.identity(), (k1, k2) -> k2));
        
        String name = organizationUnit.getName();
        String newSuffix = OU_SUFFIX_MAP.get(organizationUnit.getType().value);
        SysDepart sysDepart;
        
        if (nameToDepartMapping.containsKey(name + ORIGINAL_OU_SUFFIX)) {
            // 更新旧格式的群组名
            SysDepart existDepart = nameToDepartMapping.get(name + ORIGINAL_OU_SUFFIX);
            existDepart.setDepartName(name + newSuffix);
            existDepart.setDepartNameAbbr(organizationUnit.getType().value);
            sysDepartService.updateDepartDataById(existDepart, username);
            sysDepart = existDepart;
        } else if (nameToDepartMapping.containsKey(name + newSuffix)) {
            sysDepart = nameToDepartMapping.get(name + newSuffix);
        } else {
            // 创建新的群组
            sysDepart = createNewDepartment(organizationUnit, parentDepartId, username);
        }
        
        // 递归处理子级
        if (organizationUnit.hasChildren()) {
            for (OrganizationUnit child : organizationUnit.getChildren()) {
                addOrganizationUnitToSystem(child, sysDepart.getId(), parentDepartMap, departIds);
            }
        }
        
        departIds.add(sysDepart.getId());
    }
    
    /**
     * 创建新部门
     * 
     * @param organizationUnit 组织单元
     * @param parentDepartId 父部门ID
     * @param username 操作用户名
     * @return 新创建的部门
     */
    private SysDepart createNewDepartment(OrganizationUnit organizationUnit, String parentDepartId, String username) {
        SysDepart sysDepart = new SysDepart();
        sysDepart.setParentId(parentDepartId);
        sysDepart.setId(UUID.randomUUID().toString().replace("-", ""));
        sysDepart.setDepartName(organizationUnit.getName() + OU_SUFFIX_MAP.get(organizationUnit.getType().value));
        sysDepart.setDepartNameAbbr(organizationUnit.getType().value);
        sysDepart.setDepartOrder(DEPART_ORDER_DEFAULT);
        sysDepart.setThirdId(THIRD_ID_LDAP);
        sysDepart.setThirdType(THIRD_TYPE_LDAP);
        sysDepart.setOrgCategory(StringUtils.isBlank(parentDepartId) ? String.valueOf(ORG_CATEGORY_TOP) : String.valueOf(ORG_CATEGORY_SUB));
        sysDepartService.saveDepartData(sysDepart, username);
        return sysDepart;
    }
    
    /**
     * 移除未使用部门中的用户
     * 
     * @param unusedDepartIds 未使用的部门ID列表
     */
    private void removeUsersInUnusedDepartments(List<String> unusedDepartIds) {
        QueryWrapper<SysUserDepart> userDepartQuery = new QueryWrapper<>();
        userDepartQuery.lambda().in(SysUserDepart::getDepId, unusedDepartIds);
        List<SysUserDepart> sysUserDeparts = sysUserDepartService.list(userDepartQuery);
        
        List<String> userIds = sysUserDeparts.stream()
                .map(SysUserDepart::getUserId)
                .collect(Collectors.toList());
        
        if (CollectionUtils.isNotEmpty(userIds)) {
            sysUserService.removeByIds(userIds);
        }
        
        // 移除用户部门关联关系
        if (CollectionUtils.isNotEmpty(sysUserDeparts)) {
            sysUserDepartService.removeByIds(sysUserDeparts);
        }
    }
    
    /**
     * 移除逻辑删除的用户
     * 
     * @param allLdapDeparts 所有LDAP部门
     * @param syncedDepartIds 已同步的部门ID集合
     */
    private void removeLogicallyDeletedUsers(List<SysDepart> allLdapDeparts, Set<String> syncedDepartIds) {
        allLdapDeparts.stream()
                .filter(depart -> !syncedDepartIds.contains(depart.getId()))
                .map(SysDepart::getOrgCode)
                .forEach(orgCode -> {
                    List<SysUser> deletedUsers = sysUserDepartService.queryLogicDeletedUserByDepCode(orgCode, null);
                    List<String> deletedUserIds = deletedUsers.stream()
                            .map(SysUser::getId)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(deletedUserIds)) {
                        sysUserService.removeLogicDeleted(deletedUserIds);
                    }
                });
    }
    
    /**
     * 移除未使用的部门
     * 
     * @param unusedDepartIds 未使用的部门ID列表
     */
    private void removeUnusedDepartments(List<String> unusedDepartIds) {
        QueryWrapper<SysDepart> departQuery = new QueryWrapper<>();
        departQuery.lambda().eq(SysDepart::getThirdId, THIRD_ID_LDAP);
        departQuery.lambda().in(SysDepart::getId, unusedDepartIds);
        sysDepartService.remove(departQuery);
    }
    
    /**
     * 删除项目与群组关联
     * 
     * @param ids 逗号分隔的群组ID字符串
     */
    private void deleteRelations(String ids) {
        scapBaseAPI.doDeleteRelation(ids);
        oscaBaseAPI.doDeleteRelation(ids);
    }
    
    /**
     * 清空部门缓存
     */
    private void clearDepartmentCache() {
        Set<String> departCacheKeys = redisTemplate.keys(CacheConstant.SYS_DEPARTS_CACHE + "*");
        Set<String> departIdCacheKeys = redisTemplate.keys(CacheConstant.SYS_DEPART_IDS_CACHE + "*");
        if (CollectionUtils.isNotEmpty(departCacheKeys)) {
            redisTemplate.delete(departCacheKeys);
        }
        if (CollectionUtils.isNotEmpty(departIdCacheKeys)) {
            redisTemplate.delete(departIdCacheKeys);
        }
    }
    
    /**
     * 获取当前操作用户名
     * 
     * @return 用户名
     */
    private String getCurrentUsername() {
        try {
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            return sysUser.getUsername();
        } catch (Exception e) {
            return "";
        }
    }
}
