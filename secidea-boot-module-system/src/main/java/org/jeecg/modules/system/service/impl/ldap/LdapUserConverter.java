package org.jeecg.modules.system.service.impl.ldap;

import org.jeecg.modules.ldap.model.LdapUser;
import org.jeecg.modules.system.entity.Person;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * LDAP用户转换器
 * 负责LDAP用户对象与系统用户对象之间的转换
 *
 * <AUTHOR>
 */
@Component
public class LdapUserConverter {

    /**
     * 将LdapUser转换为Person对象
     *
     * @param ldapUser LDAP用户对象
     * @return Person对象，如果输入为null则返回null
     */
    public Person convertToPerson(LdapUser ldapUser) {
        if (Objects.isNull(ldapUser)) {
            return null;
        }

        Person person = new Person();
        person.setLoginName(ldapUser.getUsername());
        person.setUserName(ldapUser.getFullName());
        person.setEmail(ldapUser.getEmail());
        person.setTelephoneNumber(ldapUser.getTelephoneNumber());
        person.setDistinguishedName(ldapUser.getDistinguishedName());
        person.setDescription(ldapUser.getDescription());
        person.setCompany(ldapUser.getCompany());
        person.setDepartment(ldapUser.getDepartment());

        return person;
    }
}
