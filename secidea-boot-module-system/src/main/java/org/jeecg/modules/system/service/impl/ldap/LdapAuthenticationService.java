package org.jeecg.modules.system.service.impl.ldap;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.PasswordUtil;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.StringUtils;
import org.jeecg.common.util.encryption.AesEncryptUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.base.service.BaseCommonService;
import org.jeecg.modules.system.controller.LoginController;
import org.jeecg.modules.system.entity.SysLdap;
import org.jeecg.modules.system.entity.SysRole;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.model.LdapConnectTestModel;
import org.jeecg.modules.system.model.SysLoginModel;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.system.service.impl.SysRoleServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.jeecg.modules.system.service.impl.ldap.LdapConstants.*;

/**
 * LDAP认证服务
 * 负责LDAP用户的身份验证和登录处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LdapAuthenticationService {

    @Autowired
    private LdapConfigurationService configurationService;

    @Autowired
    private LdapConnectionService connectionService;

    @Autowired
    private LdapUserSyncService userSyncService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private SysRoleServiceImpl sysRoleService;

    @Autowired
    private LoginController loginController;

    @Autowired
    private BaseCommonService baseCommonService;

    @Autowired
    private RedisUtil redisUtil;

    @Value("${login.check.password:true}")
    private boolean loginCheckPassword;

    /**
     * LDAP用户认证
     *
     * @param sysLoginModel 登录模型
     * @return 认证结果
     */
    public Result<JSONObject> authenticate(SysLoginModel sysLoginModel) {
        Result<JSONObject> result = new Result<>();

        // 验证验证码
        Result<JSONObject> checkCodeResult = loginController.getCheckCodeResult(sysLoginModel);
        if (checkCodeResult != null) {
            return checkCodeResult;
        }

        // 检查LDAP是否开启
        if (!configurationService.isLdapEnabled()) {
            result.error500(ERROR_LDAP_NOT_ENABLED);
            return result;
        }

        // 获取配置参数
        Map<String, String> configParams = configurationService.getConfigParams(
                AD_VERIFICATION_KEY, AD_FROZEN_KEY);
        String adVerification = configParams.get(AD_VERIFICATION_KEY);
        String adFrozen = configParams.get(AD_FROZEN_KEY);

        String username = sysLoginModel.getUsername();
        String password = decryptPassword(sysLoginModel.getPassword());

        if (Objects.isNull(password)) {
            result.error500(ERROR_USERNAME_PASSWORD);
            return result;
        }

        // 获取或创建用户
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getUsername, username);
        SysUser sysUser = sysUserService.getOne(queryWrapper);

        LoginUser loginUser = new LoginUser();
        loginUser.setUsername(username);

        // 验证现有用户
        if (Objects.nonNull(sysUser)) {
            Result<JSONObject> userValidationResult = validateExistingUser(sysUser, username, adVerification,
                                                                           loginUser);
            if (userValidationResult != null) {
                return userValidationResult;
            }
        }

        // LDAP认证
        Result<?> ldapAuthResult = performLdapAuthentication(username, password);
        if (!ldapAuthResult.isSuccess()) {
            result.error500(ERROR_USERNAME_PASSWORD);
            logLoginFailure(username, LOG_LOGIN_FAILED_USERNAME_PASSWORD, loginUser);
            return result;
        }

        // 处理用户登录
        if (sysUser == null) {
            sysUser = handleNewUserLogin(username, password, adVerification, adFrozen, loginUser);
            if (sysUser == null) {
                return result; // 错误已在方法内设置
            }
        }

        // 更新用户密码（首次LDAP登录）
        updateUserPasswordIfNeeded(sysUser, password);

        // 登录成功处理
        handleSuccessfulLogin(sysUser, username, result);

        return result;
    }

    /**
     * 解密密码
     *
     * @param encryptedPassword 加密的密码
     * @return 解密后的密码，解密失败返回null
     */
    private String decryptPassword(String encryptedPassword) {
        try {
            String password = AesEncryptUtil.desEncrypt(encryptedPassword.replaceAll("%2B", "\\+")).trim();
            return StringUtils.isNotEmpty(password) ? password : null;
        } catch (Exception e) {
            log.error("密码解密失败", e);
            return null;
        }
    }

    /**
     * 验证现有用户
     *
     * @param sysUser        系统用户
     * @param username       用户名
     * @param adVerification AD验证配置
     * @param loginUser      登录用户
     * @return 验证结果，null表示验证通过
     */
    private Result<JSONObject> validateExistingUser(SysUser sysUser, String username,
                                                    String adVerification, LoginUser loginUser) {
        // 检查非LDAP用户
        if (STATUS_ENABLED.equals(adVerification) && !THIRD_ID_LDAP.equals(sysUser.getThirdId())) {
            Result<JSONObject> result = new Result<>();
            result.error500(ERROR_ACCOUNT_ABNORMAL);
            logLoginFailure(username, LOG_LOGIN_FAILED_ACCOUNT_ABNORMAL, loginUser);
            return result;
        }

        // 检查登录错误次数
        if (sysUserService.getLoginUserErrorCount(username) >= MAX_LOGIN_ERROR_COUNT) {
            sysUser.setStatus(2);
            sysUserService.updateById(sysUser);
            sysUserService.updateLoginUserErrorCount(username, -1);
        }

        // 校验用户是否有效
        Result<JSONObject> userValidResult = sysUserService.checkUserIsEffective(sysUser);
        if (loginCheckPassword && !userValidResult.isSuccess()) {
            return userValidResult;
        }

        return null;
    }

    /**
     * 执行LDAP认证
     *
     * @param username 用户名
     * @param password 密码
     * @return 认证结果
     */
    private Result<?> performLdapAuthentication(String username, String password) {
        SysLdap sysLdap = configurationService.getLdapConfiguration();

        LdapConnectTestModel connectProperties = new LdapConnectTestModel();
        connectProperties.setAdAddress(sysLdap.getAdAddress())
                .setLdapServerType(String.valueOf(sysLdap.getLdapServerType().getValue()))
                .setAdPort(sysLdap.getAdPort())
                .setLdapEncryptType(String.valueOf(sysLdap.getLdapEncryptType().getValue()))
                .setAdName(username)
                .setAdPasswd(password)
                .setAddomainName(sysLdap.getAddomainName());

        return connectionService.testConnection(connectProperties);
    }

    /**
     * 处理新用户登录
     *
     * @param username       用户名
     * @param password       密码
     * @param adVerification AD验证配置
     * @param adFrozen       AD冻结配置
     * @param loginUser      登录用户
     * @return 创建的用户，失败返回null
     */
    private SysUser handleNewUserLogin(String username, String password, String adVerification,
                                       String adFrozen, LoginUser loginUser) {
        // 检查是否允许未同步用户登录
        if (STATUS_ENABLED.equals(adVerification)) {
            Result<JSONObject> result = new Result<>();
            result.error500(ERROR_ACCOUNT_NOT_SYNCED);
            return null;
        }

        // 确定用户状态
        int status = STATUS_ENABLED.equals(adVerification) ? 1 :
                (STATUS_DISABLED.equals(adVerification) && STATUS_DISABLED.equals(adFrozen) ? 2 : 1);

        // 检查逻辑删除的用户
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUser::getUsername, username);
        List<SysUser> deletedUsers = sysUserService.queryLogicDeleted(wrapper);
        if (!deletedUsers.isEmpty()) {
            Result<JSONObject> result = new Result<>();
            result.error500(ERROR_USER_DELETED);
            logLoginFailure(username, LOG_LOGIN_FAILED_USER_DELETED, loginUser);
            return null;
        }

        // 获取默认角色
        SysLdap sysLdap = configurationService.getLdapConfiguration();
        LambdaQueryWrapper<SysRole> roleQuery = new LambdaQueryWrapper<>();
        roleQuery.in(SysRole::getRoleCode, Arrays.asList(sysLdap.getLdapRole().split(",")));
        List<SysRole> defaultRoles = sysRoleService.list(roleQuery);

        if (defaultRoles == null) {
            Result<JSONObject> result = new Result<>();
            result.error500(ERROR_NO_DEFAULT_ROLE);
            logLoginFailure(username, LOG_LOGIN_FAILED_NO_DEFAULT_ROLE, loginUser);
            return null;
        }

        // 创建用户
        SysUser newUser = createNewUser(defaultRoles, sysLdap.getLdapDepart(), username, password, status);
        if (newUser == null) {
            Result<JSONObject> result = new Result<>();
            result.error500(ERROR_NO_DEFAULT_DEPART);
            logLoginFailure(username, LOG_LOGIN_FAILED_NO_DEFAULT_DEPART, loginUser);
            return null;
        }

        if (status == 2) {
            Result<JSONObject> result = new Result<>();
            result.error500(ERROR_ACCOUNT_FROZEN);
            return null;
        }

        return newUser;
    }

    /**
     * 更新用户密码（如果需要）
     *
     * @param sysUser  系统用户
     * @param password 密码
     */
    private void updateUserPasswordIfNeeded(SysUser sysUser, String password) {
        if (sysUser.getPassword() == null || StringUtils.isNotBlank(sysUser.getSalt())) {
            String salt = oConvertUtils.randomGen(SALT_LENGTH);
            sysUser.setSalt("");
            String passwordEncode = PasswordUtil.encrypt(sysUser.getUsername(), password, salt);
            sysUser.setPassword(passwordEncode);
            sysUserService.updateById(sysUser);
        }
    }

    /**
     * 处理成功登录
     *
     * @param sysUser  系统用户
     * @param username 用户名
     * @param result   结果对象
     */
    private void handleSuccessfulLogin(SysUser sysUser, String username, Result<JSONObject> result) {
        // 登录成功，错误次数清零
        redisUtil.set(username, 0);

        // 设置用户登录信息
        loginController.userInfo(sysUser, result, false, true);

        // 记录登录日志
        LoginUser user = new LoginUser();
        BeanUtils.copyProperties(sysUser, user);
        baseCommonService.addLog(String.format(LOG_LOGIN_SUCCESS, username), CommonConstant.LOG_TYPE_1, null, user);
    }

    /**
     * 创建新用户
     *
     * @param defaultRoles 默认角色列表
     * @param ldapDepart   LDAP部门
     * @param username     用户名
     * @param password     密码
     * @param status       状态
     * @return 创建的用户
     */
    private SysUser createNewUser(List<SysRole> defaultRoles, String ldapDepart, String username,
                                  String password, int status) {
        return userSyncService.saveUser(defaultRoles, ldapDepart, username, password, status, null);
    }

    /**
     * 记录登录失败日志
     *
     * @param username    用户名
     * @param logTemplate 日志模板
     * @param loginUser   登录用户
     */
    private void logLoginFailure(String username, String logTemplate, LoginUser loginUser) {
        baseCommonService.addLog(String.format(logTemplate, username), CommonConstant.LOG_TYPE_1, null, loginUser);
    }
}
