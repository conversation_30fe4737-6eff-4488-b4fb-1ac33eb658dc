package org.jeecg.modules.system.service;


import com.alibaba.fastjson.JSONObject;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.system.entity.Person;
import org.jeecg.modules.system.model.LdapConnectTestModel;
import org.jeecg.modules.system.model.LdapModel;
import org.jeecg.modules.system.model.SysLoginModel;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.core.support.LdapContextSource;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

public interface ILdapService {

    LdapContextSource contextSource();

    LdapTemplate ldapTemplate(LdapContextSource contextSource);

    Result<?> sczCompany();

    Result<?> verification(String departId);

    @Async
    void sczUser(int status, String departId, LdapModel ldapModel);

    Result<?> timingDepartUser(int adTimingOpen, String adCronExpression, String adParentId, Integer adXxlJobInfoId);

    List<Person> findUsersFromOrgnizationalUnit(String base);
    /**
     * LDAP用户认证
     */
    Result<JSONObject> authenticate(SysLoginModel sysLoginModel);

    /**
     * 检索域用户
     */
    Person searchLdapUser(String keyword);

    /**
     * 通过连接信息（认证协议、地址、端口、用户密码）测试 ldap 服务连接
     * @param connectProperties 连接信息
     * @return 测试连接结果
     */
    Result<?> testLdapConnect(LdapConnectTestModel connectProperties);
}
