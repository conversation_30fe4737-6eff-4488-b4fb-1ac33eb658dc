package org.jeecg.modules.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.StringUtils;
import org.jeecg.modules.system.entity.Person;
import org.jeecg.common.system.vo.SysConfigParam;
import org.jeecg.modules.system.model.LdapConnectTestModel;
import org.jeecg.modules.system.model.LdapModel;
import org.jeecg.modules.system.model.SysLoginModel;
import org.jeecg.modules.system.service.ILdapService;
import org.jeecg.modules.system.service.ISysConfigParamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.core.support.LdapContextSource;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sys/ldap")
@Tag(name = "ldap用户登录")
public class LdapController {
    @Autowired
    private ILdapService ILdapService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private ISysConfigParamService sysConfigParamService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ISysBaseAPI sysBaseAPI;

    @Operation(summary = "ldap登录接口")
    @PostMapping(value = "/authenticate")
    public Result<JSONObject> authenticate(@RequestBody SysLoginModel sysLoginModel){
        return ILdapService.authenticate(sysLoginModel);
    }

    @Operation(summary = "ldap通过名称查询")
    @PostMapping(value = "/searchLdapUser")
    public Person searchLdapUser(@RequestParam(name = "username") String name){
        return ILdapService.searchLdapUser(name);
    }

    @Operation(summary = "同步ldap单位")
    @PostMapping(value = "/sczCompany")
    public Result<?> sczCompany() {
        String key = "sys:ldap:sczCompany";
        if (redisUtil.get(key) != null){
            return Result.error("正在同步ldap单位，请稍等！");
        }
        redisUtil.set(key,"",3000);
        try {
            QueryWrapper<SysConfigParam> sysConfigParamQueryWrapper = new QueryWrapper<>();
            sysConfigParamQueryWrapper.lambda()
                .in(SysConfigParam::getParamKey, "adVerification", "adOpen");
            List<SysConfigParam> sysConfigParams = sysConfigParamService.list(
                sysConfigParamQueryWrapper);
            String adVerification = "";
            String adOpen = "";
            for (SysConfigParam sysConfigParam : sysConfigParams) {
                if ("adVerification".equals(sysConfigParam.getParamKey())) {
                    adVerification = sysConfigParam.getParamValue();
                }
                if ("adOpen".equals(sysConfigParam.getParamKey())) {
                    adOpen = sysConfigParam.getParamValue();
                }
            }
            if (!"1".equals(adOpen)) {
                return Result.error("未开启LDAP服务,请联系管理员！");
            }
            if (!"1".equals(adVerification)) {
                return Result.error("请关闭LDAP配置中【允许未同步用户登录】！");
            }
            return ILdapService.sczCompany();
        } finally {
            redisUtil.del(key);
        }
    }

    @Operation(summary = "同步ldap用户")
    @GetMapping(value = "/sczUser")
    public Result<?> sczUser(@RequestParam(name="status") int status,@RequestParam(name="departId") String departId) {
        String key = "sys:ldap:sczUser";
        if (redisUtil.get(key) != null){
            return Result.error("正在同步ldap用户，请稍等！");
        }
        String adOpen = sysConfigParamService.getSysConfigParamByKey("adOpen");
        if (!"1".equals(adOpen)) {
            return Result.error("未开启LDAP服务,请联系管理员！");
        }
        redisUtil.set(key,"",3000);
        try {
            Result<?> verification = ILdapService.verification(departId);
            if (verification.getCode() != 200) {
                return verification;
            }
            ObjectMapper objectMapper = new ObjectMapper();
            LdapModel ldapModel = objectMapper.convertValue(verification.getResult(),
                LdapModel.class);
            ILdapService.sczUser(status, departId, ldapModel);
            return Result.OK("正在同步用户，请稍后查询！");
        } finally {
            redisUtil.del("sys:ldap:sczUser");
        }
    }

    @XxlJob(value = "timingDepartUserXxl")
    public ReturnT<String> timingDepartUserXxl() {
        String[] params = XxlJobHelper.getJobParam().split(",");
        for (String param : params){
            Result<?> verification = ILdapService.verification(param);
            if (verification.getCode() != 200){
                return ReturnT.FAIL;
            }

            ObjectMapper objectMapper = new ObjectMapper();
            LdapModel ldapModel = objectMapper.convertValue(verification.getResult(), LdapModel.class);
            String adUserStatus = sysBaseAPI.getSysConfigParamByKey("adUserStatus");
            if (StringUtils.isBlank(adUserStatus)){
                adUserStatus = "2";
            }
            ILdapService.sczUser(Integer.parseInt(adUserStatus), param, ldapModel);
        }
        return ReturnT.SUCCESS;
    }

    @Operation(summary = "定时任务同步ldap用户")
    @GetMapping(value = "/timingDepartUser")
    public Result<?> timingDepartUser(@RequestParam(name = "adTimingOpen") int adTimingOpen, @RequestParam(name = "adCronExpression", required = false) String adCronExpression, @RequestParam(name = "adParentId", required = false) String adParentId, @RequestParam(name = "adXxlJobInfoId", required = false) Integer adXxlJobInfoId) {
        return ILdapService.timingDepartUser(adTimingOpen, adCronExpression, adParentId, adXxlJobInfoId);
    }


    @Operation(summary = "测试连接")
    @PostMapping(value = "/testadField")
    public Result<?> testadField(@RequestBody LdapConnectTestModel ldapConnectTestModel) {
        return ILdapService.testLdapConnect(ldapConnectTestModel);
    }

    @PostMapping("/getVersion")
    public void reloadInstance(){
        //获取上下文
        DefaultListableBeanFactory defaultListableBeanFactory =
                (DefaultListableBeanFactory)applicationContext.getAutowireCapableBeanFactory();
        //销毁指定实例 execute是上文注解过的实例名称 name="execute"
        defaultListableBeanFactory.destroySingleton("contextSource");
        LdapContextSource ldapContextSource = ILdapService.contextSource();
        defaultListableBeanFactory.registerSingleton("contextSource", ldapContextSource);

        defaultListableBeanFactory.destroySingleton("template");
        //按照旧有的逻辑重新获取实例,Excute是我自己逻辑中的类
        LdapTemplate ldapTemplate = ILdapService.ldapTemplate(ldapContextSource);
        //重新注册同名实例，这样在其他地方注入的实例还是同一个名称，但是实例内容已经重新加载
        defaultListableBeanFactory.registerSingleton("template", ldapTemplate);
    }
}
