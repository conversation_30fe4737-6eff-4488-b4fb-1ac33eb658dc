package org.jeecg.modules.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.dto.message.XxlJobInfo;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.vo.SysConfigParam;
import org.jeecg.common.util.*;
import org.jeecg.common.util.encryption.AesEncryptUtil;
import org.jeecg.common.xxljob.entity.Resp;
import org.jeecg.common.xxljob.service.XxlJobService;
import org.jeecg.modules.api.IOscaBaseAPI;
import org.jeecg.modules.api.IScapBaseAPI;
import org.jeecg.modules.base.service.BaseCommonService;
import org.jeecg.modules.ldap.config.LdapAttributeMapping;
import org.jeecg.modules.ldap.config.LdapServerType;
import org.jeecg.modules.ldap.model.LdapUser;
import org.jeecg.modules.ldap.model.OrganizationUnit;
import org.jeecg.modules.ldap.service.LdapSynchronizationService;
import org.jeecg.modules.ldap.service.LdapUserService;
import org.jeecg.modules.ldap.util.LdapDnUtils;
import org.jeecg.modules.system.controller.LoginController;
import org.jeecg.modules.system.entity.*;
import org.jeecg.modules.system.entity.SysLdap.AttrMapperField;
import org.jeecg.modules.system.entity.SysLdap.EncryptType;
import org.jeecg.modules.system.entity.SysLdap.ServerType;
import org.jeecg.modules.system.model.LdapConnectTestModel;
import org.jeecg.modules.system.model.LdapModel;
import org.jeecg.modules.system.model.SysLoginModel;
import org.jeecg.modules.system.service.ILdapService;
import org.jeecg.modules.system.service.ISysConfigParamService;
import org.jeecg.modules.system.service.ISysUserRoleService;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.system.util.ldap.NonVerifyingSSLSocketFactory;
import org.jeecg.modules.system.util.ldap.SSLLdapContextSource;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.ldap.core.*;
import org.springframework.ldap.core.support.LdapContextSource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.naming.AuthenticationException;
import javax.naming.Context;
import javax.naming.NamingException;
import javax.naming.directory.*;
import java.util.*;
import java.util.stream.Collectors;

import static org.jeecg.common.system.util.AuthCommonUtil.getLoginUser;

@Service
@Slf4j
public class LdapServiceImpl implements ILdapService {
    @Autowired
    private LdapSynchronizationService ldapSynchronizationService;
    @Autowired
    private LdapUserService ldapUserService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private LoginController loginController;
    @Autowired
    private SysRoleServiceImpl sysRoleService;
    @Autowired
    private SysDepartServiceImpl sysDepartService;
    @Autowired
    private SysUserDepartServiceImpl sysUserDepartService;
    @Value("${login.check.password:true}")
    private boolean loginCheckPassword;
    @Resource
    private BaseCommonService baseCommonService;
    @Autowired
    public RedisTemplate<String, Object> redisTemplate;
    @Resource
    private ISysConfigParamService sysConfigParamService;
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    @Autowired
    private XxlJobService xxlJobService;
    @Autowired
    private ISysUserRoleService sysUserRoleService;
    @Autowired
    private IScapBaseAPI scapBaseAPI;
    @Autowired
    private IOscaBaseAPI oscaBaseAPI;

    private final String LDAP_OVERWRITE_SYS_USER_KEY = "ldapOverwriteSysUser";

    /**
     * 匹配DN中的o(或ou)和名称
     */
    private static final Pattern ouPattern = Pattern.compile("^(?i)(o|ou)=([^,]+)");

    private final String oPrefix = "o";
    private final String ouPrefix = "ou";
    private final String oSuffix = "（域群组_o）";
    private final String ouSuffix = "（域群组_ou）";

    /**
     * 历史版本OU在系统中的后缀
     */
    private final String originalOuSuffix = "（域群组）";

    private final Map<String, String> ouSuffixMap = new HashMap<String, String>() {{
        put(oPrefix, oSuffix);
        put(ouPrefix, ouSuffix);
    }};

    @Override
    public LdapContextSource contextSource() {
        SysLdap sysLdap = sysLdapParameter();
        LdapContextSource contextSource;
        String urlPrefix;
        if (sysLdap.getLdapEncryptType() == EncryptType.LDAP) {
            contextSource = new LdapContextSource();
            urlPrefix = "ldap://";
        } else {
            contextSource = new SSLLdapContextSource();
            urlPrefix = "ldaps://";
        }
        contextSource.setUrl(urlPrefix + sysLdap.getAdAddress() + ":" + sysLdap.getAdPort());
        String addomainName = sysLdap.getAddomainName();
        contextSource.setBase(addomainName);
        String adName = sysLdap.getAdName();
        if (StringUtils.isNotBlank(addomainName)) {
            String userDN = LdapDnUtils.extractAdminLoginName(adName, addomainName);
            contextSource.setUserDn(userDN);
        }
        String apPassword = Optional.ofNullable(sysLdap.getAdPasswd()).orElse("");
        contextSource.setPassword(apPassword);
        contextSource.setPooled(true);
        contextSource.afterPropertiesSet();

        // 乱码问题
        Map<String, Object> config = new HashMap<>();
        config.put("java.naming.ldap.attributes.binary", "objectGUID");
        contextSource.setBaseEnvironmentProperties(config);

        return contextSource;
    }

    @Override
    public LdapTemplate ldapTemplate(LdapContextSource contextSource) {
        return new LdapTemplate(contextSource);
    }

    @Override
    public List<Person> findUsersFromOrgnizationalUnit(String base) {
        SysLdap sysLdap = sysLdapParameter();
        ldapConfigUpdate(sysLdap);
        List<LdapUser> ldapUsers = ldapSynchronizationService.getUsersFromOrganizationalUnit(base,
                                                                                             sysLdap.getLdapSyncFilter());
        return ldapUsers.stream().map(this::ldapUserToPerson).collect(Collectors.toList());
    }

    private Person ldapUserToPerson(LdapUser ldapUser) {
        if (Objects.isNull(ldapUser)) {
            return null;
        }
        Person person = new Person();
        person.setLoginName(ldapUser.getUsername());
        person.setUserName(ldapUser.getFullName());
        person.setEmail(ldapUser.getEmail());
        person.setTelephoneNumber(ldapUser.getTelephoneNumber());
        person.setDistinguishedName(ldapUser.getDistinguishedName());
        person.setDescription(ldapUser.getDescription());
        person.setCompany(ldapUser.getCompany());
        person.setDepartment(ldapUser.getDepartment());
        return person;
    }

    @Override
    public Result<?> sczCompany() {

        // 1、从ldap中获取ou列表，以树的方式返回
        List<OrganizationUnit> organizationUnits;
        try {
            ldapConfigUpdate(sysLdapParameter());
            organizationUnits = ldapSynchronizationService.discoverOrganizationalUnitsAsTreeWithFilter("", "");
        } catch (Exception e) {
            log.info("ad同步用户系统异常", e);
            return Result.error("请检查LDAP服务配置是否正确！需要配置地址、端口、节点、管理员DN、密码");
        }

        // 2、检查顶级群组是否存在重名
        Result<String> checkResult = checkDepartName(organizationUnits);
        if (!checkResult.isSuccess()) {
            return Result.error("存在同级重名群组" + checkResult.getMessage() + "，群组同步失败");
        }

        // 3、将ldap的群组添加到系统中
        Set<String> sysLdapDepartIds = addOrganizationUnitsToSysDepart(organizationUnits);

        //4、删除ldap中不存在的域群组和用户
        QueryWrapper<SysDepart> sysDepartQueryWrapper = new QueryWrapper<>();
        sysDepartQueryWrapper.lambda().eq(SysDepart::getThirdId, "1");
        List<SysDepart> sysDeparts = sysDepartService.list(sysDepartQueryWrapper);
        if (CollectionUtils.isNotEmpty(sysDeparts)) {
            removeUnusedDepartAndUser(sysDeparts, sysLdapDepartIds);
        }
        //5、清空群组缓存
        Set<String> keys3 = redisTemplate.keys(CacheConstant.SYS_DEPARTS_CACHE + "*");
        Set<String> keys4 = redisTemplate.keys(CacheConstant.SYS_DEPART_IDS_CACHE + "*");
        redisTemplate.delete(keys3);
        redisTemplate.delete(keys4);

        return Result.OK();
    }

    private void removeUnusedDepartAndUser(List<SysDepart> sysDeparts, Set<String> savedLdapDepartIds) {
        List<String> unusedDepartIds = sysDeparts.stream().map(SysDepart::getId).collect(Collectors.toList());
        unusedDepartIds.removeAll(savedLdapDepartIds);
        if (CollectionUtils.isEmpty(unusedDepartIds)) {
            return;
        }
        // 1.移除同步定时任务
        sysDepartService.cleanSyncLdapUserTiming(unusedDepartIds);
        // 2.移除同步中不存在的群组下的用户
        QueryWrapper<SysUserDepart> sysUserDepartQueryWrapper = new QueryWrapper<>();
        sysUserDepartQueryWrapper.lambda().in(SysUserDepart::getDepId, unusedDepartIds);
        List<SysUserDepart> sysUserDeparts = sysUserDepartService.list(sysUserDepartQueryWrapper);
        List<String> userIds = sysUserDeparts.stream().map(SysUserDepart::getUserId).collect(Collectors.toList());
        sysUserService.removeByIds(userIds);
        // 3.彻底移除被删除群组下存在的逻辑删除用户
        sysDeparts.stream()
                .filter(sysDepart -> !savedLdapDepartIds.contains(sysDepart.getId()))
                .map(SysDepart::getOrgCode)
                .forEach(orgCode -> {
                    List<SysUser> deletedUserList = sysUserDepartService.queryLogicDeletedUserByDepCode(orgCode, null);
                    List<String> deletedUserIds =
                            deletedUserList.stream().map(SysUser::getId).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(deletedUserIds)) {
                        sysUserService.removeLogicDeleted(deletedUserIds);
                    }
                });

        // 4.移除本次同步不存在的域群组
        QueryWrapper<SysDepart> notUsedDepartQueryWrapper = new QueryWrapper<>();
        notUsedDepartQueryWrapper.lambda().eq(SysDepart::getThirdId, "1");
        notUsedDepartQueryWrapper.lambda().in(SysDepart::getId, unusedDepartIds);
        sysDepartService.remove(notUsedDepartQueryWrapper);
        // 5.移除不存在域群组和用户关联关系
        sysUserDepartService.removeByIds(sysUserDeparts);
        // 移除项目与群组关联
        doDeleteRelation(String.join(",", unusedDepartIds));
    }

    /**
     * 校验域群组的顶级群组是否在系统中有重名的情况
     * 仅校验同步保存为顶级群组的域群组，域群组下级的同名情况由LDAP服务保证
     * 如果同级下重名，则DN会完全一致，因此无法从LDAP服务上创建
     *
     * @param organizationUnits 本次同步从ldap服务器中获取的群组
     * @return 无重名时返回成功，重名时返回第一个重名的群组名
     */
    private Result<String> checkDepartName(List<OrganizationUnit> organizationUnits) {
        LambdaQueryWrapper<SysDepart> topSysDepartWrapper = new LambdaQueryWrapper<SysDepart>()
                .and(wrapper -> wrapper.ne(SysDepart::getThirdId, "1").or().isNull(SysDepart::getThirdId))
                .and(wrapper -> wrapper.eq(SysDepart::getParentId, "").or().isNull(SysDepart::getParentId))
                .select(SysDepart::getDepartName);
        List<SysDepart> topSysDeparts = sysDepartService.list(topSysDepartWrapper);
        Set<String> existTopSysDepartNames = topSysDeparts.stream()
                .map(SysDepart::getDepartName)
                .collect(Collectors.toSet());

        for (OrganizationUnit organizationUnit : organizationUnits) {
            // 系统中实际存储名字为 o/ou 名 + 对应后缀
            String name = organizationUnit.getName() + ouSuffixMap.get(organizationUnit.getType().value);
            if (existTopSysDepartNames.contains(name)) {
                return Result.error(name);
            }
        }
        return Result.ok();
    }

    /**
     * 删除项目与群组关联
     *
     * @param ids 逗号分隔的群组ids字符串
     */
    private void doDeleteRelation(String ids) {
        scapBaseAPI.doDeleteRelation(ids);
        oscaBaseAPI.doDeleteRelation(ids);
    }

    @Override
    public void sczUser(int status, String departId, LdapModel ldapModel) {
        List<Person> personList = ldapModel.getPersonList();
        List<SysRole> sysRoleList = ldapModel.getSysRoleList();
        SysDepart sysDepart = sysDepartService.getById(departId);
        //通过部门查找平台用户
        List<SysUser> sysUserList = sysUserDepartService.queryUserByDepCode(sysDepart.getOrgCode(), null);
        List<String> userNames = sysUserList.stream().map(SysUser::getUsername).collect(Collectors.toList());
        List<String> existence = new ArrayList<>();
        String ldapOverwriteSysUser = sysConfigParamService.getSysConfigParamByKey(LDAP_OVERWRITE_SYS_USER_KEY);
        boolean isOverwriteSysUser = "1".equals(ldapOverwriteSysUser);
        for (Person person : personList) {
            if (StringUtils.isEmpty(person.getLoginName())) {
                // 获取的用户名为空，不保存到系统
                continue;
            }
            try {
                String loginName = person.getLoginName();
                //查看ad域里用户是否存在
                if (userNames.contains(loginName)) {
                    existence.add(loginName);
                }

                LambdaQueryWrapper<SysUser> sysUserQueryWrapper = new LambdaQueryWrapper<>();
                sysUserQueryWrapper.eq(SysUser::getUsername, loginName);
                //是否逻辑删除
                List<SysUser> sysUsers = sysUserService.queryLogicDeleted(sysUserQueryWrapper.clone());
                if (!sysUsers.isEmpty()) {
                    //平台存在用户账号非ad域且不开启覆盖重名用户配置，不处理
                    if (!"1".equals(sysUsers.get(0).getThirdId()) && !isOverwriteSysUser) {
                        continue;
                    }
                    List<String> userIds = sysUsers.stream().map(SysUser::getId).collect(Collectors.toList());
                    SysUser updateEntity = new SysUser();
                    updateEntity.setUpdateTime(new Date());
                    sysUserService.revertLogicDeleted(userIds, updateEntity);
                }

                SysUser sysUser = sysUserService.getOne(sysUserQueryWrapper);
                if (sysUser != null) {
                    //平台存在用户账号非ad域且不开启覆盖重名用户配置，不处理
                    if (!"1".equals(sysUser.getThirdId()) && !isOverwriteSysUser) {
                        continue;
                    }
                    updateUser(loginName, sysUser, sysRoleList, status, person);
                    continue;
                }
                saveUser(sysRoleList, null, loginName, null, status, person);
            } catch (Exception e) {
                log.warn("同步ad域用户失败：{}", e.getMessage());
            }
        }
        userNames.removeAll(existence);
        //逻辑删除ad域中不存在的用户
        if (CollectionUtils.isNotEmpty(userNames)) {
            QueryWrapper<SysUser> sysUserQueryWrapper = new QueryWrapper<>();
            sysUserQueryWrapper.lambda().in(SysUser::getUsername, userNames);
            sysUserService.remove(sysUserQueryWrapper);
        }
        // 移除已经在回收站中的用户(逻辑删除的，且该域群组下已经不存在该用户)
        List<SysUser> logicDeletedUserList =
                sysUserDepartService.queryLogicDeletedUserByDepCode(sysDepart.getOrgCode(), null);
        List<String> deletedUserIds = logicDeletedUserList.stream().map(SysUser::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deletedUserIds)) {
            sysUserService.removeLogicDeleted(deletedUserIds);
        }
        log.info("同步ad域用户完成！");
        redisUtil.del("sys:ldap:sczUser");
    }

    @Override
    public Result<?> verification(String departId) {
        SysDepart sysDepart = sysDepartService.getById(departId);
        if (!"1".equals(sysDepart.getThirdId())) {
            return Result.error("无法同步本地群组中的用户，请选择域群组");
        }
        QueryWrapper<SysConfigParam> sysConfigParamQueryWrapper = new QueryWrapper<>();
        sysConfigParamQueryWrapper.lambda().in(SysConfigParam::getParamKey, "adVerification");
        List<SysConfigParam> sysConfigParams = sysConfigParamService.list(sysConfigParamQueryWrapper);
        String adVerification = "";
        for (SysConfigParam sysConfigParam : sysConfigParams) {
            if ("adVerification".equals(sysConfigParam.getParamKey())) {
                adVerification = sysConfigParam.getParamValue();
            }
        }
        if (!"1".equals(adVerification)) {
            return Result.error("请关闭LDAP配置中【允许未同步用户登录】！");
        }
        SysLdap sysLdap = sysLdapParameter();
        LambdaQueryWrapper<SysRole> sysRoleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysRoleLambdaQueryWrapper.in(SysRole::getRoleCode, Arrays.asList(sysLdap.getLdapRole().split(",")));
        List<SysRole> sysRoleList = sysRoleService.list(sysRoleLambdaQueryWrapper);
        if (sysRoleList == null) {
            return Result.error("同步失败，请联系管理员设置默认角色");
        }
        String addomainName = sysLdap.getAddomainName();
        // recursionBase 获取到顶层ou
        String base = recursionBase(departId, "").toLowerCase();
        base = LdapDnUtils.relativePath(addomainName, base);

        List<Person> personList;
        try {
            personList = this.findUsersFromOrgnizationalUnit(base);
        } catch (Exception e) {
            log.info("ad同步群组系统异常：" + e.getMessage());
            return Result.error("请检查LDAP服务配置是否正确！需要配置地址、端口、节点、管理员DN、密码");
        }
        LdapModel ldapModel = new LdapModel();
        ldapModel.setSysLdap(sysLdap);
        ldapModel.setSysRoleList(sysRoleList);
        ldapModel.setPersonList(personList);
        return Result.OK(ldapModel);
    }

    /**
     * 递归构建当前群组到顶级群组（无父级群组）的相对DN 如群组层级关系为 ou1 -> ou1_1 -> ou1_1_1 -> ou1_1_1_1, 而 departId 对应群组为
     * ou1_1_1 则构建的结果为ou1_1_1,ou1_1,ou1 注意，ldap 服务器中允许不同层级的OU属性值相同
     *
     * @param departId 群组在系统中的id
     * @param base     基准群组名
     * @return 相对DN
     */
    public String recursionBase(String departId, String base) {
        SysDepart sysDepart = sysDepartService.getById(departId);
        if (sysDepart != null) {
            // 拼接当前群组名
            String departName = sysDepart.getDepartName();
            departName = departName.replace(originalOuSuffix, "")
                    .replace(ouSuffix, "")
                    .replace(oSuffix, "");
            // 拼接当前基准路径, 如果基准路径为空，当前路径为 ou=[departName](o=[departName])
            // 否则拼接到基准路径后：ou=[base],ou=[departName](o=[base],o=[departName])
            String prefix = ouPrefix;
            if (oPrefix.equals(sysDepart.getDepartNameAbbr())) {
                prefix = oPrefix;
            }
            base = "".equals(base)
                    ? prefix + "=" + departName
                    : base + "," + prefix + "=" + departName;
            if (StringUtils.isNotBlank(sysDepart.getParentId())) {
                base = recursionBase(sysDepart.getParentId(), base);
            }
        }
        return base;
    }

    /**
     * 更新用户信息的方法。
     *
     * @param userName      用户名，用于标识需要更新的用户。
     * @param sysUser       系统用户对象，包含用户的基本信息。
     * @param defaultDepart 角色列表，表示用户的角色集合。
     * @param status        用户状态，用于设置用户的启用或禁用状态。
     * @param people        用户的LDAP信息对象，包含用户在LDAP中的详细信息。
     */
    public void updateUser(String userName, SysUser sysUser, List<SysRole> defaultDepart,
                           int status, Person people) {
        if (people == null) {
            people = this.searchLdapUser(userName);
        }
        if (people == null) {
            log.warn("用户[{}]在ldap服务器中不存在", userName);
            return;
        }
        // 从用户ou中查询出所属部门
        UserDepartment department = getDepartFormOu(people.getDistinguishedName());
        String adUserStatus = sysBaseAPI.getSysConfigParamByKey("adUserStatus");
        if (StringUtils.isBlank(adUserStatus)) {
            adUserStatus = "2";
        }
        List<SysDepart> sysDeparts = sysDepartService.queryUserDeparts(sysUser.getId());
        List<String> userSysDepartIds = new ArrayList<>();
        if (sysDeparts != null) {
            for (SysDepart sysDepart : sysDeparts) {
                if ("1".equals(sysDepart.getThirdId())) {
                    if (!department.getDepartmentName().equals(sysDepart.getDepartName())) {
                        // 如果用户所属域群组变化，根据配置是否冻结用户
                        sysUser.setStatus(Integer.valueOf(adUserStatus));
                    }
                } else {
                    // 非域群组
                    userSysDepartIds.add(sysDepart.getId());
                }
            }
        } else {
            sysUser.setStatus(Integer.valueOf(adUserStatus));
        }
        if (CollectionUtils.isNotEmpty(userSysDepartIds)) {
            userSysDepartIds.add(department.getDepartmentId());
            String userSysDepartIdStr = String.join(",", userSysDepartIds);
            department.setDepartmentId(userSysDepartIdStr);
        }

        sysUser.setUpdateTime(new Date());
        sysUser.setRealname(people.getUserName());
        sysUser.setDescription(people.getDescription());
        sysUser.setEmail(people.getEmail());
        sysUser.setPhone(people.getTelephoneNumber());
        sysUser.setOrgCode(department.getOrgCode());
        // 如果用户不属于LDAP，覆盖重名用户时修改用户原密码，用户状态根据前端同步方式设置（同步或者同步并激活）
        if (!"1".equals(sysUser.getThirdId())) {
            String salt = oConvertUtils.randomGen(8);
            sysUser.setSalt("");
            String passwordEncode = PasswordUtil.encrypt(sysUser.getUsername(),
                                                         sysUser.getPassword(), salt);
            sysUser.setPassword(passwordEncode);
            sysUser.setStatus(status);
        }
        LambdaQueryWrapper<SysUserRole> sysUserRoleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysUserRoleLambdaQueryWrapper.eq(SysUserRole::getUserId, sysUser.getId());
        sysUserRoleLambdaQueryWrapper.select(SysUserRole::getRoleId);
        List<SysUserRole> sysUserRoleList = sysUserRoleService.list(sysUserRoleLambdaQueryWrapper);
        String sysRoleIds = "";
        // 如果用户角色为空，或该用户不属于LDAP用户，将该用户的角色改为LDAP用户默认角色
        if (sysUserRoleList == null || sysUserRoleList.isEmpty() || !"1".equals(sysUser.getThirdId())) {
            sysRoleIds = defaultDepart.stream().map(SysRole::getId).distinct().collect(Collectors.joining(","));
        } else {
            sysRoleIds = sysUserRoleList.stream().map(SysUserRole::getRoleId).distinct().collect(Collectors.joining(
                    ","));
        }
        sysUser.setThirdId("1");
        sysUser.setThirdType("LDAP同步");
        sysUserService.editUser(sysUser, sysRoleIds, department.getDepartmentId());
    }

    /**
     * 根据传入的辨别名（distinguishedName）获取系统部门信息。
     * <p>例如 "OU=部门A,OU=部门B,DC=example,DC=com", 则获取到的部门为 部门A(域群组_ou) </p>
     *
     * @param distinguishedName 来自于目录服务的辨别名
     * @return SysDepart 对象，包含部门的基本信息（如ID、部门名称、组织编码等）
     */
    private UserDepartment getDepartFormOu(String distinguishedName) {
        UserDepartment depart = new UserDepartment();
        List<String> list = Arrays.asList(distinguishedName.split(","));
        for (int i = list.size() - 1; i >= 0; i--) {
            String s = list.get(i);
            Matcher matcher = ouPattern.matcher(s);
            if (!matcher.find()) {
                continue;
            }
            LambdaQueryWrapper<SysDepart> query = new LambdaQueryWrapper<SysDepart>();
            addDepartNameCondition(matcher, query);
            query.eq(SysDepart::getThirdId, "1");
            if (StringUtils.isNotBlank(depart.getDepartmentId())) {
                query.eq(SysDepart::getParentId, depart.getDepartmentId());
            } else {
                query.eq(SysDepart::getOrgType, 1);
            }
            List<SysDepart> sysDepartList = sysDepartService.list(query);
            if (!sysDepartList.isEmpty()) {
                depart.setDepartmentId(sysDepartList.get(0).getId());
                depart.setDepartmentName(sysDepartList.get(0).getDepartName());
                depart.setOrgCode(sysDepartList.get(0).getOrgCode());
            }
        }
        return depart;
    }

    /**
     * query添加群组名查询条件，兼容原来的域群组后缀
     *
     * @param matcher matcher
     * @param query   queryWrapper
     */
    private void addDepartNameCondition(Matcher matcher, LambdaQueryWrapper<SysDepart> query) {
        String name = matcher.group(2);
        String suffix = ouSuffixMap.get(matcher.group(1).toLowerCase());
        query.and(
                q -> q.eq(SysDepart::getDepartName, name + suffix)
                        .or()
                        .eq(SysDepart::getDepartName, name + originalOuSuffix)
        );
    }

    public SysUser saveUser(List<SysRole> list1, String ldapDepart, String userName, String passWord, int status,
                            Person person) {
        QueryWrapper<SysConfigParam> sysConfigParamQueryWrapper = new QueryWrapper<>();
        sysConfigParamQueryWrapper.lambda().in(SysConfigParam::getParamKey, "adVerification");
        List<SysConfigParam> sysConfigParams = sysConfigParamService.list(sysConfigParamQueryWrapper);
        String adVerification = "";
        for (SysConfigParam sysConfigParam : sysConfigParams) {
            if ("adVerification".equals(sysConfigParam.getParamKey())) {
                adVerification = sysConfigParam.getParamValue();
            }
        }
        String selectedRoles = list1.stream().map(SysRole::getId).distinct().collect(Collectors.joining(","));
        SysUser sysUser = new SysUser();
        sysUser.setUsername(userName);
        if (StringUtils.isNotBlank(passWord)) {
            String salt = oConvertUtils.randomGen(8);
            sysUser.setSalt(salt);
            String passwordEncode = PasswordUtil.encrypt(sysUser.getUsername(), passWord + "::ad", salt);
            sysUser.setPassword(passwordEncode);
        }
        sysUser.setActivitiSync(1);
        sysUser.setUserIdentity(1);
        if ("1".equals(adVerification)) {
            sysUser.setThirdId("1");
        }
        sysUser.setThirdType("LDAP同步");
        sysUser.setStatus(status);
        sysUser.setDelFlag(CommonConstant.DEL_FLAG_0);
        sysUser.setCreateTime(new Date());
        sysUser.setUpdateTime(new Date());
        sysUser.setRealname(userName);
        if (person == null) {
            person = this.searchLdapUser(userName);
        }
        if (person != null) {
            sysUser.setRealname(person.getUserName());
            sysUser.setDescription(person.getDescription());
            sysUser.setEmail(person.getEmail());
            sysUser.setPhone(person.getTelephoneNumber());
        }
        UserDepartment department = new UserDepartment();
        if (StringUtils.isNotBlank(ldapDepart)) {
            // 如果用户已有群组信息，保留用户的信息，不变更
            String[] orgCodes = ldapDepart.split(",");
            SysDepart sysDepart = sysDepartService.getById(orgCodes[0]);
            if (sysDepart != null) {
                department.setOrgCode(sysDepart.getOrgCode());
            }
            department.setDepartmentId(ldapDepart);
        } else if (person != null) {
            String distinguishedName = person.getDistinguishedName();
            department = getDepartFormOu(distinguishedName);
        }
        if (StringUtils.isNotBlank(department.getDepartmentId())) {
            // LDAP用户所属的群组在系统中存在，才需要保存
            sysUser.setOrgCode(department.getOrgCode());
            //添加用户、角色、群组
            sysUserService.saveUser(sysUser, selectedRoles, department.getDepartmentId());
            return sysUser;
        }
        return null;
    }

    public Set<String> addOrganizationUnitsToSysDepart(List<OrganizationUnit> organizationUnit) {
        // 1、查询目前系统中已有的域群组
        QueryWrapper<SysDepart> sysDepartQueryWrapper = new QueryWrapper<>();
        sysDepartQueryWrapper.lambda()
                .eq(SysDepart::getThirdId, "1")
                .select(SysDepart::getId, SysDepart::getParentId,
                        SysDepart::getDepartNameAbbr, SysDepart::getDepartName);
        List<SysDepart> sysLdapDeparts = sysDepartService.list(sysDepartQueryWrapper);

        // 2、构建 群组id - 子群组 的Map
        Map<String, Set<SysDepart>> parentDepartMap = new HashMap<>();
        for (SysDepart sysLdapDepart : sysLdapDeparts) {
            String parentId = StringUtils.isNotBlank(sysLdapDepart.getParentId()) ? sysLdapDepart.getParentId() : "";
            Set<SysDepart> departIds = parentDepartMap.computeIfAbsent(parentId, k -> new HashSet<>());
            departIds.add(sysLdapDepart);
        }

        // 递归添加群组
        HashSet<String> departIds = new HashSet<>();
        for (OrganizationUnit unit : organizationUnit) {
            addOrganizationUnitToSysDepart(unit, "", parentDepartMap, departIds);
        }
        return departIds;
    }

    private void addOrganizationUnitToSysDepart(OrganizationUnit organizationUnit,
                                                String parentDepartId,
                                                Map<String, Set<SysDepart>> parentDepartMap,
                                                Set<String> departIds) {
        String username = getOptUsername();
        Set<SysDepart> departs = parentDepartMap.getOrDefault(parentDepartId, new HashSet<>());
        Map<String, SysDepart> nameToDepartMapping = departs.stream()
                .collect(Collectors.toMap(SysDepart::getDepartName, Function.identity(), (k1, k2) -> k2));
        String name = organizationUnit.getName();
        SysDepart sysDepart;
        if (nameToDepartMapping.containsKey(name + originalOuSuffix)) {
            // 群组名在系统中已存在，但是为旧格式，更新（旧格式为（域群组），不包含 o/ou 标识）
            SysDepart existDepart = nameToDepartMapping.get(name);
            existDepart.setDepartName(name + ouSuffixMap.get(organizationUnit.getType().value));
            existDepart.setDepartNameAbbr(organizationUnit.getType().value);
            sysDepartService.updateDepartDataById(existDepart, username);
            sysDepart = existDepart;
        } else if (nameToDepartMapping.containsKey(name + ouSuffixMap.get(organizationUnit.getType().value))) {
            sysDepart = nameToDepartMapping.get(name + ouSuffixMap.get(organizationUnit.getType().value));
        } else {
            // 即不存在该群组
            sysDepart = new SysDepart();
            sysDepart.setParentId(parentDepartId);
            String uuid = UUID.randomUUID().toString().replace("-", "");
            sysDepart.setId(uuid);
            sysDepart.setDepartName(name + ouSuffixMap.get(organizationUnit.getType().value));
            sysDepart.setDepartNameAbbr(organizationUnit.getType().value);
            sysDepart.setDepartOrder(0);
            sysDepart.setThirdId("1");
            sysDepart.setThirdType("LDAP同步");
            sysDepart.setOrgCategory(StringUtils.isBlank(parentDepartId) ? "1" : "2");
            sysDepartService.saveDepartData(sysDepart, username);
        }
        // 如果有子级, 递归添加
        if (organizationUnit.hasChildren()) {
            for (OrganizationUnit child : organizationUnit.getChildren()) {
                addOrganizationUnitToSysDepart(child, sysDepart.getId(), parentDepartMap, departIds);
            }
        }
        departIds.add(sysDepart.getId());
    }

    private String getOptUsername() {
        String optUsername;
        try {
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            optUsername = sysUser.getUsername();
        } catch (Exception e) {
            optUsername = "";
        }
        return optUsername;
    }

    /**
     * LDAP用户认证
     */
    @Override
    public Result<JSONObject> authenticate(SysLoginModel sysLoginModel) {
        Result<JSONObject> result = new Result<JSONObject>();
        Result<JSONObject> checkCodeResult = loginController.getCheckCodeResult(sysLoginModel);
        if (null != checkCodeResult) {
            return checkCodeResult;
        }
        QueryWrapper<SysConfigParam> sysConfigParamQueryWrapper = new QueryWrapper<>();
        sysConfigParamQueryWrapper.lambda().in(SysConfigParam::getParamKey, "adOpen", "adVerification", "adFrozen");
        List<SysConfigParam> sysConfigParams = sysConfigParamService.list(sysConfigParamQueryWrapper);
        String adOpen = "";
        String adVerification = "";
        String adFrozen = "";
        for (SysConfigParam sysConfigParam : sysConfigParams) {
            if ("adOpen".equals(sysConfigParam.getParamKey())) {
                adOpen = sysConfigParam.getParamValue();
            } else if ("adVerification".equals(sysConfigParam.getParamKey())) {
                adVerification = sysConfigParam.getParamValue();
            } else if ("adFrozen".equals(sysConfigParam.getParamKey())) {
                adFrozen = sysConfigParam.getParamValue();
            }
        }
        if (!"1".equals(adOpen)) {
            result.error500("未开启LDAP服务,请联系管理员！");
            return result;
        }
        String username = sysLoginModel.getUsername();
        String password = sysLoginModel.getPassword();
        try {
            //密码解密
            password = AesEncryptUtil.desEncrypt(password.replaceAll("%2B", "\\+")).trim();
        } catch (Exception e) {
            e.printStackTrace();
            result.error500("账号名或密码错误");
            return result;
        }
        if ("".equals(password)) {
            result.error500("账号名或密码错误");
            return result;
        }

        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getUsername, username);
        SysUser sysUser = sysUserService.getOne(queryWrapper);
        LoginUser loginUser = new LoginUser();
        loginUser.setUsername(username);
        if (sysUser != null) {

            //平台存在用户账号非ad域不处理
            if ("1".equals(adVerification) && !"1".equals(sysUser.getThirdId())) {
                result.error500("账号名异常,请联系管理员！");
                baseCommonService.addLog("域群组用户登录失败，" + username + "账号名异常！", CommonConstant.LOG_TYPE_1, null,
                                         loginUser);
                return result;
            }
            // 查询错误次数，如果超过10次，冻结该用户
            if (sysUserService.getLoginUserErrorCount(username) >= 10) {
                // 冻结
                sysUser.setStatus(2);
                sysUserService.updateById(sysUser);
                // 冻结完清零 否则改完状态 下次还是被冻结
                sysUserService.updateLoginUserErrorCount(username, -1);
            }

            //1. 校验用户是否有效
            //update-end-author:wangshuai date:******** for: 登录代码验证用户是否注销bug，if条件永远为false
            result = sysUserService.checkUserIsEffective(sysUser);
            if (loginCheckPassword && !result.isSuccess()) {
                return result;
            }
        }
        SysLdap sysLdap = sysLdapParameter();
        //ldap用户验证
        //        EqualsFilter filter = new EqualsFilter("sAMAccountName", sysLoginModel.getUsername());
        LdapConnectTestModel connectProperties = new LdapConnectTestModel();
        connectProperties.setAdAddress(sysLdap.getAdAddress())
                .setLdapServerType(String.valueOf(sysLdap.getLdapServerType().getValue()))
                .setAdPort(sysLdap.getAdPort())
                .setLdapEncryptType((String.valueOf(sysLdap.getLdapEncryptType().getValue())))
                .setAdName(username)
                .setAdPasswd(password)
                .setAddomainName(sysLdap.getAddomainName());
        Result<?> result1 = testLdapConnect(connectProperties);
        //        boolean authenticate = ldapTemplate.authenticate("", filter.toString(), password);
        if (!result1.isSuccess()) {
            result.error500("账号名或密码错误");
            baseCommonService.addLog("域群组用户登录失败，" + username + "账号名或密码错误！", CommonConstant.LOG_TYPE_1, null, loginUser);
            return result;
        } else if (sysUser == null) {
            //登录成功是否为冻结
            int status = 1;
            if ("1".equals(adVerification)) {
                result.error500("账号暂未同步");
                return result;
            } else if ("2".equals(adVerification) && "2".equals(adFrozen)) {
                status = 2;
            }
            LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SysUser::getUsername, username);
            //是否逻辑删除
            List<SysUser> sysUsers = sysUserService.queryLogicDeleted(wrapper);
            if (!sysUsers.isEmpty()) {
                result.error500("用户登录失败，用户已经删除");
                baseCommonService.addLog("域群组用户登录失败，" + username + "用户已经删除！", CommonConstant.LOG_TYPE_1, null,
                                         loginUser);
                return result;
            }
            LambdaQueryWrapper<SysRole> sysRoleLambdaQueryWrapper = new LambdaQueryWrapper<>();
            sysRoleLambdaQueryWrapper.in(SysRole::getRoleCode, Arrays.asList(sysLdap.getLdapRole().split(",")));
            List<SysRole> list1 = sysRoleService.list(sysRoleLambdaQueryWrapper);
            if (list1 == null) {
                result.error500("用户登录失败，请联系管理员设置默认角色");
                baseCommonService.addLog("域群组用户登录失败，" + username + "未设置默认角色！", CommonConstant.LOG_TYPE_1, null,
                                         loginUser);
                return result;
            }
            sysUser = saveUser(list1, sysLdap.getLdapDepart(), username, password, status, null);
            if (sysUser == null) {
                result.error500("用户登录失败，设置的默认群组不存在");
                baseCommonService.addLog("域群组用户登录失败，" + username + "设置的默认群组不存在！", CommonConstant.LOG_TYPE_1, null,
                                         loginUser);
                return result;
            }
            if (status == 2) {
                result.error500("该用户已冻结，请联系管理员");
                return result;
            }
        }
        // 根据salt判断是否为未同步用户初次LDAP登录，避免每次登录执行修改密码操作，第一次登录后，salt置为空
        if (sysUser.getPassword() == null || StringUtils.isNotBlank(sysUser.getSalt())) {
            // 登录为LDAP用户后，修改用户密码
            String salt = oConvertUtils.randomGen(8);
            sysUser.setSalt("");
            String passwordEncode = PasswordUtil.encrypt(sysUser.getUsername(), password, salt);
            sysUser.setPassword(passwordEncode);
            sysUserService.updateById(sysUser);
        }
        // 登录成功 错误次数清零
        redisUtil.set(username, 0);
        //用户登录信息
        loginController.userInfo(sysUser, result, false, true);
        LoginUser user = new LoginUser();
        BeanUtils.copyProperties(sysUser, user);
        baseCommonService.addLog("账号名: " + username + ",域群组登录成功！", CommonConstant.LOG_TYPE_1, null, user);
        //update-end--Author:wangshuai  Date:20200714  for：登录日志没有记录人员
        return result;
    }

    public SysLdap sysLdapParameter() {
        SysLdap sysLdap = new SysLdap();
        AttrMapperField attrMapperField = new AttrMapperField();
        QueryWrapper<SysConfigParam> sysConfigParamQueryWrapper = new QueryWrapper<>();
        sysConfigParamQueryWrapper.lambda()
                .in(SysConfigParam::getParamKey, "adAddress", "adPort", "addomainName", "adName",
                    "adPasswd", "ldapSyncFilter", "ldapServerType", "ldapEncryptType",
                    "ldapLoginUserAttr", "ldapUsernameAttr", "ldapEmailAttr", "ldapPhoneAttr",
                    "ldapDescriptionAttr", "ldapRole", "ldapDepart");
        List<SysConfigParam> sysConfigParams = sysConfigParamService.list(sysConfigParamQueryWrapper);
        for (SysConfigParam sysConfigParam : sysConfigParams) {
            String paramKey = sysConfigParam.getParamKey();
            String paramValue = sysConfigParam.getParamValue();
            switch (paramKey) {
                case "adAddress":
                    sysLdap.setAdAddress(paramValue);
                    break;
                case "adPort":
                    sysLdap.setAdPort(paramValue);
                    break;
                case "addomainName":
                    sysLdap.setAddomainName(paramValue);
                    break;
                case "adName":
                    sysLdap.setAdName(paramValue);
                    break;
                case "adPasswd":
                    sysLdap.setAdPasswd(paramValue);
                    break;
                case "ldapSyncFilter":
                    sysLdap.setLdapSyncFilter(paramValue);
                    break;
                case "ldapServerType":
                    sysLdap.setLdapServerType(ServerType.fromValue(Integer.parseInt(paramValue)));
                    break;
                case "ldapEncryptType":
                    sysLdap.setLdapEncryptType(EncryptType.fromValue(Integer.parseInt(paramValue)));
                    break;
                case "ldapRole":
                    sysLdap.setLdapRole(paramValue);
                    break;
                case "ldapDepart":
                    sysLdap.setLdapDepart(paramValue);
                    break;
                // 设置默认映射关系
                case "ldapLoginUserAttr":
                    attrMapperField.setLdapLoginUserAttr(paramValue);
                    break;
                case "ldapUsernameAttr":
                    attrMapperField.setLdapUsernameAttr(paramValue);
                    break;
                case "ldapEmailAttr":
                    attrMapperField.setLdapEmailAttr(paramValue);
                    break;
                case "ldapPhoneAttr":
                    attrMapperField.setLdapPhoneAttr(paramValue);
                    break;
                case "ldapDescriptionAttr":
                    attrMapperField.setLdapDescriptionAttr(paramValue);
                    break;
                default:
                    break;
            }
        }
        attrMapperField.setDefaultFieldIfEmpty(sysLdap.getLdapServerType());
        sysLdap.setAttrMapperField(attrMapperField);
        return sysLdap;
    }

    /**
     * 获取用户
     */
    @Override
    public Person searchLdapUser(String username) {
        Person person;
        try {
            ldapConfigUpdate(sysLdapParameter());
            LdapUser user = ldapUserService.findUsersByUsername(username, "");
            person = ldapUserToPerson(user);
        } catch (Exception e) {
            return null;
        }
        return person;
    }

    @Override
    public Result<?> testLdapConnect(LdapConnectTestModel connectProperties) {
        if (StringUtils.isEmpty(connectProperties.getAdAddress())) {
            return Result.error("LDAP地址不能为空！");
        }
        if (StringUtils.isEmpty(connectProperties.getAdPort())) {
            return Result.error("LDAP端口不能为空！");
        }
        Result<Object> objectResult = new Result<>();
        DirContext ctx = null;
        String adAuthResult = "";
        Hashtable<String, String> HashEnv = new Hashtable<>();
        // LDAP访问安全级别(none,simple,strong)
        HashEnv.put(Context.SECURITY_AUTHENTICATION, "simple");
        if (!StringUtils.isEmpty(connectProperties.getAdName())) {
            ldapConfigUpdate(sysLdapParameter());
            String userDN = ldapSynchronizationService.getUserDN(connectProperties.getAdName(),
                                                                 connectProperties.getAddomainName());
            if (StringUtils.isEmpty(userDN)) {
                return objectResult.error500("LDAP身份验证失败, 请检查账号密码是否正确！");
            }
            HashEnv.put(Context.SECURITY_PRINCIPAL, userDN);
            if (StringUtils.isEmpty(connectProperties.getAdPasswd())) {
                return Result.error("输入测试账号，密码不能为空！");
            }
            // LDAP的密码
            HashEnv.put(Context.SECURITY_CREDENTIALS, connectProperties.getAdPasswd());
        }
        // LDAP工厂类
        HashEnv.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
        // 连接超时设置为3秒
        HashEnv.put("com.sun.jndi.ldap.connect.timeout", "3000");
        EncryptType encryptType = EncryptType.fromValue(
                Integer.parseInt(connectProperties.getLdapEncryptType()));
        if (encryptType == EncryptType.LDAP) {
            HashEnv.put(Context.PROVIDER_URL,
                        "ldap://" + connectProperties.getAdAddress() + ":" + connectProperties.getAdPort());
        } else {
            HashEnv.put(Context.PROVIDER_URL, "ldaps://" + connectProperties.getAdAddress() + ":"
                    + connectProperties.getAdPort());
            HashEnv.put("java.naming.security.protocol", "ssl");
            HashEnv.put("java.naming.ldap.factory.socket",
                        NonVerifyingSSLSocketFactory.class.getName());
        }
        try {
            // 初始化上下文
            ctx = new InitialDirContext(HashEnv);
        } catch (AuthenticationException e) {
            log.error("LDAP身份验证失败！", e);
            adAuthResult = "LDAP身份验证失败, 请检查账号密码是否正确！";
        } catch (NamingException e) {
            log.error("LDAP连接失败！", e);
            adAuthResult = "LDAP连接失败，请检查认证协议、LDAP服务器地址、端口号是否正确，端口号是否与认证协议对应的端口一致！";
        } catch (Exception e) {
            log.error("身份验证未知异常！", e);
            adAuthResult = "LDAP身份验证未知异常！";
        } finally {
            if (null != ctx) {
                try {
                    ctx.close();
                } catch (Exception e) {
                    log.error("关闭ldap上下文异常", e);
                }
            }
        }
        if (StringUtils.isNotBlank(adAuthResult)) {
            objectResult.error500(adAuthResult);
        } else {
            objectResult.success("测试连接成功!");
        }
        return objectResult;
    }

    @Override
    public Result<?> timingDepartUser(int adTimingOpen, String adCronExpression, String adParentId,
                                      Integer adXxlJobInfoId) {
        //新增、编辑
        if (adTimingOpen == 1 && StringUtils.isNotBlank(adCronExpression) && StringUtils.isNotBlank(adParentId)) {
            Result<?> jobResult = timingInspection(adParentId, adCronExpression, adXxlJobInfoId);
            if (!jobResult.isSuccess()) {
                return Result.error(jobResult.getMessage());
            }
            if (adXxlJobInfoId == null) {
                adXxlJobInfoId = (Integer) jobResult.getResult();
            }
            return Result.OK(adXxlJobInfoId);
        } else if (adTimingOpen == 2 && adXxlJobInfoId != null) {
            Resp deleteJob = xxlJobService.delete(adXxlJobInfoId);
            if (deleteJob.getCode() != 200) {
                return Result.error("定时任务关闭异常、请稍后重试！");
            }
        } else {
            return Result.error("请填写信息！");
        }
        return Result.OK();
    }

    public Result<?> timingInspection(String id, String cronExpression, Integer xxlJobInfoId) {
        LoginUser sysUser = getLoginUser();
        Resp appNameIdByAppname = xxlJobService.getAppNameIdByAppname();
        if (appNameIdByAppname.getCode() == 404) {
            return Result.error("定时任务服务未启动,请联系管理员!");
        }
        JSONObject jsonObject;
        int groupId = 0;
        if (appNameIdByAppname.getRespBody() != null) {
            jsonObject = (JSONObject) JSONObject.toJSON(appNameIdByAppname.getRespBody());
            Integer code = jsonObject.getInteger("code");
            if (code == 200) {
                JSONObject content = jsonObject.getJSONObject("content");
                groupId = content.getInteger("id");
            }
        }
        XxlJobInfo xxlJobInfo = new XxlJobInfo();
        if (xxlJobInfoId != null) {
            xxlJobInfo.setId(xxlJobInfoId);
        }
        xxlJobInfo.setJobGroup(groupId);
        xxlJobInfo.setJobCron(cronExpression);
        xxlJobInfo.setJobDesc("群组同步用户");
        xxlJobInfo.setAuthor(sysUser.getRealname());
        xxlJobInfo.setExecutorRouteStrategy("FAILOVER");
        xxlJobInfo.setExecutorHandler("timingDepartUserXxl");
        xxlJobInfo.setExecutorParam(id);
        xxlJobInfo.setExecutorBlockStrategy("SERIAL_EXECUTION");
        xxlJobInfo.setExecutorTimeout(0);
        xxlJobInfo.setExecutorFailRetryCount(0);
        xxlJobInfo.setGlueType("BEAN");
        xxlJobInfo.setGlueRemark("GLUE代码初始化");
        xxlJobInfo.setTriggerStatus(0);
        xxlJobInfo.setTriggerLastTime(0);
        xxlJobInfo.setTriggerNextTime(0);
        Resp resp = xxlJobService.saveXxl(xxlJobInfo);
        JSONObject jsonBody;
        if (resp.getRespBody() != null) {
            jsonBody = (JSONObject) JSONObject.toJSON(resp.getRespBody());
            if (jsonBody.getInteger("code") == 200) {
                return Result.OK(jsonBody.getInteger("content"));
            } else {
                return Result.error(jsonBody.getString("msg"));
            }
        }
        return Result.OK();
    }

    private void ldapConfigUpdate(SysLdap sysLdap) {
        LdapServerType ldapServerType = sysLdap.getLdapServerType() == ServerType.AD
                ? LdapServerType.ACTIVE_DIRECTORY
                : LdapServerType.OPENLDAP;
        LdapAttributeMapping mapping = LdapAttributeMapping.builder()
                .user(
                        LdapAttributeMapping.UserAttributes.builder()
                                .username(sysLdap.getAttrMapperField().getLdapLoginUserAttr())
                                .commonName(sysLdap.getAttrMapperField().getLdapLoginUserAttr())
                                .displayName(sysLdap.getAttrMapperField().getLdapUsernameAttr())
                                .mail(sysLdap.getAttrMapperField().getLdapEmailAttr())
                                .telephoneNumber(sysLdap.getAttrMapperField().getLdapPhoneAttr())
                                .description(sysLdap.getAttrMapperField().getLdapDescriptionAttr())
                                .build()
                )
                .build();
        ldapSynchronizationService.configureLdapConnection(contextSource(), ldapServerType, mapping);
    }

    /**
     * 用户所属的部门
     */
    @Getter
    @Setter
    static class UserDepartment {
        private String departmentId;
        private String departmentName;
        private String orgCode;
    }
}
