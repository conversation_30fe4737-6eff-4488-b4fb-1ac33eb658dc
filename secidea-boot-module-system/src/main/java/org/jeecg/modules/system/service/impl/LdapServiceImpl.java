package org.jeecg.modules.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.system.entity.Person;
import org.jeecg.modules.system.model.LdapConnectTestModel;
import org.jeecg.modules.system.model.LdapModel;
import org.jeecg.modules.system.model.SysLoginModel;
import org.jeecg.modules.system.service.ILdapService;
import org.jeecg.modules.system.service.impl.ldap.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.core.support.LdapContextSource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * LDAP服务实现类
 * 重构后的主服务类，作为门面协调各个子服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LdapServiceImpl implements ILdapService {

    @Autowired
    private LdapConnectionService connectionService;

    @Autowired
    private LdapDepartmentSyncService departmentSyncService;

    @Autowired
    private LdapUserSyncService userSyncService;

    @Autowired
    private LdapAuthenticationService authenticationService;

    @Autowired
    private LdapJobService jobService;

    @Override
    public LdapContextSource contextSource() {
        return connectionService.createContextSource();
    }

    @Override
    public LdapTemplate ldapTemplate(LdapContextSource contextSource) {
        return connectionService.createLdapTemplate(contextSource);
    }

    @Override
    public List<Person> findUsersFromOrgnizationalUnit(String base) {
        return userSyncService.findUsersFromOrganizationalUnit(base);
    }

    @Override
    public Result<?> sczCompany() {
        return departmentSyncService.syncCompany();
    }

    @Override
    public void sczUser(int status, String departId, LdapModel ldapModel) {
        userSyncService.syncUsers(status, departId, ldapModel);
    }

    @Override
    public Result<?> verification(String departId) {
        return userSyncService.verifyDepartmentUserSync(departId);
    }

    @Override
    public Result<JSONObject> authenticate(SysLoginModel sysLoginModel) {
        return authenticationService.authenticate(sysLoginModel);
    }

    @Override
    public Person searchLdapUser(String username) {
        return userSyncService.searchLdapUser(username);
    }

    @Override
    public Result<?> testLdapConnect(LdapConnectTestModel connectProperties) {
        return connectionService.testConnection(connectProperties);
    }

    @Override
    public Result<?> timingDepartUser(int adTimingOpen, String adCronExpression, String adParentId,
                                      Integer adXxlJobInfoId) {
        return jobService.manageTimingDepartUser(adTimingOpen, adCronExpression, adParentId, adXxlJobInfoId);
    }

}
