package org.jeecg.modules.system.service.impl.ldap;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * LDAP服务相关常量定义
 * 
 * <AUTHOR>
 */
public final class LdapConstants {
    
    private LdapConstants() {
        // 工具类，禁止实例化
    }
    
    // ==================== 配置参数键 ====================
    public static final String LDAP_OVERWRITE_SYS_USER_KEY = "ldapOverwriteSysUser";
    public static final String AD_VERIFICATION_KEY = "adVerification";
    public static final String AD_OPEN_KEY = "adOpen";
    public static final String AD_FROZEN_KEY = "adFrozen";
    public static final String AD_USER_STATUS_KEY = "adUserStatus";
    
    // ==================== LDAP配置参数键 ====================
    public static final String AD_ADDRESS_KEY = "adAddress";
    public static final String AD_PORT_KEY = "adPort";
    public static final String AD_DOMAIN_NAME_KEY = "addomainName";
    public static final String AD_NAME_KEY = "adName";
    public static final String AD_PASSWD_KEY = "adPasswd";
    public static final String LDAP_SYNC_FILTER_KEY = "ldapSyncFilter";
    public static final String LDAP_SERVER_TYPE_KEY = "ldapServerType";
    public static final String LDAP_ENCRYPT_TYPE_KEY = "ldapEncryptType";
    public static final String LDAP_LOGIN_USER_ATTR_KEY = "ldapLoginUserAttr";
    public static final String LDAP_USERNAME_ATTR_KEY = "ldapUsernameAttr";
    public static final String LDAP_EMAIL_ATTR_KEY = "ldapEmailAttr";
    public static final String LDAP_PHONE_ATTR_KEY = "ldapPhoneAttr";
    public static final String LDAP_DESCRIPTION_ATTR_KEY = "ldapDescriptionAttr";
    public static final String LDAP_ROLE_KEY = "ldapRole";
    public static final String LDAP_DEPART_KEY = "ldapDepart";
    
    // ==================== URL前缀 ====================
    public static final String LDAP_URL_PREFIX = "ldap://";
    public static final String LDAPS_URL_PREFIX = "ldaps://";
    
    // ==================== 组织单元相关 ====================
    public static final String O_PREFIX = "o";
    public static final String OU_PREFIX = "ou";
    public static final String O_SUFFIX = "（域群组_o）";
    public static final String OU_SUFFIX = "（域群组_ou）";
    public static final String ORIGINAL_OU_SUFFIX = "（域群组）";
    
    // OU后缀映射
    public static final Map<String, String> OU_SUFFIX_MAP = new HashMap<String, String>() {{
        put(O_PREFIX, O_SUFFIX);
        put(OU_PREFIX, OU_SUFFIX);
    }};
    
    // ==================== 正则表达式 ====================
    /**
     * 匹配DN中的o(或ou)和名称
     */
    public static final Pattern OU_PATTERN = Pattern.compile("^(?i)(o|ou)=([^,]+)");
    
    // ==================== 状态值 ====================
    public static final String THIRD_ID_LDAP = "1";
    public static final String THIRD_TYPE_LDAP = "LDAP同步";
    public static final String STATUS_ENABLED = "1";
    public static final String STATUS_DISABLED = "2";
    public static final String DEFAULT_AD_USER_STATUS = "2";
    
    // ==================== 错误消息 ====================
    public static final String ERROR_LDAP_CONFIG = "请检查LDAP服务配置是否正确！需要配置地址、端口、节点、管理员DN、密码";
    public static final String ERROR_LDAP_NOT_ENABLED = "未开启LDAP服务,请联系管理员！";
    public static final String ERROR_LDAP_AUTH_FAILED = "LDAP身份验证失败, 请检查账号密码是否正确！";
    public static final String ERROR_LDAP_CONNECT_FAILED = "LDAP连接失败，请检查认证协议、LDAP服务器地址、端口号是否正确，端口号是否与认证协议对应的端口一致！";
    public static final String ERROR_LDAP_UNKNOWN = "LDAP身份验证未知异常！";
    public static final String ERROR_USERNAME_PASSWORD = "账号名或密码错误";
    public static final String ERROR_ACCOUNT_ABNORMAL = "账号名异常,请联系管理员！";
    public static final String ERROR_ACCOUNT_NOT_SYNCED = "账号暂未同步";
    public static final String ERROR_ACCOUNT_FROZEN = "该用户已冻结，请联系管理员";
    public static final String ERROR_USER_DELETED = "用户登录失败，用户已经删除";
    public static final String ERROR_NO_DEFAULT_ROLE = "用户登录失败，请联系管理员设置默认角色";
    public static final String ERROR_NO_DEFAULT_DEPART = "用户登录失败，设置的默认群组不存在";
    public static final String ERROR_DUPLICATE_DEPART_NAME = "存在同级重名群组";
    public static final String ERROR_CANNOT_SYNC_LOCAL_DEPART = "无法同步本地群组中的用户，请选择域群组";
    public static final String ERROR_DISABLE_UNSYNCED_LOGIN = "请关闭LDAP配置中【允许未同步用户登录】！";
    public static final String ERROR_LDAP_ADDRESS_EMPTY = "LDAP地址不能为空！";
    public static final String ERROR_LDAP_PORT_EMPTY = "LDAP端口不能为空！";
    public static final String ERROR_LDAP_PASSWORD_EMPTY = "输入测试账号，密码不能为空！";
    public static final String ERROR_JOB_SERVICE_NOT_STARTED = "定时任务服务未启动,请联系管理员!";
    public static final String ERROR_JOB_CLOSE_FAILED = "定时任务关闭异常、请稍后重试！";
    public static final String ERROR_FILL_INFO = "请填写信息！";
    
    // ==================== 成功消息 ====================
    public static final String SUCCESS_TEST_CONNECTION = "测试连接成功!";
    
    // ==================== 日志消息 ====================
    public static final String LOG_SYNC_USER_COMPLETE = "同步ad域用户完成！";
    public static final String LOG_SYNC_USER_FAILED = "同步ad域用户失败：{}";
    public static final String LOG_SYNC_DEPART_EXCEPTION = "ad同步用户系统异常";
    public static final String LOG_SYNC_GROUP_EXCEPTION = "ad同步群组系统异常：{}";
    public static final String LOG_USER_NOT_EXIST_IN_LDAP = "用户[{}]在ldap服务器中不存在";
    public static final String LOG_LOGIN_SUCCESS = "账号名: {},域群组登录成功！";
    public static final String LOG_LOGIN_FAILED_USERNAME_PASSWORD = "域群组用户登录失败，{}账号名或密码错误！";
    public static final String LOG_LOGIN_FAILED_ACCOUNT_ABNORMAL = "域群组用户登录失败，{}账号名异常！";
    public static final String LOG_LOGIN_FAILED_USER_DELETED = "域群组用户登录失败，{}用户已经删除！";
    public static final String LOG_LOGIN_FAILED_NO_DEFAULT_ROLE = "域群组用户登录失败，{}未设置默认角色！";
    public static final String LOG_LOGIN_FAILED_NO_DEFAULT_DEPART = "域群组用户登录失败，{}设置的默认群组不存在！";
    
    // ==================== 缓存相关 ====================
    public static final String CACHE_LDAP_SYNC_USER = "sys:ldap:sczUser";
    
    // ==================== LDAP属性 ====================
    public static final String LDAP_ATTR_OBJECT_GUID = "objectGUID";
    public static final String LDAP_ATTR_BINARY = "java.naming.ldap.attributes.binary";
    
    // ==================== LDAP上下文配置 ====================
    public static final String LDAP_CONTEXT_FACTORY = "com.sun.jndi.ldap.LdapCtxFactory";
    public static final String LDAP_SECURITY_AUTHENTICATION = "simple";
    public static final String LDAP_SECURITY_PROTOCOL = "ssl";
    public static final String LDAP_CONNECT_TIMEOUT = "com.sun.jndi.ldap.connect.timeout";
    public static final String LDAP_CONNECT_TIMEOUT_VALUE = "3000";
    
    // ==================== 定时任务相关 ====================
    public static final String JOB_DESC_SYNC_DEPART_USER = "群组同步用户";
    public static final String JOB_EXECUTOR_HANDLER = "timingDepartUserXxl";
    public static final String JOB_EXECUTOR_ROUTE_STRATEGY = "FAILOVER";
    public static final String JOB_EXECUTOR_BLOCK_STRATEGY = "SERIAL_EXECUTION";
    public static final String JOB_GLUE_TYPE = "BEAN";
    public static final String JOB_GLUE_REMARK = "GLUE代码初始化";
    
    // ==================== 数值常量 ====================
    public static final int MAX_LOGIN_ERROR_COUNT = 10;
    public static final int JOB_EXECUTOR_TIMEOUT = 0;
    public static final int JOB_EXECUTOR_FAIL_RETRY_COUNT = 0;
    public static final int JOB_TRIGGER_STATUS = 0;
    public static final int JOB_TRIGGER_LAST_TIME = 0;
    public static final int JOB_TRIGGER_NEXT_TIME = 0;
    public static final int HTTP_CODE_SUCCESS = 200;
    public static final int HTTP_CODE_NOT_FOUND = 404;
    public static final int DEPART_ORDER_DEFAULT = 0;
    public static final int ORG_TYPE_TOP = 1;
    public static final int ORG_CATEGORY_TOP = 1;
    public static final int ORG_CATEGORY_SUB = 2;
    
    // ==================== 密码相关 ====================
    public static final String PASSWORD_SUFFIX = "::ad";
    public static final int SALT_LENGTH = 8;
}
