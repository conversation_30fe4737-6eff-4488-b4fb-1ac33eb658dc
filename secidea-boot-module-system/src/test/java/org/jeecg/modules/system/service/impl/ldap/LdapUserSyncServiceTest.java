package org.jeecg.modules.system.service.impl.ldap;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.ldap.model.LdapUser;
import org.jeecg.modules.ldap.service.LdapSynchronizationService;
import org.jeecg.modules.ldap.service.LdapUserService;
import org.jeecg.modules.system.entity.*;
import org.jeecg.modules.system.model.LdapModel;
import org.jeecg.modules.system.service.ISysUserRoleService;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.system.service.impl.SysDepartServiceImpl;
import org.jeecg.modules.system.service.impl.SysRoleServiceImpl;
import org.jeecg.modules.system.service.impl.SysUserDepartServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LDAP用户同步服务单元测试
 * 
 * 测试覆盖用户同步的各种场景：
 * - 从组织单元查找用户
 * - 部门用户同步验证
 * - 用户同步处理逻辑
 * - 用户更新和创建逻辑
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("LDAP用户同步服务测试")
class LdapUserSyncServiceTest {

    @InjectMocks
    private LdapUserSyncService userSyncService;

    @Mock
    private LdapSynchronizationService ldapSynchronizationService;

    @Mock
    private LdapUserService ldapUserService;

    @Mock
    private LdapConnectionService connectionService;

    @Mock
    private LdapConfigurationService configurationService;

    @Mock
    private LdapUserConverter userConverter;

    @Mock
    private ISysUserService sysUserService;

    @Mock
    private SysRoleServiceImpl sysRoleService;

    @Mock
    private SysDepartServiceImpl sysDepartService;

    @Mock
    private SysUserDepartServiceImpl sysUserDepartService;

    @Mock
    private ISysUserRoleService sysUserRoleService;

    @Mock
    private ISysBaseAPI sysBaseAPI;

    @Mock
    private RedisUtil redisUtil;

    private SysLdap testLdapConfig;
    private SysDepart testDepart;
    private List<SysRole> testRoles;
    private Person testPerson;
    private LdapUser testLdapUser;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testLdapConfig = new SysLdap();
        testLdapConfig.setLdapSyncFilter("(objectClass=person)");

        testDepart = new SysDepart();
        testDepart.setId("dept123");
        testDepart.setThirdId("1");
        testDepart.setOrgCode("ORG001");

        SysRole role = new SysRole();
        role.setId("role123");
        role.setRoleCode("admin");
        testRoles = Arrays.asList(role);

        testPerson = new Person();
        testPerson.setLoginName("testuser");
        testPerson.setUserName("Test User");
        testPerson.setEmail("<EMAIL>");
        testPerson.setDistinguishedName("CN=testuser,OU=Users,DC=test,DC=com");

        testLdapUser = new LdapUser();
        testLdapUser.setUsername("testuser");
        testLdapUser.setFullName("Test User");
        testLdapUser.setEmail("<EMAIL>");
    }

    @Test
    @DisplayName("从组织单元查找用户应正确转换LDAP用户")
    void findUsersFromOrganizationalUnit_ShouldConvertLdapUsersCorrectly() {
        // Given
        String base = "OU=Users,DC=test,DC=com";
        when(configurationService.getLdapConfiguration()).thenReturn(testLdapConfig);
        when(ldapSynchronizationService.getUsersFromOrganizationalUnit(eq(base), any()))
                .thenReturn(Arrays.asList(testLdapUser));
        when(userConverter.convertToPerson(testLdapUser)).thenReturn(testPerson);

        // When
        List<Person> result = userSyncService.findUsersFromOrganizationalUnit(base);

        // Then
        assertEquals(1, result.size());
        assertEquals(testPerson, result.get(0));
        verify(connectionService).updateLdapConfiguration(testLdapConfig);
    }

    @Test
    @DisplayName("验证非LDAP部门同步应返回错误")
    void verifyDepartmentUserSync_WhenNotLdapDepartment_ShouldReturnError() {
        // Given
        testDepart.setThirdId("0"); // 非LDAP部门
        when(sysDepartService.getById("dept123")).thenReturn(testDepart);

        // When
        Result<?> result = userSyncService.verifyDepartmentUserSync("dept123");

        // Then
        assertFalse(result.isSuccess());
        assertEquals("无法同步本地群组中的用户，请选择域群组", result.getMessage());
    }

    @Test
    @DisplayName("允许未同步用户登录时验证部门同步应返回错误")
    void verifyDepartmentUserSync_WhenUnSyncedLoginAllowed_ShouldReturnError() {
        // Given
        when(sysDepartService.getById("dept123")).thenReturn(testDepart);
        when(configurationService.isUnSyncedLoginAllowed()).thenReturn(true);

        // When
        Result<?> result = userSyncService.verifyDepartmentUserSync("dept123");

        // Then
        assertFalse(result.isSuccess());
        assertEquals("请关闭LDAP配置中【允许未同步用户登录】！", result.getMessage());
    }

    @Test
    @DisplayName("无默认角色时验证部门同步应返回错误")
    void verifyDepartmentUserSync_WhenNoDefaultRole_ShouldReturnError() {
        // Given
        when(sysDepartService.getById("dept123")).thenReturn(testDepart);
        when(configurationService.isUnSyncedLoginAllowed()).thenReturn(false);
        when(configurationService.getLdapConfiguration()).thenReturn(testLdapConfig);
        when(sysRoleService.list(any(LambdaQueryWrapper.class))).thenReturn(null);

        // When
        Result<?> result = userSyncService.verifyDepartmentUserSync("dept123");

        // Then
        assertFalse(result.isSuccess());
        assertEquals("用户登录失败，请联系管理员设置默认角色", result.getMessage());
    }

    @Test
    @DisplayName("LDAP查询异常时验证部门同步应返回配置错误")
    void verifyDepartmentUserSync_WhenLdapQueryFailed_ShouldReturnConfigError() {
        // Given
        when(sysDepartService.getById("dept123")).thenReturn(testDepart);
        when(configurationService.isUnSyncedLoginAllowed()).thenReturn(false);
        when(configurationService.getLdapConfiguration()).thenReturn(testLdapConfig);
        when(sysRoleService.list(any(LambdaQueryWrapper.class))).thenReturn(testRoles);
        
        // 模拟LDAP查询异常
        when(userSyncService.findUsersFromOrganizationalUnit(anyString()))
                .thenThrow(new RuntimeException("LDAP connection failed"));

        // When
        Result<?> result = userSyncService.verifyDepartmentUserSync("dept123");

        // Then
        assertFalse(result.isSuccess());
        assertEquals("请检查LDAP服务配置是否正确！需要配置地址、端口、节点、管理员DN、密码", result.getMessage());
    }

    @Test
    @DisplayName("验证部门同步成功时应返回LDAP模型")
    void verifyDepartmentUserSync_WhenSuccessful_ShouldReturnLdapModel() {
        // Given
        when(sysDepartService.getById("dept123")).thenReturn(testDepart);
        when(configurationService.isUnSyncedLoginAllowed()).thenReturn(false);
        when(configurationService.getLdapConfiguration()).thenReturn(testLdapConfig);
        when(sysRoleService.list(any(LambdaQueryWrapper.class))).thenReturn(testRoles);

        // Mock buildDepartmentBaseDN相关调用
        when(sysDepartService.getById("dept123")).thenReturn(testDepart);
        
        List<Person> persons = Arrays.asList(testPerson);
        // 由于findUsersFromOrganizationalUnit是当前类的方法，需要spy
        LdapUserSyncService spyService = spy(userSyncService);
        doReturn(persons).when(spyService).findUsersFromOrganizationalUnit(anyString());

        // When
        Result<?> result = spyService.verifyDepartmentUserSync("dept123");

        // Then
        assertTrue(result.isSuccess());
        assertNotNull(result.getResult());
        assertTrue(result.getResult() instanceof LdapModel);
        
        LdapModel ldapModel = (LdapModel) result.getResult();
        assertEquals(testLdapConfig, ldapModel.getSysLdap());
        assertEquals(testRoles, ldapModel.getSysRoleList());
        assertEquals(persons, ldapModel.getPersonList());
    }

    @Test
    @DisplayName("同步用户时应正确处理现有用户和新用户")
    void syncUsers_ShouldHandleExistingAndNewUsers() {
        // Given
        LdapModel ldapModel = new LdapModel();
        ldapModel.setPersonList(Arrays.asList(testPerson));
        ldapModel.setSysRoleList(testRoles);

        when(sysDepartService.getById("dept123")).thenReturn(testDepart);
        when(sysUserDepartService.queryUserByDepCode(any(), any())).thenReturn(Collections.emptyList());
        when(configurationService.isOverwriteSysUser()).thenReturn(false);

        // Mock用户不存在的情况
        when(sysUserService.queryLogicDeleted(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(sysUserService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Mock saveUser方法
        SysUser newUser = new SysUser();
        newUser.setUsername("testuser");
        LdapUserSyncService spyService = spy(userSyncService);
        doReturn(newUser).when(spyService).saveUser(any(), any(), any(), any(), anyInt(), any());

        // When
        spyService.syncUsers(1, "dept123", ldapModel);

        // Then
        verify(spyService).saveUser(eq(testRoles), isNull(), eq("testuser"), isNull(), eq(1), eq(testPerson));
        verify(redisUtil).del("sys:ldap:sczUser");
    }

    @Test
    @DisplayName("同步用户时应正确处理逻辑删除的用户")
    void syncUsers_ShouldHandleLogicallyDeletedUsers() {
        // Given
        LdapModel ldapModel = new LdapModel();
        ldapModel.setPersonList(Arrays.asList(testPerson));
        ldapModel.setSysRoleList(testRoles);

        when(sysDepartService.getById("dept123")).thenReturn(testDepart);
        when(sysUserDepartService.queryUserByDepCode(any(), any())).thenReturn(Collections.emptyList());
        when(configurationService.isOverwriteSysUser()).thenReturn(true);

        // Mock逻辑删除的用户
        SysUser deletedUser = new SysUser();
        deletedUser.setId("user123");
        deletedUser.setThirdId("1");
        when(sysUserService.queryLogicDeleted(any(LambdaQueryWrapper.class)))
                .thenReturn(Arrays.asList(deletedUser));

        // Mock现有用户
        SysUser existingUser = new SysUser();
        existingUser.setId("user123");
        existingUser.setUsername("testuser");
        existingUser.setThirdId("1");
        when(sysUserService.getOne(any(LambdaQueryWrapper.class))).thenReturn(existingUser);

        LdapUserSyncService spyService = spy(userSyncService);
        doNothing().when(spyService).updateUser(any(), any(), any(), anyInt(), any());

        // When
        spyService.syncUsers(1, "dept123", ldapModel);

        // Then
        verify(sysUserService).revertLogicDeleted(eq(Arrays.asList("user123")), any());
        verify(spyService).updateUser(eq("testuser"), eq(existingUser), eq(testRoles), eq(1), eq(testPerson));
    }

    @Test
    @DisplayName("搜索LDAP用户成功时应返回转换后的Person对象")
    void searchLdapUser_WhenSuccessful_ShouldReturnConvertedPerson() {
        // Given
        when(configurationService.getLdapConfiguration()).thenReturn(testLdapConfig);
        when(ldapUserService.findUsersByUsername("testuser", "")).thenReturn(testLdapUser);
        when(userConverter.convertToPerson(testLdapUser)).thenReturn(testPerson);

        // When
        Person result = userSyncService.searchLdapUser("testuser");

        // Then
        assertEquals(testPerson, result);
        verify(connectionService).updateLdapConfiguration(testLdapConfig);
    }

    @Test
    @DisplayName("搜索LDAP用户异常时应返回null")
    void searchLdapUser_WhenException_ShouldReturnNull() {
        // Given
        when(configurationService.getLdapConfiguration()).thenReturn(testLdapConfig);
        when(ldapUserService.findUsersByUsername(any(), any()))
                .thenThrow(new RuntimeException("LDAP search failed"));

        // When
        Person result = userSyncService.searchLdapUser("testuser");

        // Then
        assertNull(result);
    }

    @Test
    @DisplayName("保存用户时应正确设置用户属性")
    void saveUser_ShouldSetUserPropertiesCorrectly() {
        // Given
        when(configurationService.isUnSyncedLoginAllowed()).thenReturn(false); // 严格模式，应设置thirdId

        // Mock部门信息
        LdapUserSyncService.UserDepartment department = new LdapUserSyncService.UserDepartment();
        department.setDepartmentId("dept123");
        department.setOrgCode("ORG001");

        LdapUserSyncService spyService = spy(userSyncService);
        doReturn(testPerson).when(spyService).searchLdapUser("testuser");
        doReturn(department).when(spyService).getDepartmentInfo(any(), any());

        // When
        SysUser result = spyService.saveUser(testRoles, null, "testuser", "password", 1, null);

        // Then
        assertNotNull(result);
        assertEquals("testuser", result.getUsername());
        assertEquals("1", result.getThirdId()); // 严格模式下应设置thirdId
        assertEquals("LDAP同步", result.getThirdType());
        assertEquals(1, result.getStatus());
        assertEquals("Test User", result.getRealname());
        assertEquals("<EMAIL>", result.getEmail());
        verify(sysUserService).saveUser(eq(result), anyString(), eq("dept123"));
    }

    @Test
    @DisplayName("保存用户时部门不存在应返回null")
    void saveUser_WhenDepartmentNotExists_ShouldReturnNull() {
        // Given
        LdapUserSyncService.UserDepartment department = new LdapUserSyncService.UserDepartment();
        department.setDepartmentId(""); // 部门不存在

        LdapUserSyncService spyService = spy(userSyncService);
        doReturn(testPerson).when(spyService).searchLdapUser("testuser");
        doReturn(department).when(spyService).getDepartmentInfo(any(), any());

        // When
        SysUser result = spyService.saveUser(testRoles, null, "testuser", "password", 1, null);

        // Then
        assertNull(result);
        verify(sysUserService, never()).saveUser(any(), any(), any());
    }
}
