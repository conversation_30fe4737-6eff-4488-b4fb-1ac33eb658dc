package org.jeecg.modules.system.service.impl.ldap;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.api.IOscaBaseAPI;
import org.jeecg.modules.api.IScapBaseAPI;
import org.jeecg.modules.ldap.model.OrganizationUnit;
import org.jeecg.modules.ldap.service.LdapSynchronizationService;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.entity.SysUserDepart;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.system.service.impl.SysDepartServiceImpl;
import org.jeecg.modules.system.service.impl.SysUserDepartServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LDAP部门同步服务单元测试
 * 
 * 测试覆盖部门同步的各种场景：
 * - 公司组织结构同步
 * - 部门名称冲突检查
 * - 组织单元添加到系统
 * - 未使用部门和用户清理
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("LDAP部门同步服务测试")
class LdapDepartmentSyncServiceTest {

    @InjectMocks
    private LdapDepartmentSyncService departmentSyncService;

    @Mock
    private LdapSynchronizationService ldapSynchronizationService;

    @Mock
    private LdapConnectionService connectionService;

    @Mock
    private LdapConfigurationService configurationService;

    @Mock
    private SysDepartServiceImpl sysDepartService;

    @Mock
    private SysUserDepartServiceImpl sysUserDepartService;

    @Mock
    private ISysUserService sysUserService;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    private IScapBaseAPI scapBaseAPI;

    @Mock
    private IOscaBaseAPI oscaBaseAPI;

    private OrganizationUnit testOrgUnit;
    private SysDepart testDepart;
    private List<OrganizationUnit> testOrgUnits;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testOrgUnit = new OrganizationUnit();
        testOrgUnit.setName("TestDepartment");
        testOrgUnit.setType(OrganizationUnit.Type.ORGANIZATIONAL_UNIT);

        testDepart = new SysDepart();
        testDepart.setId("dept123");
        testDepart.setDepartName("TestDepartment（域群组_ou）");
        testDepart.setThirdId("1");
        testDepart.setOrgCode("ORG001");

        testOrgUnits = Arrays.asList(testOrgUnit);
    }

    @Test
    @DisplayName("同步公司结构成功时应返回成功结果")
    void syncCompany_WhenSuccessful_ShouldReturnSuccess() {
        // Given
        when(configurationService.getLdapConfiguration()).thenReturn(mock(org.jeecg.modules.system.entity.SysLdap.class));
        when(ldapSynchronizationService.discoverOrganizationalUnitsAsTreeWithFilter("", ""))
                .thenReturn(testOrgUnits);

        // Mock部门名称冲突检查
        LdapDepartmentSyncService spyService = spy(departmentSyncService);
        doReturn(Result.ok()).when(spyService).checkDepartmentNameConflicts(testOrgUnits);
        doReturn(Collections.singleton("dept123")).when(spyService).addOrganizationUnitsToSystem(testOrgUnits);
        doNothing().when(spyService).removeUnusedDepartmentsAndUsers(any());
        doNothing().when(spyService).clearDepartmentCache();

        // When
        Result<?> result = spyService.syncCompany();

        // Then
        assertTrue(result.isSuccess());
        verify(connectionService).updateLdapConfiguration(any());
        verify(spyService).checkDepartmentNameConflicts(testOrgUnits);
        verify(spyService).addOrganizationUnitsToSystem(testOrgUnits);
        verify(spyService).removeUnusedDepartmentsAndUsers(any());
        verify(spyService).clearDepartmentCache();
    }

    @Test
    @DisplayName("LDAP连接异常时应返回配置错误")
    void syncCompany_WhenLdapException_ShouldReturnConfigError() {
        // Given
        when(configurationService.getLdapConfiguration()).thenReturn(mock(org.jeecg.modules.system.entity.SysLdap.class));
        when(ldapSynchronizationService.discoverOrganizationalUnitsAsTreeWithFilter("", ""))
                .thenThrow(new RuntimeException("LDAP connection failed"));

        // When
        Result<?> result = departmentSyncService.syncCompany();

        // Then
        assertFalse(result.isSuccess());
        assertEquals("请检查LDAP服务配置是否正确！需要配置地址、端口、节点、管理员DN、密码", result.getMessage());
    }

    @Test
    @DisplayName("部门名称冲突时应返回冲突错误")
    void syncCompany_WhenDepartmentNameConflict_ShouldReturnConflictError() {
        // Given
        when(configurationService.getLdapConfiguration()).thenReturn(mock(org.jeecg.modules.system.entity.SysLdap.class));
        when(ldapSynchronizationService.discoverOrganizationalUnitsAsTreeWithFilter("", ""))
                .thenReturn(testOrgUnits);

        LdapDepartmentSyncService spyService = spy(departmentSyncService);
        doReturn(Result.error("ConflictDepartment")).when(spyService).checkDepartmentNameConflicts(testOrgUnits);

        // When
        Result<?> result = spyService.syncCompany();

        // Then
        assertFalse(result.isSuccess());
        assertTrue(result.getMessage().contains("存在同级重名群组"));
        assertTrue(result.getMessage().contains("ConflictDepartment"));
    }

    @Test
    @DisplayName("检查部门名称冲突时无冲突应返回成功")
    void checkDepartmentNameConflicts_WhenNoConflict_ShouldReturnSuccess() {
        // Given
        List<SysDepart> existingDeparts = Arrays.asList();
        when(sysDepartService.list(any(LambdaQueryWrapper.class))).thenReturn(existingDeparts);

        // When
        Result<String> result = departmentSyncService.checkDepartmentNameConflicts(testOrgUnits);

        // Then
        assertTrue(result.isSuccess());
    }

    @Test
    @DisplayName("检查部门名称冲突时有冲突应返回冲突部门名")
    void checkDepartmentNameConflicts_WhenConflictExists_ShouldReturnConflictName() {
        // Given
        SysDepart conflictDepart = new SysDepart();
        conflictDepart.setDepartName("TestDepartment（域群组_ou）");
        List<SysDepart> existingDeparts = Arrays.asList(conflictDepart);
        when(sysDepartService.list(any(LambdaQueryWrapper.class))).thenReturn(existingDeparts);

        // When
        Result<String> result = departmentSyncService.checkDepartmentNameConflicts(testOrgUnits);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("TestDepartment（域群组_ou）", result.getMessage());
    }

    @Test
    @DisplayName("添加组织单元到系统时应正确处理新部门")
    void addOrganizationUnitsToSystem_WhenNewDepartment_ShouldCreateDepartment() {
        // Given
        when(sysDepartService.list(any(QueryWrapper.class))).thenReturn(Collections.emptyList());

        LdapDepartmentSyncService spyService = spy(departmentSyncService);
        doReturn("testuser").when(spyService).getCurrentUsername();

        SysDepart newDepart = new SysDepart();
        newDepart.setId("newdept123");
        doReturn(newDepart).when(spyService).createNewDepartment(any(), any(), any());

        // When
        Set<String> result = spyService.addOrganizationUnitsToSystem(testOrgUnits);

        // Then
        assertEquals(1, result.size());
        assertTrue(result.contains("newdept123"));
        verify(spyService).createNewDepartment(eq(testOrgUnit), eq(""), eq("testuser"));
    }

    @Test
    @DisplayName("添加组织单元到系统时应正确处理现有部门")
    void addOrganizationUnitsToSystem_WhenExistingDepartment_ShouldUseExistingDepartment() {
        // Given
        List<SysDepart> existingDeparts = Arrays.asList(testDepart);
        when(sysDepartService.list(any(QueryWrapper.class))).thenReturn(existingDeparts);

        LdapDepartmentSyncService spyService = spy(departmentSyncService);
        doReturn("testuser").when(spyService).getCurrentUsername();

        // When
        Set<String> result = spyService.addOrganizationUnitsToSystem(testOrgUnits);

        // Then
        assertEquals(1, result.size());
        assertTrue(result.contains("dept123"));
        verify(spyService, never()).createNewDepartment(any(), any(), any());
    }

    @Test
    @DisplayName("移除未使用的部门和用户时应正确清理")
    void removeUnusedDepartmentsAndUsers_ShouldCleanupCorrectly() {
        // Given
        Set<String> syncedDepartIds = Collections.singleton("dept456");
        
        List<SysDepart> allLdapDeparts = Arrays.asList(testDepart); // dept123不在syncedDepartIds中
        when(sysDepartService.list(any(QueryWrapper.class))).thenReturn(allLdapDeparts);

        // Mock用户部门关联
        SysUserDepart userDepart = new SysUserDepart();
        userDepart.setUserId("user123");
        when(sysUserDepartService.list(any(QueryWrapper.class))).thenReturn(Arrays.asList(userDepart));

        // Mock逻辑删除的用户
        SysUser deletedUser = new SysUser();
        deletedUser.setId("deleteduser123");
        when(sysUserDepartService.queryLogicDeletedUserByDepCode(any(), any()))
                .thenReturn(Arrays.asList(deletedUser));

        LdapDepartmentSyncService spyService = spy(departmentSyncService);
        doNothing().when(spyService).deleteRelations(any());

        // When
        spyService.removeUnusedDepartmentsAndUsers(syncedDepartIds);

        // Then
        verify(sysDepartService).cleanSyncLdapUserTiming(eq(Arrays.asList("dept123")));
        verify(sysUserService).removeByIds(eq(Arrays.asList("user123")));
        verify(sysUserService).removeLogicDeleted(eq(Arrays.asList("deleteduser123")));
        verify(sysDepartService).remove(any(QueryWrapper.class));
        verify(sysUserDepartService).removeByIds(any());
        verify(spyService).deleteRelations("dept123");
    }

    @Test
    @DisplayName("移除未使用的部门和用户时无未使用部门应跳过处理")
    void removeUnusedDepartmentsAndUsers_WhenNoUnusedDepartments_ShouldSkip() {
        // Given
        Set<String> syncedDepartIds = Collections.singleton("dept123");
        
        List<SysDepart> allLdapDeparts = Arrays.asList(testDepart); // dept123在syncedDepartIds中
        when(sysDepartService.list(any(QueryWrapper.class))).thenReturn(allLdapDeparts);

        // When
        departmentSyncService.removeUnusedDepartmentsAndUsers(syncedDepartIds);

        // Then
        verify(sysDepartService, never()).cleanSyncLdapUserTiming(any());
        verify(sysUserService, never()).removeByIds(any());
        verify(sysUserService, never()).removeLogicDeleted(any());
        verify(sysDepartService, never()).remove(any(QueryWrapper.class));
    }

    @Test
    @DisplayName("清空部门缓存时应删除相关缓存键")
    void clearDepartmentCache_ShouldDeleteCacheKeys() {
        // Given
        Set<String> departCacheKeys = Collections.singleton("sys:departs:cache:key1");
        Set<String> departIdCacheKeys = Collections.singleton("sys:depart:ids:cache:key1");
        
        when(redisTemplate.keys("sys:departs:cache*")).thenReturn(departCacheKeys);
        when(redisTemplate.keys("sys:depart:ids:cache*")).thenReturn(departIdCacheKeys);

        LdapDepartmentSyncService spyService = spy(departmentSyncService);
        doNothing().when(spyService).clearDepartmentCache();
        spyService.clearDepartmentCache();

        // When
        departmentSyncService.clearDepartmentCache();

        // Then
        verify(redisTemplate).delete(departCacheKeys);
        verify(redisTemplate).delete(departIdCacheKeys);
    }

    @Test
    @DisplayName("清空部门缓存时无缓存键应跳过删除")
    void clearDepartmentCache_WhenNoCacheKeys_ShouldSkipDelete() {
        // Given
        when(redisTemplate.keys("sys:departs:cache*")).thenReturn(Collections.emptySet());
        when(redisTemplate.keys("sys:depart:ids:cache*")).thenReturn(Collections.emptySet());

        // When
        departmentSyncService.clearDepartmentCache();

        // Then
        verify(redisTemplate, never()).delete(any(Collection.class));
    }

    @Test
    @DisplayName("创建新部门时应设置正确的属性")
    void createNewDepartment_ShouldSetCorrectProperties() {
        // Given
        LdapDepartmentSyncService spyService = spy(departmentSyncService);
        
        // When
        SysDepart result = spyService.createNewDepartment(testOrgUnit, "parent123", "testuser");

        // Then
        assertNotNull(result);
        assertEquals("parent123", result.getParentId());
        assertEquals("TestDepartment（域群组_ou）", result.getDepartName());
        assertEquals("ou", result.getDepartNameAbbr());
        assertEquals("1", result.getThirdId());
        assertEquals("LDAP同步", result.getThirdType());
        assertEquals("2", result.getOrgCategory()); // 有父级，应为子级
        verify(sysDepartService).saveDepartData(eq(result), eq("testuser"));
    }

    @Test
    @DisplayName("创建顶级新部门时应设置正确的组织类别")
    void createNewDepartment_WhenTopLevel_ShouldSetTopOrgCategory() {
        // Given
        LdapDepartmentSyncService spyService = spy(departmentSyncService);
        
        // When
        SysDepart result = spyService.createNewDepartment(testOrgUnit, "", "testuser");

        // Then
        assertNotNull(result);
        assertEquals("", result.getParentId());
        assertEquals("1", result.getOrgCategory()); // 无父级，应为顶级
        verify(sysDepartService).saveDepartData(eq(result), eq("testuser"));
    }
}
