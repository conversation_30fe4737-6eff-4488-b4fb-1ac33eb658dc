package org.jeecg.modules.system.service.impl.ldap;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.common.system.vo.SysConfigParam;
import org.jeecg.modules.system.entity.SysLdap;
import org.jeecg.modules.system.service.ISysConfigParamService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LDAP配置服务单元测试
 * 
 * 测试覆盖配置管理的各种场景：
 * - LDAP配置获取
 * - 配置参数查询
 * - 配置状态检查
 * - 配置值设置
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("LDAP配置服务测试")
class LdapConfigurationServiceTest {

    @InjectMocks
    private LdapConfigurationService configurationService;

    @Mock
    private ISysConfigParamService sysConfigParamService;

    private List<SysConfigParam> testConfigParams;

    @BeforeEach
    void setUp() {
        // 初始化测试配置参数
        testConfigParams = Arrays.asList(
                createConfigParam("adAddress", "ldap.test.com"),
                createConfigParam("adPort", "389"),
                createConfigParam("addomainName", "dc=test,dc=com"),
                createConfigParam("adName", "admin"),
                createConfigParam("adPasswd", "password"),
                createConfigParam("ldapSyncFilter", "(objectClass=person)"),
                createConfigParam("ldapServerType", "1"), // AD
                createConfigParam("ldapEncryptType", "1"), // LDAP
                createConfigParam("ldapLoginUserAttr", "sAMAccountName"),
                createConfigParam("ldapUsernameAttr", "displayName"),
                createConfigParam("ldapEmailAttr", "mail"),
                createConfigParam("ldapPhoneAttr", "telephoneNumber"),
                createConfigParam("ldapDescriptionAttr", "description"),
                createConfigParam("ldapRole", "admin,user"),
                createConfigParam("ldapDepart", "dept1,dept2"),
                createConfigParam("adOpen", "1"),
                createConfigParam("adVerification", "0"),
                createConfigParam("ldapOverwriteSysUser", "1"),
                createConfigParam("adUserStatus", "1")
        );
    }

    @Test
    @DisplayName("获取LDAP配置时应正确设置所有属性")
    void getLdapConfiguration_ShouldSetAllPropertiesCorrectly() {
        // Given
        when(sysConfigParamService.list(any(QueryWrapper.class))).thenReturn(testConfigParams);

        // When
        SysLdap result = configurationService.getLdapConfiguration();

        // Then
        assertNotNull(result);
        assertEquals("ldap.test.com", result.getAdAddress());
        assertEquals("389", result.getAdPort());
        assertEquals("dc=test,dc=com", result.getAddomainName());
        assertEquals("admin", result.getAdName());
        assertEquals("password", result.getAdPasswd());
        assertEquals("(objectClass=person)", result.getLdapSyncFilter());
        assertEquals(SysLdap.ServerType.AD, result.getLdapServerType());
        assertEquals(SysLdap.EncryptType.LDAP, result.getLdapEncryptType());
        assertEquals("admin,user", result.getLdapRole());
        assertEquals("dept1,dept2", result.getLdapDepart());

        // 验证属性映射字段
        SysLdap.AttrMapperField attrMapper = result.getAttrMapperField();
        assertNotNull(attrMapper);
        assertEquals("sAMAccountName", attrMapper.getLdapLoginUserAttr());
        assertEquals("displayName", attrMapper.getLdapUsernameAttr());
        assertEquals("mail", attrMapper.getLdapEmailAttr());
        assertEquals("telephoneNumber", attrMapper.getLdapPhoneAttr());
        assertEquals("description", attrMapper.getLdapDescriptionAttr());
    }

    @Test
    @DisplayName("获取LDAP配置时缺少参数应使用默认值")
    void getLdapConfiguration_WhenMissingParams_ShouldUseDefaults() {
        // Given
        List<SysConfigParam> partialParams = Arrays.asList(
                createConfigParam("adAddress", "ldap.test.com"),
                createConfigParam("ldapServerType", "2") // OpenLDAP
        );
        when(sysConfigParamService.list(any(QueryWrapper.class))).thenReturn(partialParams);

        // When
        SysLdap result = configurationService.getLdapConfiguration();

        // Then
        assertNotNull(result);
        assertEquals("ldap.test.com", result.getAdAddress());
        assertEquals(SysLdap.ServerType.OPEN_LDAP, result.getLdapServerType());
        assertNull(result.getAdPort()); // 未设置的参数应为null
        
        // 属性映射字段应调用默认值设置
        assertNotNull(result.getAttrMapperField());
    }

    @Test
    @DisplayName("获取指定配置参数时应返回正确的映射")
    void getConfigParams_ShouldReturnCorrectMapping() {
        // Given
        List<SysConfigParam> selectedParams = Arrays.asList(
                createConfigParam("adAddress", "ldap.test.com"),
                createConfigParam("adPort", "389")
        );
        when(sysConfigParamService.list(any(QueryWrapper.class))).thenReturn(selectedParams);

        // When
        Map<String, String> result = configurationService.getConfigParams("adAddress", "adPort");

        // Then
        assertEquals(2, result.size());
        assertEquals("ldap.test.com", result.get("adAddress"));
        assertEquals("389", result.get("adPort"));
    }

    @Test
    @DisplayName("获取单个配置参数时应返回正确的值")
    void getConfigParam_ShouldReturnCorrectValue() {
        // Given
        when(sysConfigParamService.getSysConfigParamByKey("adAddress")).thenReturn("ldap.test.com");

        // When
        String result = configurationService.getConfigParam("adAddress");

        // Then
        assertEquals("ldap.test.com", result);
    }

    @Test
    @DisplayName("获取单个配置参数带默认值时应正确处理")
    void getConfigParam_WithDefaultValue_ShouldHandleCorrectly() {
        // Given
        when(sysConfigParamService.getSysConfigParamByKey("nonExistentKey")).thenReturn(null);

        // When
        String result = configurationService.getConfigParam("nonExistentKey", "defaultValue");

        // Then
        assertEquals("defaultValue", result);
    }

    @Test
    @DisplayName("获取单个配置参数带默认值时有值应返回实际值")
    void getConfigParam_WithDefaultValue_WhenHasValue_ShouldReturnActualValue() {
        // Given
        when(sysConfigParamService.getSysConfigParamByKey("adAddress")).thenReturn("ldap.test.com");

        // When
        String result = configurationService.getConfigParam("adAddress", "defaultValue");

        // Then
        assertEquals("ldap.test.com", result);
    }

    @Test
    @DisplayName("检查LDAP是否开启时应返回正确状态")
    void isLdapEnabled_ShouldReturnCorrectStatus() {
        // Given
        when(sysConfigParamService.getSysConfigParamByKey("adOpen")).thenReturn("1");

        // When
        boolean result = configurationService.isLdapEnabled();

        // Then
        assertTrue(result);
    }

    @Test
    @DisplayName("检查LDAP是否开启时未开启应返回false")
    void isLdapEnabled_WhenDisabled_ShouldReturnFalse() {
        // Given
        when(sysConfigParamService.getSysConfigParamByKey("adOpen")).thenReturn("0");

        // When
        boolean result = configurationService.isLdapEnabled();

        // Then
        assertFalse(result);
    }

    @Test
    @DisplayName("检查是否允许未同步用户登录时应返回正确状态")
    void isUnSyncedLoginAllowed_ShouldReturnCorrectStatus() {
        // Given
        when(sysConfigParamService.getSysConfigParamByKey("adVerification")).thenReturn("0");

        // When
        boolean result = configurationService.isUnSyncedLoginAllowed();

        // Then
        assertTrue(result); // adVerification不等于"1"时允许未同步用户登录
    }

    @Test
    @DisplayName("检查是否允许未同步用户登录时严格模式应返回false")
    void isUnSyncedLoginAllowed_WhenStrictMode_ShouldReturnFalse() {
        // Given
        when(sysConfigParamService.getSysConfigParamByKey("adVerification")).thenReturn("1");

        // When
        boolean result = configurationService.isUnSyncedLoginAllowed();

        // Then
        assertFalse(result); // adVerification等于"1"时不允许未同步用户登录
    }

    @Test
    @DisplayName("检查是否覆盖重名用户时应返回正确状态")
    void isOverwriteSysUser_ShouldReturnCorrectStatus() {
        // Given
        when(sysConfigParamService.getSysConfigParamByKey("ldapOverwriteSysUser")).thenReturn("1");

        // When
        boolean result = configurationService.isOverwriteSysUser();

        // Then
        assertTrue(result);
    }

    @Test
    @DisplayName("检查是否覆盖重名用户时未开启应返回false")
    void isOverwriteSysUser_WhenDisabled_ShouldReturnFalse() {
        // Given
        when(sysConfigParamService.getSysConfigParamByKey("ldapOverwriteSysUser")).thenReturn("0");

        // When
        boolean result = configurationService.isOverwriteSysUser();

        // Then
        assertFalse(result);
    }

    @Test
    @DisplayName("获取AD用户状态时应返回配置值")
    void getAdUserStatus_ShouldReturnConfigValue() {
        // Given
        when(sysConfigParamService.getSysConfigParamByKey("adUserStatus")).thenReturn("1");

        // When
        String result = configurationService.getAdUserStatus();

        // Then
        assertEquals("1", result);
    }

    @Test
    @DisplayName("获取AD用户状态时无配置应返回默认值")
    void getAdUserStatus_WhenNoConfig_ShouldReturnDefaultValue() {
        // Given
        when(sysConfigParamService.getSysConfigParamByKey("adUserStatus")).thenReturn(null);

        // When
        String result = configurationService.getAdUserStatus();

        // Then
        assertEquals("2", result); // 默认值
    }

    @Test
    @DisplayName("配置值设置逻辑应通过getLdapConfiguration方法验证")
    void configValueSetting_ShouldBeVerifiedThroughGetLdapConfiguration() {
        // Given
        List<SysConfigParam> testParams = Arrays.asList(
                createConfigParam("adAddress", "test.com"),
                createConfigParam("ldapServerType", "1"),
                createConfigParam("ldapEncryptType", "2"),
                createConfigParam("ldapLoginUserAttr", "uid")
        );
        when(sysConfigParamService.list(any(QueryWrapper.class))).thenReturn(testParams);

        // When
        SysLdap result = configurationService.getLdapConfiguration();

        // Then
        assertEquals("test.com", result.getAdAddress());
        assertEquals(SysLdap.ServerType.AD, result.getLdapServerType());
        assertEquals(SysLdap.EncryptType.LDAPS, result.getLdapEncryptType());
        assertEquals("uid", result.getAttrMapperField().getLdapLoginUserAttr());
    }

    /**
     * 创建配置参数对象的辅助方法
     */
    private SysConfigParam createConfigParam(String key, String value) {
        SysConfigParam param = new SysConfigParam();
        param.setParamKey(key);
        param.setParamValue(value);
        return param;
    }
}
