package org.jeecg.modules.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.system.entity.Person;
import org.jeecg.modules.system.model.LdapConnectTestModel;
import org.jeecg.modules.system.model.LdapModel;
import org.jeecg.modules.system.model.SysLoginModel;
import org.jeecg.modules.system.service.impl.ldap.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.core.support.LdapContextSource;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LDAP服务实现类单元测试
 * 
 * 测试覆盖主服务类的门面功能：
 * - 各个子服务的协调调用
 * - 接口方法的正确委托
 * - 返回结果的正确传递
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("LDAP服务实现类测试")
class LdapServiceImplTest {

    @InjectMocks
    private LdapServiceImpl ldapService;

    @Mock
    private LdapConnectionService connectionService;

    @Mock
    private LdapConfigurationService configurationService;

    @Mock
    private LdapDepartmentSyncService departmentSyncService;

    @Mock
    private LdapUserSyncService userSyncService;

    @Mock
    private LdapAuthenticationService authenticationService;

    @Mock
    private LdapJobService jobService;

    private LdapContextSource mockContextSource;
    private LdapTemplate mockLdapTemplate;
    private Person testPerson;
    private SysLoginModel testLoginModel;
    private LdapConnectTestModel testConnectModel;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockContextSource = mock(LdapContextSource.class);
        mockLdapTemplate = mock(LdapTemplate.class);

        testPerson = new Person();
        testPerson.setLoginName("testuser");
        testPerson.setUserName("Test User");

        testLoginModel = new SysLoginModel();
        testLoginModel.setUsername("testuser");
        testLoginModel.setPassword("password");

        testConnectModel = new LdapConnectTestModel();
        testConnectModel.setAdAddress("ldap.test.com");
        testConnectModel.setAdPort("389");
    }

    @Test
    @DisplayName("创建上下文源时应委托给连接服务")
    void contextSource_ShouldDelegateToConnectionService() {
        // Given
        when(connectionService.createContextSource()).thenReturn(mockContextSource);

        // When
        LdapContextSource result = ldapService.contextSource();

        // Then
        assertEquals(mockContextSource, result);
        verify(connectionService).createContextSource();
    }

    @Test
    @DisplayName("创建LDAP模板时应委托给连接服务")
    void ldapTemplate_ShouldDelegateToConnectionService() {
        // Given
        when(connectionService.createLdapTemplate(mockContextSource)).thenReturn(mockLdapTemplate);

        // When
        LdapTemplate result = ldapService.ldapTemplate(mockContextSource);

        // Then
        assertEquals(mockLdapTemplate, result);
        verify(connectionService).createLdapTemplate(mockContextSource);
    }

    @Test
    @DisplayName("从组织单元查找用户时应委托给用户同步服务")
    void findUsersFromOrgnizationalUnit_ShouldDelegateToUserSyncService() {
        // Given
        String base = "OU=Users,DC=test,DC=com";
        List<Person> expectedUsers = Arrays.asList(testPerson);
        when(userSyncService.findUsersFromOrganizationalUnit(base)).thenReturn(expectedUsers);

        // When
        List<Person> result = ldapService.findUsersFromOrgnizationalUnit(base);

        // Then
        assertEquals(expectedUsers, result);
        verify(userSyncService).findUsersFromOrganizationalUnit(base);
    }

    @Test
    @DisplayName("同步公司结构时应委托给部门同步服务")
    void sczCompany_ShouldDelegateToDepartmentSyncService() {
        // Given
        Result<?> expectedResult = Result.OK();
        when(departmentSyncService.syncCompany()).thenReturn(expectedResult);

        // When
        Result<?> result = ldapService.sczCompany();

        // Then
        assertEquals(expectedResult, result);
        verify(departmentSyncService).syncCompany();
    }

    @Test
    @DisplayName("同步用户时应委托给用户同步服务")
    void sczUser_ShouldDelegateToUserSyncService() {
        // Given
        int status = 1;
        String departId = "dept123";
        LdapModel ldapModel = new LdapModel();

        // When
        ldapService.sczUser(status, departId, ldapModel);

        // Then
        verify(userSyncService).syncUsers(status, departId, ldapModel);
    }

    @Test
    @DisplayName("验证部门用户同步时应委托给用户同步服务")
    void verification_ShouldDelegateToUserSyncService() {
        // Given
        String departId = "dept123";
        Result<?> expectedResult = Result.OK();
        when(userSyncService.verifyDepartmentUserSync(departId)).thenReturn(expectedResult);

        // When
        Result<?> result = ldapService.verification(departId);

        // Then
        assertEquals(expectedResult, result);
        verify(userSyncService).verifyDepartmentUserSync(departId);
    }

    @Test
    @DisplayName("用户认证时应委托给认证服务")
    void authenticate_ShouldDelegateToAuthenticationService() {
        // Given
        Result<JSONObject> expectedResult = Result.OK();
        when(authenticationService.authenticate(testLoginModel)).thenReturn(expectedResult);

        // When
        Result<JSONObject> result = ldapService.authenticate(testLoginModel);

        // Then
        assertEquals(expectedResult, result);
        verify(authenticationService).authenticate(testLoginModel);
    }

    @Test
    @DisplayName("搜索LDAP用户时应委托给用户同步服务")
    void searchLdapUser_ShouldDelegateToUserSyncService() {
        // Given
        String username = "testuser";
        when(userSyncService.searchLdapUser(username)).thenReturn(testPerson);

        // When
        Person result = ldapService.searchLdapUser(username);

        // Then
        assertEquals(testPerson, result);
        verify(userSyncService).searchLdapUser(username);
    }

    @Test
    @DisplayName("测试LDAP连接时应委托给连接服务")
    void testLdapConnect_ShouldDelegateToConnectionService() {
        // Given
        Result<?> expectedResult = Result.OK("连接成功");
        when(connectionService.testConnection(testConnectModel)).thenReturn(expectedResult);

        // When
        Result<?> result = ldapService.testLdapConnect(testConnectModel);

        // Then
        assertEquals(expectedResult, result);
        verify(connectionService).testConnection(testConnectModel);
    }

    @Test
    @DisplayName("管理定时部门用户同步时应委托给任务服务")
    void timingDepartUser_ShouldDelegateToJobService() {
        // Given
        int adTimingOpen = 1;
        String adCronExpression = "0 0 2 * * ?";
        String adParentId = "dept123";
        Integer adXxlJobInfoId = 456;
        Result<?> expectedResult = Result.OK(789);
        
        when(jobService.manageTimingDepartUser(adTimingOpen, adCronExpression, adParentId, adXxlJobInfoId))
                .thenReturn(expectedResult);

        // When
        Result<?> result = ldapService.timingDepartUser(adTimingOpen, adCronExpression, adParentId, adXxlJobInfoId);

        // Then
        assertEquals(expectedResult, result);
        verify(jobService).manageTimingDepartUser(adTimingOpen, adCronExpression, adParentId, adXxlJobInfoId);
    }

    @Test
    @DisplayName("所有委托方法应正确传递参数")
    void allDelegateMethods_ShouldPassParametersCorrectly() {
        // Given - 设置所有mock返回值
        when(connectionService.createContextSource()).thenReturn(mockContextSource);
        when(connectionService.createLdapTemplate(any())).thenReturn(mockLdapTemplate);
        when(userSyncService.findUsersFromOrganizationalUnit(any())).thenReturn(Arrays.asList(testPerson));
        when(departmentSyncService.syncCompany()).thenReturn(Result.OK());
        when(userSyncService.verifyDepartmentUserSync(any())).thenReturn(Result.OK());
        when(authenticationService.authenticate(any())).thenReturn(Result.OK());
        when(userSyncService.searchLdapUser(any())).thenReturn(testPerson);
        when(connectionService.testConnection(any())).thenReturn(Result.OK());
        when(jobService.manageTimingDepartUser(anyInt(), any(), any(), any())).thenReturn(Result.OK());

        // When - 调用所有方法
        ldapService.contextSource();
        ldapService.ldapTemplate(mockContextSource);
        ldapService.findUsersFromOrgnizationalUnit("base");
        ldapService.sczCompany();
        ldapService.sczUser(1, "dept", new LdapModel());
        ldapService.verification("dept");
        ldapService.authenticate(testLoginModel);
        ldapService.searchLdapUser("user");
        ldapService.testLdapConnect(testConnectModel);
        ldapService.timingDepartUser(1, "cron", "parent", 123);

        // Then - 验证所有委托调用
        verify(connectionService).createContextSource();
        verify(connectionService).createLdapTemplate(mockContextSource);
        verify(userSyncService).findUsersFromOrganizationalUnit("base");
        verify(departmentSyncService).syncCompany();
        verify(userSyncService).syncUsers(eq(1), eq("dept"), any(LdapModel.class));
        verify(userSyncService).verifyDepartmentUserSync("dept");
        verify(authenticationService).authenticate(testLoginModel);
        verify(userSyncService).searchLdapUser("user");
        verify(connectionService).testConnection(testConnectModel);
        verify(jobService).manageTimingDepartUser(1, "cron", "parent", 123);
    }

    @Test
    @DisplayName("服务异常时应正确传播异常")
    void serviceExceptions_ShouldBePropagatedCorrectly() {
        // Given
        RuntimeException testException = new RuntimeException("Test exception");
        when(userSyncService.searchLdapUser(any())).thenThrow(testException);

        // When & Then
        assertThrows(RuntimeException.class, () -> ldapService.searchLdapUser("testuser"));
        verify(userSyncService).searchLdapUser("testuser");
    }

    @Test
    @DisplayName("服务返回null时应正确处理")
    void serviceReturnsNull_ShouldBeHandledCorrectly() {
        // Given
        when(userSyncService.searchLdapUser(any())).thenReturn(null);

        // When
        Person result = ldapService.searchLdapUser("testuser");

        // Then
        assertNull(result);
        verify(userSyncService).searchLdapUser("testuser");
    }
}
