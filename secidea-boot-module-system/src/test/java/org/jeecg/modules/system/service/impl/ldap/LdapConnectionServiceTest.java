package org.jeecg.modules.system.service.impl.ldap;

import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.ldap.config.LdapAttributeMapping;
import org.jeecg.modules.ldap.config.LdapServerType;
import org.jeecg.modules.ldap.service.LdapSynchronizationService;
import org.jeecg.modules.system.entity.SysLdap;
import org.jeecg.modules.system.model.LdapConnectTestModel;
import org.jeecg.modules.system.util.ldap.SSLLdapContextSource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.core.support.LdapContextSource;

import javax.naming.AuthenticationException;
import javax.naming.NamingException;
import javax.naming.directory.DirContext;
import javax.naming.directory.InitialDirContext;
import java.util.Hashtable;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LDAP连接服务单元测试
 * 
 * 测试覆盖LDAP连接的各种场景：
 * - 上下文源创建
 * - LDAP模板创建
 * - 连接测试
 * - 配置更新
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("LDAP连接服务测试")
class LdapConnectionServiceTest {

    @InjectMocks
    private LdapConnectionService connectionService;

    @Mock
    private LdapConfigurationService configurationService;

    @Mock
    private LdapSynchronizationService ldapSynchronizationService;

    private SysLdap testLdapConfig;
    private LdapConnectTestModel testConnectModel;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testLdapConfig = new SysLdap();
        testLdapConfig.setAdAddress("ldap.test.com");
        testLdapConfig.setAdPort("389");
        testLdapConfig.setAddomainName("dc=test,dc=com");
        testLdapConfig.setAdName("admin");
        testLdapConfig.setAdPasswd("password");
        testLdapConfig.setLdapEncryptType(SysLdap.EncryptType.LDAP);
        testLdapConfig.setLdapServerType(SysLdap.ServerType.AD);

        SysLdap.AttrMapperField attrMapper = new SysLdap.AttrMapperField();
        attrMapper.setLdapLoginUserAttr("sAMAccountName");
        attrMapper.setLdapUsernameAttr("displayName");
        attrMapper.setLdapEmailAttr("mail");
        attrMapper.setLdapPhoneAttr("telephoneNumber");
        attrMapper.setLdapDescriptionAttr("description");
        testLdapConfig.setAttrMapperField(attrMapper);

        testConnectModel = new LdapConnectTestModel();
        testConnectModel.setAdAddress("ldap.test.com");
        testConnectModel.setAdPort("389");
        testConnectModel.setAddomainName("dc=test,dc=com");
        testConnectModel.setAdName("testuser");
        testConnectModel.setAdPasswd("testpass");
        testConnectModel.setLdapEncryptType("1"); // LDAP
    }

    @Test
    @DisplayName("创建LDAP上下文源时应设置正确的属性")
    void createContextSource_ShouldSetCorrectProperties() {
        // Given
        when(configurationService.getLdapConfiguration()).thenReturn(testLdapConfig);

        // When
        LdapContextSource result = connectionService.createContextSource();

        // Then
        assertNotNull(result);
        // 由于LdapContextSource的属性是私有的，我们主要验证方法调用
        verify(configurationService).getLdapConfiguration();
    }

    @Test
    @DisplayName("创建SSL LDAP上下文源时应使用SSL配置")
    void createContextSource_WhenSSL_ShouldUseSSLContextSource() {
        // Given
        testLdapConfig.setLdapEncryptType(SysLdap.EncryptType.LDAPS);
        when(configurationService.getLdapConfiguration()).thenReturn(testLdapConfig);

        // When
        LdapContextSource result = connectionService.createContextSource();

        // Then
        assertNotNull(result);
        // SSL配置会创建SSLLdapContextSource，但由于返回类型是父类，我们验证配置调用
        verify(configurationService).getLdapConfiguration();
    }

    @Test
    @DisplayName("创建LDAP模板时应使用提供的上下文源")
    void createLdapTemplate_ShouldUseProvidedContextSource() {
        // Given
        LdapContextSource contextSource = mock(LdapContextSource.class);

        // When
        LdapTemplate result = connectionService.createLdapTemplate(contextSource);

        // Then
        assertNotNull(result);
    }

    @Test
    @DisplayName("创建LDAP模板时无参数应创建默认上下文源")
    void createLdapTemplate_WhenNoParameter_ShouldCreateDefaultContextSource() {
        // Given
        when(configurationService.getLdapConfiguration()).thenReturn(testLdapConfig);

        // When
        LdapTemplate result = connectionService.createLdapTemplate();

        // Then
        assertNotNull(result);
        verify(configurationService).getLdapConfiguration();
    }

    @Test
    @DisplayName("测试连接时地址为空应返回错误")
    void testConnection_WhenAddressEmpty_ShouldReturnError() {
        // Given
        testConnectModel.setAdAddress("");

        // When
        Result<?> result = connectionService.testConnection(testConnectModel);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("LDAP地址不能为空！", result.getMessage());
    }

    @Test
    @DisplayName("测试连接时端口为空应返回错误")
    void testConnection_WhenPortEmpty_ShouldReturnError() {
        // Given
        testConnectModel.setAdPort("");

        // When
        Result<?> result = connectionService.testConnection(testConnectModel);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("LDAP端口不能为空！", result.getMessage());
    }

    @Test
    @DisplayName("测试连接时有用户名但密码为空应返回错误")
    void testConnection_WhenUsernameButNoPassword_ShouldReturnError() {
        // Given
        testConnectModel.setAdPasswd("");

        // When
        Result<?> result = connectionService.testConnection(testConnectModel);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("输入测试账号，密码不能为空！", result.getMessage());
    }

    @Test
    @DisplayName("测试连接成功时应返回成功结果")
    void testConnection_WhenSuccessful_ShouldReturnSuccess() {
        // Given
        try (MockedStatic<InitialDirContext> mockedStatic = mockStatic(InitialDirContext.class)) {
            DirContext mockContext = mock(DirContext.class);
            mockedStatic.when(() -> new InitialDirContext(any(Hashtable.class))).thenReturn(mockContext);

            // When
            Result<?> result = connectionService.testConnection(testConnectModel);

            // Then
            assertTrue(result.isSuccess());
            assertEquals("测试连接成功!", result.getMessage());
            verify(mockContext).close();
        } catch (Exception e) {
            fail("测试执行异常: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试连接时认证失败应返回认证错误")
    void testConnection_WhenAuthenticationFailed_ShouldReturnAuthError() {
        // Given
        try (MockedStatic<InitialDirContext> mockedStatic = mockStatic(InitialDirContext.class)) {
            mockedStatic.when(() -> new InitialDirContext(any(Hashtable.class)))
                    .thenThrow(new AuthenticationException("Authentication failed"));

            // When
            Result<?> result = connectionService.testConnection(testConnectModel);

            // Then
            assertFalse(result.isSuccess());
            assertEquals("LDAP身份验证失败, 请检查账号密码是否正确！", result.getMessage());
        }
    }

    @Test
    @DisplayName("测试连接时命名异常应返回连接错误")
    void testConnection_WhenNamingException_ShouldReturnConnectionError() {
        // Given
        try (MockedStatic<InitialDirContext> mockedStatic = mockStatic(InitialDirContext.class)) {
            mockedStatic.when(() -> new InitialDirContext(any(Hashtable.class)))
                    .thenThrow(new NamingException("Connection failed"));

            // When
            Result<?> result = connectionService.testConnection(testConnectModel);

            // Then
            assertFalse(result.isSuccess());
            assertEquals("LDAP连接失败，请检查认证协议、LDAP服务器地址、端口号是否正确，端口号是否与认证协议对应的端口一致！", result.getMessage());
        }
    }

    @Test
    @DisplayName("测试连接时其他异常应返回未知异常错误")
    void testConnection_WhenOtherException_ShouldReturnUnknownError() {
        // Given
        try (MockedStatic<InitialDirContext> mockedStatic = mockStatic(InitialDirContext.class)) {
            mockedStatic.when(() -> new InitialDirContext(any(Hashtable.class)))
                    .thenThrow(new RuntimeException("Unknown error"));

            // When
            Result<?> result = connectionService.testConnection(testConnectModel);

            // Then
            assertFalse(result.isSuccess());
            assertEquals("LDAP身份验证未知异常！", result.getMessage());
        }
    }

    @Test
    @DisplayName("更新LDAP配置时应正确配置同步服务")
    void updateLdapConfiguration_ShouldConfigureSynchronizationService() {
        // Given
        // When
        connectionService.updateLdapConfiguration(testLdapConfig);

        // Then
        verify(ldapSynchronizationService).configureLdapConnection(
                any(LdapContextSource.class),
                eq(LdapServerType.ACTIVE_DIRECTORY),
                any(LdapAttributeMapping.class)
        );
    }

    @Test
    @DisplayName("更新LDAP配置时OpenLDAP服务器应使用正确的服务器类型")
    void updateLdapConfiguration_WhenOpenLDAP_ShouldUseCorrectServerType() {
        // Given
        testLdapConfig.setLdapServerType(SysLdap.ServerType.OPEN_LDAP);

        // When
        connectionService.updateLdapConfiguration(testLdapConfig);

        // Then
        verify(ldapSynchronizationService).configureLdapConnection(
                any(LdapContextSource.class),
                eq(LdapServerType.OPENLDAP),
                any(LdapAttributeMapping.class)
        );
    }

    @Test
    @DisplayName("测试连接时无用户名应跳过用户认证")
    void testConnection_WhenNoUsername_ShouldSkipUserAuth() {
        // Given
        testConnectModel.setAdName("");
        testConnectModel.setAdPasswd("");

        try (MockedStatic<InitialDirContext> mockedStatic = mockStatic(InitialDirContext.class)) {
            DirContext mockContext = mock(DirContext.class);
            mockedStatic.when(() -> new InitialDirContext(any(Hashtable.class))).thenReturn(mockContext);

            // When
            Result<?> result = connectionService.testConnection(testConnectModel);

            // Then
            assertTrue(result.isSuccess());
            assertEquals("测试连接成功!", result.getMessage());
        } catch (Exception e) {
            fail("测试执行异常: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试SSL连接时应设置SSL相关属性")
    void testConnection_WhenSSL_ShouldSetSSLProperties() {
        // Given
        testConnectModel.setLdapEncryptType("2"); // LDAPS

        try (MockedStatic<InitialDirContext> mockedStatic = mockStatic(InitialDirContext.class)) {
            DirContext mockContext = mock(DirContext.class);
            mockedStatic.when(() -> new InitialDirContext(any(Hashtable.class))).thenReturn(mockContext);

            // When
            Result<?> result = connectionService.testConnection(testConnectModel);

            // Then
            assertTrue(result.isSuccess());
            assertEquals("测试连接成功!", result.getMessage());
            // 验证SSL相关的环境属性设置
            mockedStatic.verify(() -> new InitialDirContext(argThat(env -> {
                Hashtable<String, String> hashtable = (Hashtable<String, String>) env;
                return hashtable.get("java.naming.security.protocol").equals("ssl") &&
                       hashtable.get("java.naming.provider.url").startsWith("ldaps://");
            })));
        } catch (Exception e) {
            fail("测试执行异常: " + e.getMessage());
        }
    }
}
