package org.jeecg.modules.system.service.impl.ldap;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * 测试反射工具类
 * 
 * 提供便捷的反射操作方法，用于测试私有方法、字段和内部类。
 * 简化测试代码中的反射操作，提高测试代码的可读性和可维护性。
 * 
 * <AUTHOR>
 */
public class TestReflectionUtils {

    /**
     * 调用私有方法
     * 
     * @param target 目标对象
     * @param methodName 方法名
     * @param parameterTypes 参数类型
     * @param args 参数值
     * @return 方法返回值
     * @throws Exception 反射异常
     */
    public static Object invokePrivateMethod(Object target, String methodName, 
                                           Class<?>[] parameterTypes, Object... args) throws Exception {
        Method method = target.getClass().getDeclaredMethod(methodName, parameterTypes);
        method.setAccessible(true);
        return method.invoke(target, args);
    }

    /**
     * 调用私有方法（无参数）
     * 
     * @param target 目标对象
     * @param methodName 方法名
     * @return 方法返回值
     * @throws Exception 反射异常
     */
    public static Object invokePrivateMethod(Object target, String methodName) throws Exception {
        return invokePrivateMethod(target, methodName, new Class<?>[0]);
    }

    /**
     * 调用私有方法（单个参数）
     * 
     * @param target 目标对象
     * @param methodName 方法名
     * @param parameterType 参数类型
     * @param arg 参数值
     * @return 方法返回值
     * @throws Exception 反射异常
     */
    public static Object invokePrivateMethod(Object target, String methodName, 
                                           Class<?> parameterType, Object arg) throws Exception {
        return invokePrivateMethod(target, methodName, new Class<?>[]{parameterType}, arg);
    }

    /**
     * 获取私有字段值
     * 
     * @param target 目标对象
     * @param fieldName 字段名
     * @return 字段值
     * @throws Exception 反射异常
     */
    public static Object getPrivateField(Object target, String fieldName) throws Exception {
        Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        return field.get(target);
    }

    /**
     * 设置私有字段值
     * 
     * @param target 目标对象
     * @param fieldName 字段名
     * @param value 字段值
     * @throws Exception 反射异常
     */
    public static void setPrivateField(Object target, String fieldName, Object value) throws Exception {
        Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }

    /**
     * 创建内部类实例
     * 
     * @param outerInstance 外部类实例
     * @param innerClassName 内部类名（不包含外部类名）
     * @param parameterTypes 构造函数参数类型
     * @param args 构造函数参数值
     * @return 内部类实例
     * @throws Exception 反射异常
     */
    public static Object createInnerClassInstance(Object outerInstance, String innerClassName,
                                                Class<?>[] parameterTypes, Object... args) throws Exception {
        Class<?> outerClass = outerInstance.getClass();
        String fullInnerClassName = outerClass.getName() + "$" + innerClassName;
        Class<?> innerClass = Class.forName(fullInnerClassName);
        
        // 非静态内部类需要外部类实例作为第一个参数
        Class<?>[] constructorTypes = new Class<?>[parameterTypes.length + 1];
        constructorTypes[0] = outerClass;
        System.arraycopy(parameterTypes, 0, constructorTypes, 1, parameterTypes.length);
        
        Object[] constructorArgs = new Object[args.length + 1];
        constructorArgs[0] = outerInstance;
        System.arraycopy(args, 0, constructorArgs, 1, args.length);
        
        Constructor<?> constructor = innerClass.getDeclaredConstructor(constructorTypes);
        constructor.setAccessible(true);
        return constructor.newInstance(constructorArgs);
    }

    /**
     * 创建内部类实例（无参构造函数）
     * 
     * @param outerInstance 外部类实例
     * @param innerClassName 内部类名
     * @return 内部类实例
     * @throws Exception 反射异常
     */
    public static Object createInnerClassInstance(Object outerInstance, String innerClassName) throws Exception {
        return createInnerClassInstance(outerInstance, innerClassName, new Class<?>[0]);
    }

    /**
     * 获取静态私有字段值
     * 
     * @param clazz 目标类
     * @param fieldName 字段名
     * @return 字段值
     * @throws Exception 反射异常
     */
    public static Object getStaticPrivateField(Class<?> clazz, String fieldName) throws Exception {
        Field field = clazz.getDeclaredField(fieldName);
        field.setAccessible(true);
        return field.get(null);
    }

    /**
     * 设置静态私有字段值
     * 
     * @param clazz 目标类
     * @param fieldName 字段名
     * @param value 字段值
     * @throws Exception 反射异常
     */
    public static void setStaticPrivateField(Class<?> clazz, String fieldName, Object value) throws Exception {
        Field field = clazz.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(null, value);
    }

    /**
     * 调用静态私有方法
     * 
     * @param clazz 目标类
     * @param methodName 方法名
     * @param parameterTypes 参数类型
     * @param args 参数值
     * @return 方法返回值
     * @throws Exception 反射异常
     */
    public static Object invokeStaticPrivateMethod(Class<?> clazz, String methodName,
                                                 Class<?>[] parameterTypes, Object... args) throws Exception {
        Method method = clazz.getDeclaredMethod(methodName, parameterTypes);
        method.setAccessible(true);
        return method.invoke(null, args);
    }

    /**
     * 检查对象是否具有指定的私有方法
     * 
     * @param target 目标对象
     * @param methodName 方法名
     * @param parameterTypes 参数类型
     * @return 是否存在该方法
     */
    public static boolean hasPrivateMethod(Object target, String methodName, Class<?>... parameterTypes) {
        try {
            Method method = target.getClass().getDeclaredMethod(methodName, parameterTypes);
            return method != null;
        } catch (NoSuchMethodException e) {
            return false;
        }
    }

    /**
     * 检查对象是否具有指定的私有字段
     * 
     * @param target 目标对象
     * @param fieldName 字段名
     * @return 是否存在该字段
     */
    public static boolean hasPrivateField(Object target, String fieldName) {
        try {
            Field field = target.getClass().getDeclaredField(fieldName);
            return field != null;
        } catch (NoSuchFieldException e) {
            return false;
        }
    }

    /**
     * 获取方法对象（用于复杂的反射操作）
     * 
     * @param clazz 目标类
     * @param methodName 方法名
     * @param parameterTypes 参数类型
     * @return 方法对象
     * @throws Exception 反射异常
     */
    public static Method getMethod(Class<?> clazz, String methodName, Class<?>... parameterTypes) throws Exception {
        Method method = clazz.getDeclaredMethod(methodName, parameterTypes);
        method.setAccessible(true);
        return method;
    }

    /**
     * 获取字段对象（用于复杂的反射操作）
     * 
     * @param clazz 目标类
     * @param fieldName 字段名
     * @return 字段对象
     * @throws Exception 反射异常
     */
    public static Field getField(Class<?> clazz, String fieldName) throws Exception {
        Field field = clazz.getDeclaredField(fieldName);
        field.setAccessible(true);
        return field;
    }
}
