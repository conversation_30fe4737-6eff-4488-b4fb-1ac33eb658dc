package org.jeecg.modules.system.service.impl.ldap;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.base.service.BaseCommonService;
import org.jeecg.modules.system.controller.LoginController;
import org.jeecg.modules.system.entity.SysLdap;
import org.jeecg.modules.system.entity.SysRole;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.model.LdapConnectTestModel;
import org.jeecg.modules.system.model.SysLoginModel;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.system.service.impl.SysRoleServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LDAP认证服务单元测试
 * 
 * 测试覆盖LDAP用户认证的各种场景：
 * - 正常认证流程
 * - 各种错误情况处理
 * - 新用户创建逻辑
 * - 现有用户验证逻辑
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("LDAP认证服务测试")
class LdapAuthenticationServiceTest {

    @InjectMocks
    private LdapAuthenticationService authenticationService;

    @Mock
    private LdapConfigurationService configurationService;

    @Mock
    private LdapConnectionService connectionService;

    @Mock
    private LdapUserSyncService userSyncService;

    @Mock
    private ISysUserService sysUserService;

    @Mock
    private SysRoleServiceImpl sysRoleService;

    @Mock
    private LoginController loginController;

    @Mock
    private BaseCommonService baseCommonService;

    @Mock
    private RedisUtil redisUtil;

    private SysLoginModel loginModel;
    private SysUser testUser;
    private SysLdap testLdapConfig;
    private List<SysRole> testRoles;

    @BeforeEach
    void setUp() {
        // 设置loginCheckPassword字段
        ReflectionTestUtils.setField(authenticationService, "loginCheckPassword", true);

        // 初始化测试数据
        loginModel = new SysLoginModel();
        loginModel.setUsername("testuser");
        loginModel.setPassword("dGVzdHBhc3N3b3Jk"); // base64编码的"testpassword"

        testUser = new SysUser();
        testUser.setId("user123");
        testUser.setUsername("testuser");
        testUser.setThirdId("1");
        testUser.setStatus(1);

        testLdapConfig = new SysLdap();
        testLdapConfig.setAdAddress("ldap.test.com");
        testLdapConfig.setAdPort("389");
        testLdapConfig.setAddomainName("dc=test,dc=com");
        testLdapConfig.setLdapRole("admin,user");
        testLdapConfig.setLdapDepart("dept1");

        SysRole role1 = new SysRole();
        role1.setId("role1");
        role1.setRoleCode("admin");
        testRoles = Arrays.asList(role1);
    }

    @Test
    @DisplayName("LDAP服务未开启时应返回错误")
    void authenticate_WhenLdapNotEnabled_ShouldReturnError() {
        // Given
        when(loginController.getCheckCodeResult(any())).thenReturn(null);
        when(configurationService.isLdapEnabled()).thenReturn(false);

        // When
        Result<JSONObject> result = authenticationService.authenticate(loginModel);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("未开启LDAP服务,请联系管理员！", result.getMessage());
    }

    @Test
    @DisplayName("验证码错误时应返回验证码错误结果")
    void authenticate_WhenCheckCodeFailed_ShouldReturnCheckCodeError() {
        // Given
        Result<JSONObject> checkCodeError = Result.error("验证码错误");
        when(loginController.getCheckCodeResult(any())).thenReturn(checkCodeError);

        // When
        Result<JSONObject> result = authenticationService.authenticate(loginModel);

        // Then
        assertEquals(checkCodeError, result);
    }

    @Test
    @DisplayName("密码解密失败时应返回账号密码错误")
    void authenticate_WhenPasswordDecryptFailed_ShouldReturnPasswordError() {
        // Given
        when(loginController.getCheckCodeResult(any())).thenReturn(null);
        when(configurationService.isLdapEnabled()).thenReturn(true);
        
        Map<String, String> configParams = new HashMap<>();
        configParams.put("adVerification", "0");
        configParams.put("adFrozen", "0");
        when(configurationService.getConfigParams(any())).thenReturn(configParams);

        // 设置无效的密码（无法解密）
        loginModel.setPassword("invalid_password");

        // When
        Result<JSONObject> result = authenticationService.authenticate(loginModel);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("账号名或密码错误", result.getMessage());
    }

    @Test
    @DisplayName("现有非LDAP用户在严格验证模式下应返回账号异常")
    void authenticate_WhenExistingNonLdapUserInStrictMode_ShouldReturnAccountAbnormal() {
        // Given
        setupBasicMocks();
        Map<String, String> configParams = new HashMap<>();
        configParams.put("adVerification", "1"); // 严格验证模式
        configParams.put("adFrozen", "0");
        when(configurationService.getConfigParams(any())).thenReturn(configParams);

        // 现有用户但不是LDAP用户
        testUser.setThirdId("0");
        when(sysUserService.getOne(any(LambdaQueryWrapper.class))).thenReturn(testUser);

        // When
        Result<JSONObject> result = authenticationService.authenticate(loginModel);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("账号名异常,请联系管理员！", result.getMessage());
        verify(baseCommonService).addLog(contains("账号名异常"), anyInt(), isNull(), any());
    }

    @Test
    @DisplayName("用户登录错误次数超限应被冻结")
    void authenticate_WhenLoginErrorCountExceeded_ShouldFreezeUser() {
        // Given
        setupBasicMocks();
        when(sysUserService.getOne(any(LambdaQueryWrapper.class))).thenReturn(testUser);
        when(sysUserService.getLoginUserErrorCount(anyString())).thenReturn(15); // 超过10次

        Result<JSONObject> userValidResult = Result.OK();
        when(sysUserService.checkUserIsEffective(any())).thenReturn(userValidResult);

        Result<?> ldapAuthResult = Result.OK();
        when(connectionService.testConnection(any())).thenReturn(ldapAuthResult);

        // When
        Result<JSONObject> result = authenticationService.authenticate(loginModel);

        // Then
        verify(sysUserService).updateById(argThat(user -> user.getStatus() == 2));
        verify(sysUserService).updateLoginUserErrorCount(eq("testuser"), eq(-1));
    }

    @Test
    @DisplayName("LDAP认证失败时应返回账号密码错误")
    void authenticate_WhenLdapAuthFailed_ShouldReturnPasswordError() {
        // Given
        setupBasicMocks();
        when(sysUserService.getOne(any(LambdaQueryWrapper.class))).thenReturn(testUser);
        when(sysUserService.getLoginUserErrorCount(anyString())).thenReturn(0);

        Result<JSONObject> userValidResult = Result.OK();
        when(sysUserService.checkUserIsEffective(any())).thenReturn(userValidResult);

        Result<?> ldapAuthResult = Result.error("LDAP认证失败");
        when(connectionService.testConnection(any())).thenReturn(ldapAuthResult);

        // When
        Result<JSONObject> result = authenticationService.authenticate(loginModel);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("账号名或密码错误", result.getMessage());
        verify(baseCommonService).addLog(contains("账号名或密码错误"), anyInt(), isNull(), any());
    }

    @Test
    @DisplayName("新用户在严格验证模式下应返回账号未同步错误")
    void authenticate_WhenNewUserInStrictMode_ShouldReturnNotSyncedError() {
        // Given
        setupBasicMocks();
        Map<String, String> configParams = new HashMap<>();
        configParams.put("adVerification", "1"); // 严格验证模式
        configParams.put("adFrozen", "0");
        when(configurationService.getConfigParams(any())).thenReturn(configParams);

        when(sysUserService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null); // 新用户

        Result<?> ldapAuthResult = Result.OK();
        when(connectionService.testConnection(any())).thenReturn(ldapAuthResult);

        // When
        Result<JSONObject> result = authenticationService.authenticate(loginModel);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("账号暂未同步", result.getMessage());
    }

    @Test
    @DisplayName("新用户已被逻辑删除时应返回用户已删除错误")
    void authenticate_WhenNewUserLogicallyDeleted_ShouldReturnUserDeletedError() {
        // Given
        setupBasicMocks();
        when(sysUserService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        Result<?> ldapAuthResult = Result.OK();
        when(connectionService.testConnection(any())).thenReturn(ldapAuthResult);

        // 模拟逻辑删除的用户
        when(sysUserService.queryLogicDeleted(any(LambdaQueryWrapper.class)))
                .thenReturn(Arrays.asList(testUser));

        // When
        Result<JSONObject> result = authenticationService.authenticate(loginModel);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("用户登录失败，用户已经删除", result.getMessage());
        verify(baseCommonService).addLog(contains("用户已经删除"), anyInt(), isNull(), any());
    }

    @Test
    @DisplayName("新用户无默认角色时应返回角色设置错误")
    void authenticate_WhenNewUserNoDefaultRole_ShouldReturnRoleError() {
        // Given
        setupBasicMocks();
        when(sysUserService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        Result<?> ldapAuthResult = Result.OK();
        when(connectionService.testConnection(any())).thenReturn(ldapAuthResult);

        when(sysUserService.queryLogicDeleted(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        when(configurationService.getLdapConfiguration()).thenReturn(testLdapConfig);
        when(sysRoleService.list(any(LambdaQueryWrapper.class))).thenReturn(null); // 无默认角色

        // When
        Result<JSONObject> result = authenticationService.authenticate(loginModel);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("用户登录失败，请联系管理员设置默认角色", result.getMessage());
        verify(baseCommonService).addLog(contains("未设置默认角色"), anyInt(), isNull(), any());
    }

    @Test
    @DisplayName("新用户创建失败时应返回默认群组不存在错误")
    void authenticate_WhenNewUserCreationFailed_ShouldReturnDepartmentError() {
        // Given
        setupBasicMocks();
        when(sysUserService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        Result<?> ldapAuthResult = Result.OK();
        when(connectionService.testConnection(any())).thenReturn(ldapAuthResult);

        when(sysUserService.queryLogicDeleted(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        when(configurationService.getLdapConfiguration()).thenReturn(testLdapConfig);
        when(sysRoleService.list(any(LambdaQueryWrapper.class))).thenReturn(testRoles);

        // 用户创建失败
        when(userSyncService.saveUser(any(), any(), any(), any(), anyInt(), any())).thenReturn(null);

        // When
        Result<JSONObject> result = authenticationService.authenticate(loginModel);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("用户登录失败，设置的默认群组不存在", result.getMessage());
        verify(baseCommonService).addLog(contains("设置的默认群组不存在"), anyInt(), isNull(), any());
    }

    @Test
    @DisplayName("新用户状态为冻结时应返回用户冻结错误")
    void authenticate_WhenNewUserFrozen_ShouldReturnFrozenError() {
        // Given
        setupBasicMocks();
        Map<String, String> configParams = new HashMap<>();
        configParams.put("adVerification", "2");
        configParams.put("adFrozen", "2"); // 冻结状态
        when(configurationService.getConfigParams(any())).thenReturn(configParams);

        when(sysUserService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        Result<?> ldapAuthResult = Result.OK();
        when(connectionService.testConnection(any())).thenReturn(ldapAuthResult);

        when(sysUserService.queryLogicDeleted(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        when(configurationService.getLdapConfiguration()).thenReturn(testLdapConfig);
        when(sysRoleService.list(any(LambdaQueryWrapper.class))).thenReturn(testRoles);

        SysUser frozenUser = new SysUser();
        frozenUser.setStatus(2); // 冻结状态
        when(userSyncService.saveUser(any(), any(), any(), any(), anyInt(), any())).thenReturn(frozenUser);

        // When
        Result<JSONObject> result = authenticationService.authenticate(loginModel);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("该用户已冻结，请联系管理员", result.getMessage());
    }

    @Test
    @DisplayName("认证成功时应正确处理登录流程")
    void authenticate_WhenSuccessful_ShouldHandleLoginCorrectly() {
        // Given
        setupBasicMocks();
        when(sysUserService.getOne(any(LambdaQueryWrapper.class))).thenReturn(testUser);
        when(sysUserService.getLoginUserErrorCount(anyString())).thenReturn(0);

        Result<JSONObject> userValidResult = Result.OK();
        when(sysUserService.checkUserIsEffective(any())).thenReturn(userValidResult);

        Result<?> ldapAuthResult = Result.OK();
        when(connectionService.testConnection(any())).thenReturn(ldapAuthResult);

        // 模拟首次LDAP登录（需要更新密码）
        testUser.setPassword(null);
        testUser.setSalt("somesalt");

        Result<JSONObject> loginResult = Result.OK();
        doNothing().when(loginController).userInfo(any(), any(), anyBoolean(), anyBoolean());

        // When
        Result<JSONObject> result = authenticationService.authenticate(loginModel);

        // Then
        verify(redisUtil).set(eq("testuser"), eq(0)); // 清零错误次数
        verify(sysUserService).updateById(any()); // 更新密码
        verify(loginController).userInfo(eq(testUser), any(), eq(false), eq(true));
        verify(baseCommonService).addLog(contains("域群组登录成功"), anyInt(), isNull(), any());
    }

    @Test
    @DisplayName("解密密码时应正确处理各种情况")
    void decryptPassword_ShouldHandleVariousCases() throws Exception {
        // Given
        Method decryptPasswordMethod = LdapAuthenticationService.class
                .getDeclaredMethod("decryptPassword", String.class);
        decryptPasswordMethod.setAccessible(true);

        // When & Then - 测试空密码
        String result1 = (String) decryptPasswordMethod.invoke(authenticationService, "");
        assertNull(result1);

        // 测试无效密码格式
        String result2 = (String) decryptPasswordMethod.invoke(authenticationService, "invalid");
        assertNull(result2);
    }

    @Test
    @DisplayName("验证现有用户时应正确处理各种状态")
    void validateExistingUser_ShouldHandleVariousStates() throws Exception {
        // Given
        Method validateExistingUserMethod = LdapAuthenticationService.class
                .getDeclaredMethod("validateExistingUser", SysUser.class, String.class, String.class, LoginUser.class);
        validateExistingUserMethod.setAccessible(true);

        LoginUser loginUser = new LoginUser();
        loginUser.setUsername("testuser");

        // 测试非LDAP用户在严格模式
        testUser.setThirdId("0");
        when(sysUserService.getLoginUserErrorCount(anyString())).thenReturn(0);
        when(sysUserService.checkUserIsEffective(any())).thenReturn(Result.OK());

        // When
        Result<JSONObject> result = (Result<JSONObject>) validateExistingUserMethod
                .invoke(authenticationService, testUser, "testuser", "1", loginUser);

        // Then
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("账号名异常,请联系管理员！", result.getMessage());
    }

    @Test
    @DisplayName("执行LDAP认证时应正确构建连接属性")
    void performLdapAuthentication_ShouldBuildConnectionPropertiesCorrectly() throws Exception {
        // Given
        Method performLdapAuthMethod = LdapAuthenticationService.class
                .getDeclaredMethod("performLdapAuthentication", String.class, String.class);
        performLdapAuthMethod.setAccessible(true);

        when(configurationService.getLdapConfiguration()).thenReturn(testLdapConfig);
        when(connectionService.testConnection(any())).thenReturn(Result.OK());

        // When
        Result<?> result = (Result<?>) performLdapAuthMethod.invoke(authenticationService, "testuser", "password");

        // Then
        assertTrue(result.isSuccess());
        verify(connectionService).testConnection(any(LdapConnectTestModel.class));
    }

    @Test
    @DisplayName("更新用户密码时应正确处理首次LDAP登录")
    void updateUserPasswordIfNeeded_ShouldHandleFirstLdapLogin() throws Exception {
        // Given
        Method updatePasswordMethod = LdapAuthenticationService.class
                .getDeclaredMethod("updateUserPasswordIfNeeded", SysUser.class, String.class);
        updatePasswordMethod.setAccessible(true);

        // 模拟首次LDAP登录（密码为null或有salt）
        testUser.setPassword(null);
        testUser.setSalt("somesalt");

        // When
        updatePasswordMethod.invoke(authenticationService, testUser, "newpassword");

        // Then
        verify(sysUserService).updateById(testUser);
        assertEquals("", testUser.getSalt()); // salt应被清空
        assertNotNull(testUser.getPassword()); // 密码应被设置
    }

    @Test
    @DisplayName("处理成功登录时应正确设置用户信息")
    void handleSuccessfulLogin_ShouldSetUserInfoCorrectly() throws Exception {
        // Given
        Method handleSuccessfulLoginMethod = LdapAuthenticationService.class
                .getDeclaredMethod("handleSuccessfulLogin", SysUser.class, String.class, Result.class);
        handleSuccessfulLoginMethod.setAccessible(true);

        Result<JSONObject> result = new Result<>();

        // When
        handleSuccessfulLoginMethod.invoke(authenticationService, testUser, "testuser", result);

        // Then
        verify(redisUtil).set("testuser", 0); // 清零错误次数
        verify(loginController).userInfo(eq(testUser), eq(result), eq(false), eq(true));
        verify(baseCommonService).addLog(contains("域群组登录成功"), anyInt(), isNull(), any());
    }

    @Test
    @DisplayName("记录登录失败日志时应使用正确的模板")
    void logLoginFailure_ShouldUseCorrectTemplate() throws Exception {
        // Given
        Method logLoginFailureMethod = LdapAuthenticationService.class
                .getDeclaredMethod("logLoginFailure", String.class, String.class, LoginUser.class);
        logLoginFailureMethod.setAccessible(true);

        LoginUser loginUser = new LoginUser();
        loginUser.setUsername("testuser");

        // When
        logLoginFailureMethod.invoke(authenticationService, "testuser", "域群组用户登录失败，%s账号名异常！", loginUser);

        // Then
        verify(baseCommonService).addLog(eq("域群组用户登录失败，testuser账号名异常！"), anyInt(), isNull(), eq(loginUser));
    }

    /**
     * 设置基础Mock对象
     */
    private void setupBasicMocks() {
        when(loginController.getCheckCodeResult(any())).thenReturn(null);
        when(configurationService.isLdapEnabled()).thenReturn(true);

        Map<String, String> configParams = new HashMap<>();
        configParams.put("adVerification", "0");
        configParams.put("adFrozen", "0");
        when(configurationService.getConfigParams(any())).thenReturn(configParams);
    }
}
