#!/bin/bash

echo "========================================"
echo "LDAP服务单元测试执行脚本"
echo "========================================"
echo

echo "正在运行LDAP服务完整测试套件..."
echo

# 运行完整测试套件
mvn test -Dtest=org.jeecg.modules.system.service.impl.ldap.LdapServiceTestSuite

if [ $? -eq 0 ]; then
    echo
    echo "========================================"
    echo "测试套件执行成功！"
    echo "========================================"
    echo
    echo "如需运行单个测试类，请使用以下命令："
    echo
    echo "认证服务测试："
    echo "mvn test -Dtest=LdapAuthenticationServiceTest"
    echo
    echo "用户同步服务测试："
    echo "mvn test -Dtest=LdapUserSyncServiceTest"
    echo
    echo "部门同步服务测试："
    echo "mvn test -Dtest=LdapDepartmentSyncServiceTest"
    echo
    echo "连接服务测试："
    echo "mvn test -Dtest=LdapConnectionServiceTest"
    echo
    echo "配置服务测试："
    echo "mvn test -Dtest=LdapConfigurationServiceTest"
    echo
    echo "主服务测试："
    echo "mvn test -Dtest=LdapServiceImplTest"
    echo
else
    echo
    echo "========================================"
    echo "测试执行失败！错误代码：$?"
    echo "========================================"
    echo
    echo "请检查测试输出中的错误信息"
    echo "常见问题排查："
    echo "1. 确保所有依赖已正确安装"
    echo "2. 检查Mock配置是否正确"
    echo "3. 验证反射方法调用是否正确"
    echo "4. 确认测试数据设置是否完整"
    echo
fi
