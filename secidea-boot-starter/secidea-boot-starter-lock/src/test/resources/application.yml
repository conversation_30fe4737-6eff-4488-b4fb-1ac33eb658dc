spring:
  redis:
    database: 0
    host: 127.0.0.1
    jedis:
      pool:
        max-active: 8   #最大连接数据库连接数,设 0 为没有限制
        max-idle: 8     #最大等待连接中的数量,设 0 为没有限制
        max-wait: -1ms  #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
        min-idle: 0     #最小等待连接中的数量,设 0 为没有限制
      shutdown-timeout: 100ms
    password: jeecg
    port: 6379
jeecg :
  redisson:
    address: 127.0.0.1:6379
    password: jeecg
    type: STANDALONE
    enabled: true
