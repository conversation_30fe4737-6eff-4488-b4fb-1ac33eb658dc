spring:
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
#  profiles:
#    active: dev
  application:
    name: ${APP-PREFIX-NAME:secidea}-${APP-NAME:appName}
  cloud:
    #配置Bus id(远程推送事件)
    bus:
      id: ${spring.application.name}:${server.port}
    nacos:
      config:
        file-extension: yaml
        prefix: jeecg
        namespace: ${NACOS-NAMESPACE}
        group: ${NACOS-GROUP}
      discovery:
        watch:
          enabled: false
        namespace: ${NACOS-NAMESPACE}
        group: ${NACOS-GROUP}
      username: ${NACOS-USERNAME}
      password: ${NACOS-PASSWORD}
      server-addr: ${NACOS-SERVER-ADDR}
NACOS-NAMESPACE: d643881d-a94f-49da-a359-b61e04fb5d74
NACOS-SERVER-ADDR: 127.0.0.1:8848
NACOS-USERNAME: nacos
NACOS-PASSWORD: ENC(UEyXm0xe55P3pVzFsUcSG1TNRDhvCIGVQ8s0SXt1Ypov8BviKlUixJUB6gGDO0s0)
NACOS-GROUP: DEFAULT_GROUP

# feign启用sentinel
feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      default:
        #不设置connectTimeout会导致readTimeout设置不生效
        connectTimeout: 5000
        readTimeout: 10000
  compression:
    request:
      enabled: true
    response:
      enabled: true
