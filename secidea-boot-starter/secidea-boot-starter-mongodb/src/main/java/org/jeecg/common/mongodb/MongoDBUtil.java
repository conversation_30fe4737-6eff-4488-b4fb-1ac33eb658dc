package org.jeecg.common.mongodb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mongodb.client.ListIndexesIterable;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.client.model.Indexes;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description mongodb使用接口
 * @Date 2021/09/27
 */
@Service
public class MongoDBUtil<T> {

    /**
     * 注入template，减少重复代码
     */
    @Autowired
    private MongoTemplate mongoTemplate;


    /**
     * 功能描述: 创建一个集合
     * 同一个集合中可以存入多个不同类型的对象，我们为了方便维护和提升性能，
     * 后续将限制一个集合中存入的对象类型，即一个集合只能存放一个类型的数据
     */
    public void createCollection(T info) {
        mongoTemplate.createCollection(getDocumentName(info));
    }

    /**
     * 功能描述: 创建索引
     * 索引是顺序排列，且唯一的索引
     */
    public String createIndex(T info, String filedName) {
        //配置索引选项
        IndexOptions options = new IndexOptions();
        // 设置为唯一
        options.unique(true);
        //创建按filedName升序排的索引
        return mongoTemplate.getCollection(getDocumentName(info)).createIndex(Indexes.ascending(filedName), options);
    }


    /**
     * 功能描述: 获取当前集合对应的所有索引的名称
     */
    public List<String> getAllIndexes(T info) {
        ListIndexesIterable<Document> list = mongoTemplate.getCollection( getDocumentName(info) ).listIndexes();
        //上面的list不能直接获取size，因此初始化arrayList就不设置初始化大小了
        List<String> indexes = new ArrayList<>();
        for (Document document : list) {
            document.forEach((key1, value) -> {
                //提取出索引的名称
                if ("name".equals(key1)) {
                    indexes.add(value.toString());
                }
            });
        }
        return indexes;
    }

    /**
     * 功能描述: 往对应的集合中插入一条数据
     */
    public void insert(T info) {
        mongoTemplate.insert(info, getDocumentName(info));
    }

    /**
     * 功能描述: 往对应的集合中批量插入数据，注意批量的数据中不要包含重复的id
     */
    public void insertMulti(List<T> infos) {
        mongoTemplate.insert(infos, getDocumentName(infos.get(0)));
    }

    /**
     * 功能描述: 使用索引信息精确更改某条数据
     */
    public void updateById(String id, T info) {
        Query query = new Query(Criteria.where("id").is(id));
        Update update = new Update();
        String str = JSON.toJSONString(info);
        JSONObject jQuery = JSON.parseObject(str);
        jQuery.forEach((key, value) -> {
            //因为id相当于传统数据库中的主键，这里使用时就不支持更新，所以需要剔除掉
            if (!"id".equals(key)) {
                update.set(key, value);
            }
        });
        mongoTemplate.updateMulti(query, update, info.getClass(), getDocumentName(info) );
    }

    /**
     * 功能描述: 根据id删除集合中的内容
     */
    public void deleteById(String id, Class<T> clazz) {
        // 设置查询条件，当id=#{id}
        Query query = new Query(Criteria.where("id").is(id));
        // mongodb在删除对象的时候会判断对象类型，如果你不传入对象类型，只传入了集合名称，它是找不到的
        // 上面我们为了方便管理和提升后续处理的性能，将一个集合限制了一个对象类型，所以需要自行管理一下对象类型
        // 在接口传入时需要同时传入对象类型
        mongoTemplate.remove(query, clazz, getDocumentName(clazz));
    }

    /**
     * 功能描述: 根据id查询信息
     */
    public T selectById(String id, Class<T> clazz ) {
        // 查询对象的时候，不仅需要传入id这个唯一键，还需要传入对象的类型，以及集合的名称
        return mongoTemplate.findById(id, clazz, getDocumentName(clazz));
    }

    /**
     * 功能描述: 查询列表信息
     * 将集合中符合对象类型的数据全部查询出来
     */
    public List<T> selectList(Class<T> clazz) {
        return selectList( clazz, null, null);
    }

    /**
     * 功能描述: 分页查询列表信息
     */
    public List<T> selectList( Class<T> clazz, Integer currentPage, Integer pageSize) {
        //设置分页参数
        Query query = new Query();
        //设置分页信息
        if (!ObjectUtils.isEmpty(currentPage) && ObjectUtils.isEmpty(pageSize)) {
            query.limit(pageSize);
            query.skip(pageSize * (currentPage - 1));
        }
        return mongoTemplate.find(query, clazz, getDocumentName(clazz) );
    }


    /**
     * 功能描述: 根据条件查询集合
     */
    public List<T> selectByCondition( Object conditions, Class<T> clazz, Integer pageNo, Integer pageSize) {
        Integer currentPage = (pageNo-1) * pageSize ;
        if (ObjectUtils.isEmpty(conditions)) {
            return selectList( clazz, currentPage, pageSize);
        } else {
            //设置分页参数
            Query query = new Query();
            query.limit(pageSize);
            query.skip(currentPage);
            // 往query中注入查询条件
            Map conditionsMap = JSON.parseObject(JSON.toJSONString(conditions),Map.class);
            conditionsMap.forEach((key, value) -> query.addCriteria(Criteria.where((String) key).is(value)));
            return mongoTemplate.find(query, clazz, getDocumentName(clazz));
        }
    }

    private String getDocumentName(T info){
        return info.getClass().getAnnotation(org.springframework.data.mongodb.core.mapping.Document.class).value();
    }

    private String getDocumentName(Class<T> clazz){
        return clazz.getAnnotation(org.springframework.data.mongodb.core.mapping.Document.class).value();
    }
}
