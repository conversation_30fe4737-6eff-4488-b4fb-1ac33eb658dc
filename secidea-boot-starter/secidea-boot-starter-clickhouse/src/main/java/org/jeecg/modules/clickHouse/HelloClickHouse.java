package org.jeecg.modules.clickHouse;

import com.clickhouse.jdbc.*;
import java.sql.*;
import java.util.*;

/**
 * <AUTHOR>
 */
public class HelloClickHouse {
    public static void main(String[] args) throws Exception {

        // 配置
        String host = "127.0.0.1:8123";
        String userName = "user";
        String password = "pwd";

        String executeSql = "select * from system.tables limit 10";

        Properties properties = new Properties();
        // properties.setProperty("ssl", "true");
        // properties.setProperty("sslmode", "NONE"); // NONE to trust all servers; STRICT for trusted only

        ClickHouseDataSource dataSource = new ClickHouseDataSource("jdbc:ch://" + host, properties);
        try (Connection connection = dataSource.getConnection(userName,password );
        Statement statement = connection.createStatement();
        ResultSet resultSet = statement.executeQuery(executeSql)) {
            ResultSetMetaData resultSetMetaData = resultSet.getMetaData();
            int columns = resultSetMetaData.getColumnCount();
            while (resultSet.next()) {
                for (int c = 1; c <= columns; c++) {
                    System.out.print(resultSetMetaData.getColumnName(c) + ":" + resultSet.getString(c) + (c < columns ? ", " : "\n"));
                }
            }
        }
    }
}
