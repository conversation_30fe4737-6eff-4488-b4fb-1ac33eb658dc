package org.jeecg.modules.clickHouse.method;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;
import com.baomidou.mybatisplus.core.metadata.TableInfo;

import java.util.List;

/**
 * ClickHouse注册
 * <AUTHOR>
 */
public class ClickHouseSqlInjector extends DefaultSqlInjector {

    @Override
    public List<AbstractMethod> getMethodList(Class<?> mapperClass, TableInfo tableInfo) {
        List<AbstractMethod> methodList = super.getMethodList(mapperClass, tableInfo);
        /***
         * 添加自定义方法类
         */
//        methodList.add(new UpdateByIdForClickHouse());
        // 其他的还没做...
//        methodList.add(new UpdateForClickHouse());
//        methodList.add(new DeleteForClickHouse());
        return methodList;
    }
}
