package org.jeecg.modules.clickHouse.method;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ClickHouse SQL枚举类
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ClickHouseSqlEnum {
    /**
     * 删除
     */
    DELETE_BY_ID("deleteByIdForClickHouse", "根据ID 删除一条数据", "<script>\nALTER TABLE %s DELETE WHERE %s=#{%s}\n</script>"),

    /**
     * 逻辑删除
     */
    LOGIC_DELETE_BY_ID("deleteByIdForClickHouse", "根据ID 逻辑删除一条数据", "<script>\nALTER TABLE %s UPDATE %s where %s=#{%s} %s\n</script>"),

    /**
     * 修改 条件主键
     */
    UPDATE_BY_ID("updateByIdForClickHouse", "根据ID 选择修改数据", "<script>\nALTER TABLE %s UPDATE %s where %s=#{%s} %s\n</script>"),
    /**
     * 修改 条件主键
     */
    UPDATE("updateForClickHouse", "根据 whereEntity 条件，更新记录", "<script>\nALTER TABLE %s UPDATE  %s %s %s\n</script>");

    private final String method;
    private final String desc;
    private final String sql;

}
