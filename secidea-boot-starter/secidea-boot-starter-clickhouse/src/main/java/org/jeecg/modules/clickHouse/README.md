ClickHouse用法
目前仅支持 查询、新增
待支持（就算做出来性能很差，在实际使用中尽量规避） 修改、删除


1. 在Service接口添加动态数据源 
   @DS("ch")
   
2. 修改该数据源的Mapper继承
   原：       xxxMapper extends BaseMapper
   修改后:    Mapper extends ClickHouseBaseMapper
   
3. 在ServiceImpl中重写update,delete方法
   
   @Autowired
   private XxxMapper mapper;

   @Override
   public boolean updateById(Xxx entity) {
        return mapper.updateByIdForClickHouse(entity);
   }
