package org.jeecg.modules.clickHouse.config;


import org.jeecg.modules.clickHouse.method.ClickHouseSqlInjector;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * ClickHouseConfig
 * <AUTHOR>
 *
 */
@Configuration
@MapperScan(value={"org.jeecg.modules.**.mapper*"})
public class ClickHouseConfig {

    @Bean
    public ClickHouseSqlInjector clickHouseSqlInjector(){
        return new ClickHouseSqlInjector();
    }

}
