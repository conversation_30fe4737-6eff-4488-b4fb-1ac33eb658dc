package org.jeecg.modules.clickHouse.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import java.io.Serializable;
import com.baomidou.mybatisplus.core.conditions.Wrapper;

/**
 * 基于ClientHouse 的自定义顶级Mapper
 * <AUTHOR>
 */

@SuppressWarnings("all")
public interface ClickHouseBaseMapper<T> extends BaseMapper<T> {
    /**
     * 根据ID更新
     * @param entity 实体类
     * @return
     */
    boolean updateByIdForClickHouse(@Param("et") T entity);


    /**
     * 根据自定义条件更新
     * @param entity 实体类
     * @param updateWrapper 自定义条件
     * @return
     */
    boolean updateClickForHouse(@Param("et") T entity, @Param("ew") Wrapper<T> updateWrapper);

    /**
     * 根据ID删除
     * @param id ID
     * @return
     */
    int deleteByIdForClickHouse(Serializable id);
}
