package org.jeecg.modules.interceptor;
import org.jeecg.config.online.interceptor.SubModuleLicenseInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 子模块自定义路径 拦截器配置
 * 仅修改 urlList 即可
 * <AUTHOR>
 */
@Configuration
public class SubModuleLicenseConfiguration implements WebMvcConfigurer {

    public static String[] urlList = new String[] {
            "/scaProject/add",
            "/scaProject/addScaProjectVersion",
            "/scaProject/batchTestingGitSvn",
            "/scaProject/againScaProjectVersion",
            "/scaProject/myapis/put*",
            "/scaProject/myapis/upload*"
    };

    @Bean
    public SubModuleLicenseInterceptor subModuleLicenseInterceptor(){
        return new SubModuleLicenseInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(subModuleLicenseInterceptor()).addPathPatterns(urlList);
    }

}
