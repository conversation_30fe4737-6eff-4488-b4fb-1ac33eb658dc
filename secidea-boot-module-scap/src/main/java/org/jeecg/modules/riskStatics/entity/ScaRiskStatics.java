package org.jeecg.modules.riskStatics.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 源码缺陷统计
 * @Author: Yao
 * @Date: 2024-08-19
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
public class ScaRiskStatics implements Serializable {
    private static final long serialVersionUID = 1L;

    private String projectId;
    private String projectVersionIds;
    @Excel(name = "缺陷名称", width = 35)
    private String issueZhName;
    @Excel(name = "关联项目", width = 20)
    private String projectName;
    @Excel(name = "关联群组", width = 20)
    private String groupDepartId;
    @Excel(name = "缺陷路径", width = 80)
    private String issuePath;
    @Excel(name = "缺陷等级", width = 12)
    private String nowRiskLevel;
    @Excel(name = "审核状态", width = 12)
    private String issueClass;
    @Excel(name = "缺陷状态", width = 12)
    private String scapProcessType;
    @Excel(name = "缺陷类型", width = 12)
    private String issueType;
    @Excel(name = "发现时间", width = 21, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
