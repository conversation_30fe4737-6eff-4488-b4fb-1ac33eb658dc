package org.jeecg.modules.riskStatics.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 源码缺陷统计
 * @Author: Yao
 * @Date: 2024-08-19
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
public class ScaRiskStaticsParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(name = "开始时间")
    @NotNull(message = "开始时间不能为空")
    private Date startTime;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(name = "结束时间")
    @NotNull(message = "结束时间不能为空")
    private Date endTime;
    @Schema(name = "项目ID列表")
    private List<String> projectIdList;
    @Schema(name = "群组ID列表")
    private List<String> groupDepartIdList;
    @Schema(name = "缺陷类型")
    @NotNull(message = "缺陷类型不能为空")
    @Min(value = 0, message = "不允许的缺陷类型")
    @Max(value = 2, message = "不允许的缺陷类型")
    private Integer issueType;
    @Schema(name = "审核状态")
    @NotEmpty(message = "审核状态不能为空")
    private List<String> issueClassList;
    @Schema(name = "缺陷等级")
    @NotEmpty(message = "缺陷等级不能为空")
    private List<String> nowRiskLevelList;
    @Schema(name = "检测缺陷模板ID")
    private String standardId;

}
