package org.jeecg.modules.riskStatics.entity;

import lombok.Data;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 源码缺陷统计
 * @Author: Yao
 * @Date: 2024-08-19
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
public class ScaRiskStaticsDoc implements Serializable {
    private static final long serialVersionUID = 1L;
    @ExcelCollection(name = "")
    private List<DocList> docList;
    @Excel(name = "备注", width = 25, needMerge = true)
    private String description;

    @Data
    @Accessors(chain = true)
    public static class DocList implements Serializable {
        private static final long serialVersionUID = 1L;
        @Excel(name = "缺陷状态", width = 15)
        private String scapProcessType;
        @Excel(name = "定义方法", width = 60)
        private String defMethod;
    }

}
