package org.jeecg.modules.riskStatics.controller;

import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.handler.ExportExcelHandler;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.base.entity.SysReportManager;
import org.jeecg.modules.riskStatics.entity.ScaRiskStaticsParam;
import org.jeecg.modules.riskStatics.service.IScaRiskStaticsService;
import org.jeecg.modules.sectest.scap.entity.ScaProject;
import org.jeecg.modules.sectest.scap.service.IScaProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: 源码缺陷统计
 * @Author: Yao
 * @Date: 2024-08-19
 * @Version: V1.0
 */
@RestController
@RequestMapping("/scaRiskStatics")
@Slf4j
public class ScaRiskStaticsController extends JeecgController<ScaProject, IScaProjectService> {

    @Autowired
    private ExportExcelHandler<ScaProject, IScaProjectService> scaProjectExportExcelHandler;
    @Autowired
    private IScaRiskStaticsService scaRiskStaticsService;

    @PostMapping("exportXls")
    @RequiresPermissions(value = "scap:project:export:risk:statics")
    public Result<?> exportXls(@RequestBody @Validated ScaRiskStaticsParam scaRiskStaticsParam) {
        LoginUser loginUser = getLoginUser();
        String title = "缺陷统计清单";
        SysReportManager sysReportManager = scaProjectExportExcelHandler.initSysReportManager(title, loginUser);
        scaRiskStaticsService.exportScaRiskStaticsAsync(sysReportManager, scaRiskStaticsParam, getLoginUserFilter());
        return Result.OK("生成报告中，请稍后查询");
    }

}
