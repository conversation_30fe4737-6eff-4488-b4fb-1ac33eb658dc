<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.riskStatics.mapper.ScaRiskStaticsMapper">

    <select id="getScaRiskStaticsList" parameterType="String" resultType="org.jeecg.modules.riskStatics.entity.ScaRiskStatics">
        SELECT
        ssr.issue_zh_name,
        st.projectId,
        st.projectVersionIds,
        st.projectName AS project_name,
        st.group_depart_id,
        ssr.issue_path,
        ssr.now_risk_level,
        ssr.issue_class,
        ssr.scap_process_type,
        ssr.issue_type,
        ssr.create_time
        FROM
        (
        SELECT
        str.scap_task_id,
        str.scap_result_id,
        spv.projectVersionId,
        sp.projectId,
        sp.projectName,
        sp.group_depart_id,
        group_concat(spv.projectVersionId) as projectVersionIds
        FROM
        sca_project_version spv
        LEFT JOIN sca_project sp ON sp.projectId = spv.projectId
        LEFT JOIN sca_task_result str ON spv.projectVersionId = str.scap_task_id
        WHERE
        <include refid="whereSpv"/>
        GROUP BY
        scap_result_id
        ) AS st
        LEFT JOIN sca_scan_result ssr ON st.scap_result_id = ssr.id
        <if test="param.standardId != null and param.standardId !=''">
            LEFT JOIN sdl_sca_scan_item sssi ON ssr.knowledge_id = sssi.issue_type_id
            LEFT JOIN sdl_standard_req_items ssri ON ssri.id = sssi.standard_item_id
        </if>
        <where>
        <if test="param.standardId != null and param.standardId !=''">
            AND ssri.standard_id =  #{param.standardId}
        </if>
        <if test="param.nowRiskLevelList != null and param.nowRiskLevelList.size()>0">
            AND ssr.now_risk_level IN
            <foreach collection="param.nowRiskLevelList" index="index" item="nowRiskLevel" open="(" separator="," close=")">
                #{nowRiskLevel}
            </foreach>
        </if>
        <if test="param.issueClassList != null and param.issueClassList.size()>0">
            AND ssr.issue_class IN
            <foreach collection="param.issueClassList" index="index" item="issueClass" open="(" separator="," close=")">
                #{issueClass}
            </foreach>
        </if>
        <choose>
            <when test="param.issueType != null">
                <if test="param.issueType == 1 || param.issueType == 2">
                    AND ssr.issue_type = #{param.issueType}
                </if>
            </when>
        </choose>
        </where>
    </select>

    <resultMap id="taskProjectMap" type="java.util.HashMap">
        <result property="projectId" column="projectId" javaType="java.lang.String"/>
        <result property="projectVersionId" column="projectVersionId" javaType="java.lang.String"/>
    </resultMap>

    <select id="getLastTaskListMapForProject" resultMap="taskProjectMap">
        SELECT
        projectId, projectVersionId
        FROM (
        SELECT projectVersionId, projectId
        FROM sca_project_version spv
        WHERE
        <include refid="whereSpv"/>
        ORDER BY projectVersionId DESC limit 100000000
        ) AS st
        GROUP BY projectId
    </select>

    <sql id="whereSpv">
        DATE_FORMAT(spv.create_time, '%Y-%m-%d %H:%i:%s') &gt;= #{param.startTime}
        AND DATE_FORMAT(spv.create_time, '%Y-%m-%d %H:%i:%s') &lt;= #{param.endTime}
        AND spv.STATUS = 4
        AND ( spv.sign = 1 OR spv.sign IS NULL )
        <if test="param.projectIdList != null and param.projectIdList.size()>0">
            AND spv.projectId IN
            <foreach collection="param.projectIdList" index="index" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        <if test="param.groupDepartIdList != null and param.groupDepartIdList.size()>0">
            AND spv.group_depart_id IN
            <foreach collection="param.groupDepartIdList" index="index" item="groupDepartId" open="(" separator="," close=")">
                #{groupDepartId}
            </foreach>
        </if>
        <choose>
            <when test="loginUserFilter.authGrade!=null">
                <if test="loginUserFilter.authGrade>1 and
                loginUserFilter.noInAdminUserList!=null and loginUserFilter.noInAdminUserList.size()>0">
                    AND spv.create_by NOT IN
                    <foreach collection="loginUserFilter.noInAdminUserList" index="index" item="user" open="(" separator="," close=")">
                        #{user}
                    </foreach>
                </if>
                <if test="loginUserFilter.authGrade==3 and
                loginUserFilter.username!=null and loginUserFilter.username!='' and
                loginUserFilter.sysOrgCode!=null and loginUserFilter.sysOrgCode!=''">
                    AND (spv.create_by = #{loginUserFilter.username}
                    OR (spv.sys_org_code LIKE CONCAT(#{loginUserFilter.sysOrgCode}, '%') AND spv.group_depart_id IS NOT NULL AND spv.group_depart_id != ''))
                </if>
            </when>
        </choose>
    </sql>

</mapper>
