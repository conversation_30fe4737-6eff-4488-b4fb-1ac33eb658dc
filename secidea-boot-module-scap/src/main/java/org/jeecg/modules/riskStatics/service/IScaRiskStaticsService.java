package org.jeecg.modules.riskStatics.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.system.vo.LoginUserFilter;
import org.jeecg.modules.base.entity.SysReportManager;
import org.jeecg.modules.riskStatics.entity.ScaRiskStatics;
import org.jeecg.modules.riskStatics.entity.ScaRiskStaticsParam;
import org.springframework.scheduling.annotation.Async;

/**
 * @Description: 源码缺陷统计
 * @Author: Yao
 * @Date: 2024-08-19
 * @Version: V1.0
 */
public interface IScaRiskStaticsService extends IService<ScaRiskStatics> {

    /**
     * 导出缺陷统计
     *
     * @param sysReportManager
     * @param scaRiskStaticsParam
     * @param loginUserFilter
     */
    @Async("oscapAsyncExecutor")
    void exportScaRiskStaticsAsync(SysReportManager sysReportManager, ScaRiskStaticsParam scaRiskStaticsParam, LoginUserFilter loginUserFilter);

}
