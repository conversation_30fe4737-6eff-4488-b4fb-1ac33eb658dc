package org.jeecg.modules.riskStatics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.common.system.vo.LoginUserFilter;
import org.jeecg.modules.riskStatics.entity.ScaRiskStatics;
import org.jeecg.modules.riskStatics.entity.ScaRiskStaticsParam;

import java.util.List;
import java.util.Map;

/**
 * @Description: 检测结果记录
 * @Author: jeecg-boot
 * @Date:   2021-08-26
 * @Version: V1.0
 */
public interface ScaRiskStaticsMapper extends BaseMapper<ScaRiskStatics> {

    /**
     * 获取缺陷统计清单
     * @param scaRiskStaticsParam
     * @param loginUserFilter
     * @return
     */
    List<ScaRiskStatics> getScaRiskStaticsList(@Param("param")ScaRiskStaticsParam scaRiskStaticsParam, LoginUserFilter loginUserFilter);

    /**
     * 获取需要统计缺陷的项目任务对应关系
     * @param scaRiskStaticsParam
     * @param loginUserFilter
     * @return
     */
    List<Map<String, String>> getLastTaskListMapForProject(@Param("param")ScaRiskStaticsParam scaRiskStaticsParam, LoginUserFilter loginUserFilter);

}
