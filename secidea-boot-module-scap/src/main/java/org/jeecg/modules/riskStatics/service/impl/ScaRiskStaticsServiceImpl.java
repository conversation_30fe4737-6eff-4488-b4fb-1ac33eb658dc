package org.jeecg.modules.riskStatics.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.util.DictUtils;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.system.vo.LoginUserFilter;
import org.jeecg.common.system.vo.SysDepartModel;
import org.jeecg.common.util.MD5Util;
import org.jeecg.common.util.StringUtils;
import org.jeecg.common.util.UUIDGenerator;
import org.jeecg.common.util.word.DefaultExcelStyler;
import org.jeecg.modules.base.entity.SysReportManager;
import org.jeecg.modules.riskStatics.entity.ScaRiskStatics;
import org.jeecg.modules.riskStatics.entity.ScaRiskStaticsDoc;
import org.jeecg.modules.riskStatics.entity.ScaRiskStaticsParam;
import org.jeecg.modules.riskStatics.mapper.ScaRiskStaticsMapper;
import org.jeecg.modules.riskStatics.service.IScaRiskStaticsService;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 源码缺陷统计
 * @Author: Yao
 * @Date: 2024-08-19
 * @Version: V1.0
 */
@Service
@Slf4j
public class ScaRiskStaticsServiceImpl extends ServiceImpl<ScaRiskStaticsMapper, ScaRiskStatics> implements IScaRiskStaticsService {

    @Value(value = "${jeecg.path.upload}")
    private String uploadPath;
    @Value("${APP-NAME:}")
    private String appName;
    @Autowired
    private ISysBaseAPI sysBaseAPI;

    @Override
    public void exportScaRiskStaticsAsync(SysReportManager sysReportManager, ScaRiskStaticsParam scaRiskStaticsParam, LoginUserFilter loginUserFilter) {
        boolean success = true;
        List<Map<String, Object>> listMap = new ArrayList<Map<String, Object>>();

        List<ScaRiskStatics> scaRiskStaticsAllList = baseMapper.getScaRiskStaticsList(scaRiskStaticsParam, loginUserFilter);
        // 查找符合条件的最新任务
        List<Map<String, String>> lastTaskMapForProject = baseMapper.getLastTaskListMapForProject(scaRiskStaticsParam, loginUserFilter);
        Map<String, String> lastTaskProjectMap = new HashMap<>();
        lastTaskMapForProject.forEach(item -> {
            lastTaskProjectMap.put(item.get("projectId"), item.get("projectVersionId"));
        });
        List<ScaRiskStatics> scaRiskStaticsList = new ArrayList<>();
        ExportParams riskStaticsExportParams = new ExportParams(null, "缺陷清单", ExcelType.XSSF);
        riskStaticsExportParams.setStyle(DefaultExcelStyler.class);
        Map<String, Object> riskStaticsMap = new HashMap<>();
        riskStaticsMap.put("title", riskStaticsExportParams);
        riskStaticsMap.put("entity", ScaRiskStatics.class);
        if (scaRiskStaticsAllList.isEmpty()) {
            riskStaticsMap.put("data", scaRiskStaticsAllList);
            listMap.add(riskStaticsMap);
        } else {
            // 获取满足条件的所有群组
            Set<String> groupDepartIdList = scaRiskStaticsAllList.stream().map(ScaRiskStatics::getGroupDepartId)
                    .filter(groupDepartId -> !StringUtils.isBlank(groupDepartId)).collect(Collectors.toSet());
            Map<String, String> allDepartMap = new HashMap<>();
            if (!groupDepartIdList.isEmpty()) {
                List<SysDepartModel> allDepartList = sysBaseAPI.queryDepartsByIds(groupDepartIdList);
                if (null != allDepartList) {
                    allDepartMap = allDepartList.stream().collect(Collectors.toMap(SysDepartModel::getId, SysDepartModel::getDepartName));
                }
            }
            // 字典翻译
            List<DictModel> riskLevelDictList = sysBaseAPI.queryDictItemsByCode("risk_level");
            List<DictModel> scapProcessTypeDictList = sysBaseAPI.queryDictItemsByCode("scap_process_type");
            List<DictModel> issueTypeDictList = sysBaseAPI.queryDictItemsByCode("issue_type");
            // 组装导出的缺陷清单
            for (int i = 0; i < scaRiskStaticsAllList.size(); i++) {
                if (i % 60000 == 0 && i > 0 && scaRiskStaticsAllList.size() - 1 > i) {
                    // 满足单个sheet的数量时提交上次结果
                    riskStaticsMap.put("title", riskStaticsExportParams);
                    riskStaticsMap.put("entity", ScaRiskStatics.class);
                    riskStaticsMap.put("data", scaRiskStaticsList);
                    listMap.add(riskStaticsMap);
                    // 重置sheet内容
                    riskStaticsExportParams = new ExportParams(null, "缺陷清单(" + ((i / 60000) + 1) + ")", ExcelType.XSSF);
                    riskStaticsExportParams.setStyle(DefaultExcelStyler.class);
                    riskStaticsMap = new HashMap<>();
                    riskStaticsMap.put("title", riskStaticsExportParams);
                    riskStaticsMap.put("entity", ScaRiskStatics.class);
                    scaRiskStaticsList = new ArrayList<>();
                }
                // 字典翻译
                ScaRiskStatics scaRiskStatics = scaRiskStaticsAllList.get(i);
                String scapProcessType = scaRiskStatics.getScapProcessType();
                List<String> taskIdList = new ArrayList<>(Arrays.asList(scaRiskStatics.getProjectVersionIds().split(",")));
                String lastTaskId = lastTaskProjectMap.get(scaRiskStatics.getProjectId());
                boolean containsLastTask = taskIdList.contains(lastTaskId);
                if ("1".equals(scapProcessType) || "2".equals(scapProcessType) || "3".equals(scapProcessType) || "5".equals(scapProcessType)) {
                    if (taskIdList.size() == 1 && containsLastTask) {
                        // 只有一条记录为新增
                        scapProcessType = "1";
                    } else if (taskIdList.size() > 1 && containsLastTask) {
                        // 多条结果，且在最后一个任务出现时未未修复
                        scapProcessType = "2";
                    } else {
                        // 没有出现为已修复
                        scapProcessType = "3";
                    }
                }
                scaRiskStatics.setNowRiskLevel(DictUtils.getDictTextByDictValue(riskLevelDictList, scaRiskStatics.getNowRiskLevel()))
                        .setIssueClass((StringUtils.isBlank(scaRiskStatics.getIssueClass()) || "1".equals(scaRiskStatics.getIssueClass())) ?
                                "未审核" : "已审核")
                        .setScapProcessType(DictUtils.getDictTextByDictValue(scapProcessTypeDictList, scapProcessType))
                        .setIssueType(DictUtils.getDictTextByDictValue(issueTypeDictList, scaRiskStatics.getIssueType()))
                        .setGroupDepartId(allDepartMap.get(scaRiskStatics.getGroupDepartId()));
                scaRiskStaticsList.add(scaRiskStatics);
            }
            riskStaticsMap.put("data", scaRiskStaticsList);
            listMap.add(riskStaticsMap);
        }

        // 缺陷定义方法
        List<ScaRiskStaticsDoc> scaRiskStaticsDocList = new ArrayList<>();
        ScaRiskStaticsDoc scaRiskStaticsDoc = new ScaRiskStaticsDoc().setDescription("仅考虑在统计时间范围内的检测任务及结果");
        List<ScaRiskStaticsDoc.DocList> docList = new ArrayList<>();
        docList.add(new ScaRiskStaticsDoc.DocList().setScapProcessType("新增").setDefMethod("缺陷在所选时间内的最新一次检测中首次检出"));
        docList.add(new ScaRiskStaticsDoc.DocList().setScapProcessType("未修复").setDefMethod("缺陷在所选时间内被检出多次，且在最新一次检测中检出"));
        docList.add(new ScaRiskStaticsDoc.DocList().setScapProcessType("已修复").setDefMethod("缺陷在所选时间内被检出过，但最新一次检测未检出"));
        docList.add(new ScaRiskStaticsDoc.DocList().setScapProcessType("误报").setDefMethod("缺陷被人工标记为“误报”"));
        docList.add(new ScaRiskStaticsDoc.DocList().setScapProcessType("永久忽略").setDefMethod("缺陷被人工标记为“永久忽略”"));
        scaRiskStaticsDoc.setDocList(docList);
        scaRiskStaticsDocList.add(scaRiskStaticsDoc);

        ExportParams docExportParams = new ExportParams(null, "缺陷状态定义方法", ExcelType.XSSF);
        docExportParams.setStyle(DefaultExcelStyler.class);
        Map<String, Object> docMap = new HashMap<>();
        docMap.put("title", docExportParams);
        docMap.put("entity", ScaRiskStaticsDoc.class);
        docMap.put("data", scaRiskStaticsDocList);
        listMap.add(docMap);

        // 生成报告
        String reportPath = "temp/report/" + appName + "/other/";
        String path = uploadPath + "/" + reportPath;
        FileUtil.mkdir(path);
        String fileName = UUIDGenerator.generate() + ".xlsx";
        reportPath += fileName;
        path += fileName;
        FileOutputStream out = null;
        try {
            out = new FileOutputStream(path);
            Workbook workbook = ExcelExportUtil.exportExcel(listMap, ExcelType.XSSF);
            workbook.getCellStyleAt(workbook.getNumCellStyles() - 1).setWrapText(true);
            workbook.write(out);
        } catch (IOException e) {
            e.printStackTrace();
            success = false;
        } finally {
            IoUtil.close(out);
        }
        if (success) {
            sysReportManager.setReportUrl(reportPath).setStatus(3).setMd5(MD5Util.getMd5FromFilePath(path));
        } else {
            sysReportManager.setStatus(4);
        }
        sysBaseAPI.updateSysReportManager(sysReportManager);
    }
}
