package org.jeecg.modules.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.util.StringUtils;
import org.jeecg.modules.api.IFileBaseAPI;
import org.jeecg.modules.api.IOscaBaseAPI;
import org.jeecg.modules.redisMQ.ScaProjectMqHandler;
import org.jeecg.modules.resource.entity.ScaFilterRules;
import org.jeecg.modules.resource.service.IScaFilterRulesService;
import org.jeecg.modules.sectest.scap.controller.ScaScanResultController;
import org.jeecg.modules.sectest.scap.service.IScaProjectVersionService;
import org.jeecg.modules.threadTask.entity.SysThreadTask;
import org.jeecg.modules.threadTask.service.ISysThreadTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 测试处理类，用完自删
 * <AUTHOR>
 */
@RestController
@RequestMapping("/test")
@Slf4j
public class TestController {

    @Autowired
    private IOscaBaseAPI oscaBaseAPI;
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    @Autowired
    private IFileBaseAPI fileBaseAPI;
    @Autowired
    private ScaProjectMqHandler scaProjectMqHandler;
    @Autowired
    private ScaScanResultController scaScanResultController;
    @Autowired
    private IScaProjectVersionService scaProjectVersionService;
    @Autowired
    private IScaFilterRulesService scaFilterRulesService;
    @Autowired
    private ISysThreadTaskService threadTaskService;

//    @GetMapping("addScaFilter")
    public Result<?> addScaFilter(@RequestParam(value = "ids",required=false)String ids){
        LambdaQueryWrapper<ScaFilterRules> queryWrapper = new LambdaQueryWrapper<>();
        if ( !StringUtils.isBlank(ids) ){
            queryWrapper.in(ScaFilterRules::getId,Arrays.asList(ids.split(",")));
        }
        List<ScaFilterRules> filterRulesList = scaFilterRulesService.list(queryWrapper);
        if ( !filterRulesList.isEmpty() ){
            List<SysThreadTask> sysThreadTaskParamList = new ArrayList<>();
            for (ScaFilterRules scaFilterRules : filterRulesList) {
                List<Map<String, String>> maxIdByProjectList = null;
                List<String> maxProjectParamList = null;
                if ( !StringUtils.isBlank(scaFilterRules.getProjectid()) ){
                    maxProjectParamList = new ArrayList<>(Arrays.asList(scaFilterRules.getProjectid().split(",")));
                }
                maxIdByProjectList = scaProjectVersionService.getMaxIdByProject(maxProjectParamList);
                if ( !maxIdByProjectList.isEmpty() ){
                    maxIdByProjectList.forEach(o->{
                        // 构建一键分析参数
                        cn.hutool.json.JSONObject param = new cn.hutool.json.JSONObject();
                        param.put("analyzerType","1");
                        param.put("projectid",o.get("projectId"));
                        param.put("taskId",o.get("projectVersionId"));
                        param.put("scaFilterRulesId",scaFilterRules.getId());
                        // 将任务添加到线程任务表
                        sysThreadTaskParamList.add(new SysThreadTask().setName("autoAnalysis").setPrimaryId(String.valueOf(o.get("projectVersionId"))).setParam(param.toString()));
                    });
                }
            }
            threadTaskService.saveBatch(sysThreadTaskParamList);
        }
        return Result.OK();
    }


//    @GetMapping("asyncScaFilter")
    public Result<?> asyncScaFilter(@RequestParam(value = "ids",required=false)String ids){

        long time = System.currentTimeMillis();
        LambdaQueryWrapper<ScaFilterRules> queryWrapper = new LambdaQueryWrapper<>();
        if ( !StringUtils.isBlank(ids) ){
            queryWrapper.in(ScaFilterRules::getId,Arrays.asList(ids.split(",")));
        }
        List<ScaFilterRules> filterRulesList = scaFilterRulesService.list(queryWrapper);
        if ( !filterRulesList.isEmpty() ){
            for (ScaFilterRules scaFilterRules : filterRulesList) {
                List<Map<String, String>> maxIdByProjectList = null;
                List<String> maxProjectParamList = null;
                if ( !StringUtils.isBlank(scaFilterRules.getProjectid()) ){
                    maxProjectParamList = new ArrayList<>(Arrays.asList(scaFilterRules.getProjectid().split(",")));
                }
                maxIdByProjectList = scaProjectVersionService.getMaxIdByProject(maxProjectParamList);
                if ( !maxIdByProjectList.isEmpty() ){
                    maxIdByProjectList.forEach(o->{
                        // 构建一键分析参数
                        cn.hutool.json.JSONObject param = new cn.hutool.json.JSONObject();
                        param.put("analyzerType","1");
                        param.put("projectid",o.get("projectId"));
                        param.put("taskId",o.get("projectVersionId"));
                        param.put("scaFilterRulesId",scaFilterRules.getId());
                        log.info(String.format("请求参数 %s",param));
                        long startTime = System.currentTimeMillis();
                        Result<?> analysisResult = scaScanResultController.analysis(param);
                        log.info(String.format("请求完成，耗时 %s 秒 ,返回结果 %s ",(System.currentTimeMillis()-startTime),analysisResult));
                    });

                }
            }
        }
        return Result.OK(String.format("请求完成，耗时 %s 秒",System.currentTimeMillis()-time));
    }

}
