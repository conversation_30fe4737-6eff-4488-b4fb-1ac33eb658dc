package org.jeecg.modules.thirdParty.webank;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.esotericsoftware.minlog.Log;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.StringUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.sectest.scap.entity.ScaProjectVersion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 微众 发送消息
 * <AUTHOR>
 */
@Component
public class WebankSendMsgUtil {

    /**
     * 微众 - 服务器接口地址
     */
    @Value("${webank.send.msg.url:}")
    private String webankUrl;
    /**
     * 微众 - 邮件服务状态（是否开启）
     * 1。开启
     * 2.关闭
     */
    @Value("${webank.send.msg.status:}")
    private String webankMsgStatus;
    /**
     * 微众 - 邮件接口keyId
     */
    @Value("${webank.send.msg.keyId:}")
    private String webankMsgKeyId;
    /**
     * 微众 - 邮件接口serviceName
     */
    @Value("${webank.send.msg.serviceName:}")
    private String webankServiceName;


    @Autowired
    private ISysBaseAPI sysBaseAPI;

    /**
     * 判断微众邮件服务是否开启
     * @return
     */
    public boolean isOpen(){
        return !(StringUtils.isBlank(webankUrl) || StringUtils.isBlank(webankMsgKeyId) || StringUtils.isBlank(webankServiceName) ||
                StringUtils.isBlank(webankMsgStatus) || CommonConstant.STATUS_2.equals(webankMsgStatus));
    }

    /**
     * 微众 - 检测任务完成后（成功时）发送邮件通知
     */
    public void sendMsgForTaskSuccess(ScaProjectVersion scaProjectVersion){
        // 不是检测成功，不发送通知
        if ( null == scaProjectVersion.getStatus() || 4 != scaProjectVersion.getStatus() ){
            return;
        }
        List<String> sendUserList = new ArrayList<>();
        sendUserList.add(scaProjectVersion.getCreateBy());
        if ( !StringUtils.isBlank(scaProjectVersion.getPrincipal()) ) {
            List<String> principalUserList = new ArrayList<>(Arrays.asList(scaProjectVersion.getPrincipal().split(",")));
            principalUserList.forEach(o-> {
                if (!StringUtils.isBlank(o) && !sendUserList.contains(o)) {
                    LoginUser principalUser = sysBaseAPI.getUserByName(o);
                    if (!oConvertUtils.isEmpty(principalUser)) {
                        sendUserList.add(o);
                    } else {
                        // 适配WeBank 根据 jasonzhan(詹剑)查询是否存在该用户，查不到则根据括号分隔获取jasonzhan
                        if ( o.contains("(") && !sendUserList.contains(o.split("\\(")[0].trim()) ){
                            principalUser = sysBaseAPI.getUserByName(o.split("\\(")[0].trim());
                            if ( !oConvertUtils.isEmpty(principalUser) ){
                                sendUserList.add(o.split("\\(")[0].trim());
                            }
                        } else if ( o.contains("（") && !sendUserList.contains(o.split("（")[0].trim()) ){
                            principalUser = sysBaseAPI.getUserByName(o.split("（")[0].trim());
                            if ( !oConvertUtils.isEmpty(principalUser) ){
                                sendUserList.add(o.split("（")[0].trim());
                            }
                        }
                    }
                }
            });
        }
        sendUserList.forEach(user->{
            String content = String.format(
                    "【漏洞修复提醒】%s，源代码安全检测平台（SAST）扫出如下子系统漏洞需修复，请及时跟进处理。\n" +
                            "子系统名：%s   扫描任务名：%s \n" +
                            "总漏洞数: %s 高风险漏洞数: %s 中风险漏洞数: %s " +
                            "修复期限: %s \n" +
                            "任务链接：%s",
                    user,
                    scaProjectVersion.getProjectname(),scaProjectVersion.getProjectversionname(),
                    (scaProjectVersion.getHighcount()+scaProjectVersion.getMediumcount()),scaProjectVersion.getHighcount(),scaProjectVersion.getMediumcount(),
                    scaProjectVersion.getRectifyTime(),
                    getDetailsUrlByTaskId(scaProjectVersion.getProjectversionid()));
            sendEmail(user,content);
        });

    }

    /**
     * 发送邮件
     * @param userName
     * @param content
     */
    private void sendEmail(String userName, String content){
        JSONObject requestParam = new JSONObject();
        requestParam.put("keyid", webankMsgKeyId);
        requestParam.put("content", content);
        requestParam.put("type", "text");
        requestParam.put("serviceName", webankServiceName);
        requestParam.put("userName", userName);
        String postResult = HttpRequest.post(webankUrl)
                .body(requestParam.toJSONString())
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .execute().body();
        Log.info("WeBank发送邮件回调: " + postResult);
    }

    private String getDetailsUrlByTaskId(Object taskId){
        String serverInterfaceUrl = sysBaseAPI.getSysConfigParamByKey("serverInterfaceUrl");
        return serverInterfaceUrl + "/scap/ScaScanResultList?taskId=" + taskId;
    }

}
