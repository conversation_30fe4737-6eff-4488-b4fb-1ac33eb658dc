package org.jeecg.modules.scacluster.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.scacluster.entity.ScaClusterHost;
import org.jeecg.modules.scacluster.mapper.ScaClusterHostMapper;
import org.jeecg.modules.scacluster.service.IScaClusterHostService;
import org.springframework.stereotype.Service;

/**
 * @Description: SCA主从设置
 * @Author: Yao
 * @Date:   2023-06-09
 * @Version: V1.0
 */
@Service
public class ScaClusterHostServiceImpl extends ServiceImpl<ScaClusterHostMapper, ScaClusterHost> implements IScaClusterHostService {

}
