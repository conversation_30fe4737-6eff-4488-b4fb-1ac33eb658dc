package org.jeecg.modules.scacluster.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: SCA主从设置
 * @Author: Yao
 * @Date: 2023-06-09
 * @Version: V1.0
 */
@Data
@TableName("sca_cluster_host")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "sys_cluster_host对象", description = "SCA主从设置")
public class ScaClusterHost {
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(name = "主键")
    private Integer id;
    @Schema(name = "设备IP")
    private String ip;
    @Schema(name = "主从")
    private Integer isMaster;
    @Schema(name = "并发数")
    private Integer maxCount;
    @Schema(name = "CPU使用率")
    private String cpuRate;
    @Schema(name = "内存使用率")
    private String memoryRate;
    @Schema(name = "硬盘使用率")
    private String diskRate;
    @Schema(name = "状态")
    private Integer hostStatus;
    @TableField(exist = false)
    private String rowKey;
    @TableField(exist = false)
    private String appName;
}
