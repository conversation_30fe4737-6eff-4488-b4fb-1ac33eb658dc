package org.jeecg.modules.scaHandleHistory.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.util.StringUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.scaHandleHistory.entity.ScaHandleHistory;
import org.jeecg.modules.scaHandleHistory.service.IScaHandleHistoryService;
import org.jeecg.modules.sectest.scap.service.IScaProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

 /**
 * @Description: 缺陷处理历史表
 * @Author: Secidea One
 * @Date:   2022-12-14
 * @Version: V4.0
 */
@Tag(name = "缺陷处理历史表")
@RestController
@RequestMapping("/scaHandleHistory")
@Slf4j
public class ScaHandleHistoryController extends JeecgController<ScaHandleHistory, IScaHandleHistoryService> {
	@Autowired
	private IScaHandleHistoryService scaHandleHistoryService;
	@Autowired
	private IScaProjectService scaProjectService;

	/**
	 * 分页列表查询
	 * @param scaHandleHistory
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	// @AutoLog(value = "缺陷处理历史表-分页列表查询")
	@Operation(summary = "缺陷处理历史表-分页列表查询", description = "缺陷处理历史表-分页列表查询")
	@GetMapping(value = "/list")
//	@RequiresPermissions(value = "sca:handle:history:list")
	public Result<?> queryPageList(ScaHandleHistory scaHandleHistory,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<ScaHandleHistory> queryWrapper = new QueryWrapper<>();
		if ( oConvertUtils.isEmpty(scaHandleHistory) || StringUtils.isBlank(scaHandleHistory.getIssueId()) ){
			return Result.error("数据异常");
		}
		queryWrapper.lambda().eq(ScaHandleHistory::getIssueId,scaHandleHistory.getIssueId()).orderByDesc(ScaHandleHistory::getCreateTime);
		Page<ScaHandleHistory> page = new Page<ScaHandleHistory>(pageNo, pageSize);
//		addUserFiltering(queryWrapper);
		IPage<ScaHandleHistory> pageList = scaHandleHistoryService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

}
