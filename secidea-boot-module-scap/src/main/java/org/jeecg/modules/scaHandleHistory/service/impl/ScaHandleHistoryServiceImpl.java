package org.jeecg.modules.scaHandleHistory.service.impl;

import org.jeecg.modules.scaHandleHistory.entity.ScaHandleHistory;
import org.jeecg.modules.scaHandleHistory.mapper.ScaHandleHistoryMapper;
import org.jeecg.modules.scaHandleHistory.service.IScaHandleHistoryService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 缺陷处理历史表
 * @Author: Secidea One
 * @Date:   2022-12-14
 * @Version: V4.0
 */
@Service
public class ScaHandleHistoryServiceImpl extends ServiceImpl<ScaHandleHistoryMapper, ScaHandleHistory> implements IScaHandleHistoryService {

}
