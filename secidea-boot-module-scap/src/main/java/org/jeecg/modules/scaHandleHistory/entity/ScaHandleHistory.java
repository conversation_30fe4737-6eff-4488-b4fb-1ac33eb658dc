package org.jeecg.modules.scaHandleHistory.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.common.aspect.annotation.IEdit;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotNull;

/**
 * @Description: 缺陷处理历史表
 * @Author: Secidea One
 * @Date:   2022-12-14
 * @Version: V4.0
 */
@Data
@TableName("sca_handle_history")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "sca_handle_history对象", description = "缺陷处理历史表")
public class ScaHandleHistory implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
	@NotNull(message = "id不能为空",groups = {IEdit.class})
    @Schema(name = "主键")
    private String id;
	/**创建人*/
    @Schema(name = "创建人")
    private String createBy;
	/**创建日期*/
    @Schema(name = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(name = "更新人")
    private String updateBy;
	/**更新日期*/
    @Schema(name = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @Schema(name = "所属部门")
    private String sysOrgCode;
	/**项目ID*/
	@Excel(name = "项目ID", width = 15)
    @Schema(name = "项目ID")
    private String projectId;
	/**任务ID*/
	@Excel(name = "任务ID", width = 15)
    @Schema(name = "任务ID")
    private Integer projectVersionId;
	/**问题编号*/
	@Excel(name = "问题编号", width = 15)
    @Schema(name = "问题编号")
    private String issueId;
	/**确认处理状态*/
	@Excel(name = "确认处理状态", width = 15)
    @Schema(name = "确认处理状态")
    @Dict(dicCode = "process_status")
    private Integer nowProcessStatus;
	/**原始处理状态*/
	@Excel(name = "原始处理状态", width = 15)
    @Schema(name = "原始处理状态")
    @Dict(dicCode = "process_status")
    private Integer originalProcessStatus;
	/**当前整改结束时间*/
	@Excel(name = "当前整改结束时间", width = 15)
    @Schema(name = "当前整改结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date nowRectifyTime;
	/**原整改结束时间*/
	@Excel(name = "原整改结束时间", width = 15)
    @Schema(name = "原整改结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date originalRectifyTime;
	/**备注描述*/
	@Excel(name = "备注描述", width = 15)
    @Schema(name = "备注描述")
    private String description;
}
