package org.jeecg.modules.distortStatistics.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.distortStatistics.entity.ScaDistortStatistics;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 源码规则分析统计表
 * @Author: jeecg-boot
 * @Date:   2022-04-09
 * @Version: V1.0
 */
public interface ScaDistortStatisticsMapper extends BaseMapper<ScaDistortStatistics> {

    /**
     * 获取所有数据
     * @return
     */
    List<ScaDistortStatistics> getScaScanResultAllData();

}
