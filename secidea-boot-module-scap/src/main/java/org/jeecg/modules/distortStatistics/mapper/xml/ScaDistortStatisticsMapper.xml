<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.distortStatistics.mapper.ScaDistortStatisticsMapper">

    <select id="getScaScanResultAllData" resultType="org.jeecg.modules.distortStatistics.entity.ScaDistortStatistics">
        SELECT rule_id as sink_rule_id,src_rule_id,category,issue_zh_name as name,count(1) as total_count,tool_id,flag,
               sum( case when now_risk_level='4' then 1 else 0 end ) as now_high_count,
               sum( case when now_risk_level='3' then 1 else 0 end ) as now_medium_count,
               sum( case when now_risk_level='2' then 1 else 0 end ) as now_low_count,
               sum( case when now_risk_level='5' then 1 else 0 end ) as now_distort_count,
               sum( case when original_risk_level='4' then 1 else 0 end ) as original_high_count,
               sum( case when original_risk_level='3' then 1 else 0 end ) as original_medium_count,
               sum( case when original_risk_level='2' then 1 else 0 end ) as original_low_count,
               sum( case when original_risk_level='5' then 1 else 0 end ) as original_distort_count,
               sum( case when audited_type='1' then 1 else 0 end ) as type_origin,
               sum( case when audited_type='2' then 1 else 0 end ) as type_rule,
               sum( case when audited_type='3' then 1 else 0 end ) as type_manual,
               sum( case when audited_type='4' then 1 else 0 end ) as type_confirm

        from sca_scan_result where rule_id is not null and rule_id != '' GROUP BY rule_id,src_rule_id
    </select>

</mapper>
