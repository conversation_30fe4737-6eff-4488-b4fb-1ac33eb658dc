package org.jeecg.modules.distortStatistics.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.IEdit;
import org.jeecg.common.aspect.annotation.IUpdateStatus;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.util.StringUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.base.entity.IdsParam;
import org.jeecg.modules.distortStatistics.entity.ScaDistortPolicy;
import org.jeecg.modules.distortStatistics.service.IScaDistortPolicyService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.distortStatistics.service.IScaDistortStatisticsService;
import org.jeecg.modules.tableDict.service.ITableDictService;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 误报策略管理
 * @Author: Secidea One
 * @Date:   2022-05-19
 * @Version: V4.0
 */
@Tag(name = "误报策略管理")
@RestController
@RequestMapping("/scaDistortPolicy")
@Slf4j
public class ScaDistortPolicyController extends JeecgController<ScaDistortPolicy, IScaDistortPolicyService> {
	 @Autowired
	 private IScaDistortPolicyService scaDistortPolicyService;
	 @Autowired
	 private IScaDistortStatisticsService scaDistortStatisticsService;
	 @Autowired
	 private ITableDictService tableDictService;


	/**
	 * 分页列表查询
	 *
	 * @param scaDistortPolicy
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
//	@AutoLog(value = "误报策略管理-分页列表查询")
	@Operation(summary = "误报策略管理-分页列表查询", description = "误报策略管理-分页列表查询")
	@GetMapping(value = "/list")
	@RequiresPermissions(value = "scap:distort:policy:list")
	public Result<?> queryPageList(ScaDistortPolicy scaDistortPolicy,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<ScaDistortPolicy> queryWrapper = QueryGenerator.initQueryWrapper(scaDistortPolicy, req.getParameterMap());
		Page<ScaDistortPolicy> page = new Page<ScaDistortPolicy>(pageNo, pageSize);
//		addUserFiltering(queryWrapper);
		IPage<ScaDistortPolicy> pageList = scaDistortPolicyService.page(page, queryWrapper);
		if ( !scaDistortPolicyService.checkCanSynchronizationPolicy() ){
			return Result.OK("999",pageList);
		}
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param scaDistortPolicy
	 * @return
	 */
	@AutoLog(value = "误报策略管理-添加")
	@Operation(summary = "误报策略管理-添加", description = "误报策略管理-添加")
//	@PostMapping(value = "/add")
//	@RequiresPermissions(value = "scap:distort:policy:add")
	public Result<?> add(@RequestBody ScaDistortPolicy scaDistortPolicy) {
		scaDistortPolicyService.save(scaDistortPolicy);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param scaDistortPolicy
	 * @return
	 */
	@AutoLog(value = "误报策略管理-编辑")
	@Operation(summary = "误报策略管理-编辑", description = "误报策略管理-编辑")
//	@PutMapping(value = "/edit")
//	@RequiresPermissions(value = "scap:distort:policy:edit")
	public Result<?> edit(@RequestBody @Validated(value = {IEdit.class}) ScaDistortPolicy scaDistortPolicy) {
		scaDistortPolicyService.updateById(scaDistortPolicy);
		return Result.OK("编辑成功!");
	}

	 @AutoLog(value = "源码规则分析统计表-屏蔽规则")
	 @Operation(summary = "源码规则分析统计表-屏蔽规则", description = "源码规则分析统计表-屏蔽规则")
	 @PutMapping(value = "/updateIsExclude")
	 @RequiresPermissions(value = "scap:distort:policy:generate")
	 public Result<?> updateIsExclude(@RequestBody @Validated(value = {IUpdateStatus.class}) ScaDistortPolicy scaDistortPolicy) {

		 String ids = scaDistortPolicy.getId();
		 if ( StringUtils.isBlank(ids) || ids.contains(",,") ){
			 throw new JeecgBootException("数据异常");
		 }
		 if ( ids.endsWith(",") ){
			 ids = ids.substring(0,ids.length()-1);
		 }
		 List<String> idList = Arrays.asList(ids.split(","));
		 for (String id : idList) {
			 if ( StringUtils.isBlank(id) ){
				 return Result.error("操作失败");
			 }
		 }
		 // 将状态合并到误报管理模块
		 scaDistortPolicyService.update(
				 new ScaDistortPolicy().setStatus(scaDistortPolicy.getStatus()).setDescription(scaDistortPolicy.getDescription()==null?"":scaDistortPolicy.getDescription()),
				 new LambdaQueryWrapper<ScaDistortPolicy>().in(ScaDistortPolicy::getId,idList)
		 );
		 /*List<ScaDistortPolicy> scaDistortPolicyList = scaDistortPolicyService.list(new LambdaQueryWrapper<ScaDistortPolicy>().in(ScaDistortPolicy::getId, idList));
		 scaDistortPolicyList.forEach(o->{
			 ScaDistortStatistics scaDistortStatistics = scaDistortStatisticsService.getOne(new LambdaQueryWrapper<ScaDistortStatistics>().eq(ScaDistortStatistics::getSinkRuleId, o.getSinkRuleId()).eq(ScaDistortStatistics::getSrcRuleId, o.getSrcRuleId()).eq(ScaDistortStatistics::getToolId, o.getToolId()));
			 if ( !oConvertUtils.isEmpty(scaDistortStatistics) ){
				 // 存在，只更新状态
				 scaDistortStatistics.setIsExclude(o.getStatus()).setDescription(scaDistortPolicy.getDescription()==null?"":scaDistortPolicy.getDescription());
				 scaDistortStatisticsService.updateById(scaDistortStatistics);
			 }
		 });*/
		 return Result.OK("状态更新成功！");
	 }

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "误报策略管理-通过id删除")
	@Operation(summary = "误报策略管理-通过id删除", description = "误报策略管理-通过id删除")
	@DeleteMapping(value = "/delete")
	@RequiresPermissions(value = "scap:distort:policy:delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		ScaDistortPolicy scaDistortPolicy = scaDistortPolicyService.getById(id);
		if ( oConvertUtils.isEmpty(scaDistortPolicy) ){
			return Result.error("数据异常");
		}
		// 将当前id关联的数据改为 未屏蔽
		/*ScaDistortStatistics scaDistortStatistics = scaDistortStatisticsService.getOne(new LambdaQueryWrapper<ScaDistortStatistics>().eq(ScaDistortStatistics::getName, scaDistortPolicy.getName()).eq(ScaDistortStatistics::getSinkRuleId, scaDistortPolicy.getSinkRuleId()).eq(ScaDistortStatistics::getSrcRuleId, scaDistortPolicy.getSrcRuleId()).eq(ScaDistortStatistics::getToolId, scaDistortPolicy.getToolId()));
		if ( !oConvertUtils.isEmpty(scaDistortStatistics) ){
			scaDistortStatisticsService.updateById(scaDistortStatistics.setIsExclude(0));
		}*/
		scaDistortPolicyService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param idsParam
	 * @return
	 */
	@AutoLog(value = "误报策略管理-批量删除")
	@Operation(summary = "误报策略管理-批量删除", description = "误报策略管理-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	@RequiresPermissions(value = "scap:distort:policy:delete")
	public Result<?> deleteBatch(@RequestBody @Validated IdsParam idsParam) {
		String ids = idsParam.getIds();
		if ( StringUtils.isBlank(ids) || ids.contains(",,") ){
			throw new JeecgBootException("数据异常");
		}
		if ( ids.endsWith(",") ){
			ids = ids.substring(0,ids.length()-1);
		}
		List<String> idList = Arrays.asList(ids.split(","));
		for (String id : idList) {
			if ( StringUtils.isBlank(id) ){
				return Result.error("操作失败");
			}
		}
		List<ScaDistortPolicy> scaDistortPolicyList = scaDistortPolicyService.listByIds(idList);
		if ( scaDistortPolicyList.isEmpty() ){
			return Result.error("数据异常");
		}
		/*List<ScaDistortStatistics> scaDistortStatisticsList = new ArrayList<>();
		scaDistortPolicyList.forEach(o->{
			ScaDistortStatistics scaDistortStatistics = scaDistortStatisticsService.getOne(new LambdaQueryWrapper<ScaDistortStatistics>().eq(ScaDistortStatistics::getName, o.getName()).eq(ScaDistortStatistics::getSinkRuleId, o.getSinkRuleId()).eq(ScaDistortStatistics::getSrcRuleId, o.getSrcRuleId()).eq(ScaDistortStatistics::getToolId, o.getToolId()));
			if ( !oConvertUtils.isEmpty(scaDistortStatistics) ){
				scaDistortStatistics.setIsExclude(0);
				scaDistortStatisticsList.add(scaDistortStatistics);
			}
		});*/


		scaDistortPolicyService.removeByIds(idList);
//		scaDistortStatisticsService.updateBatchById(scaDistortStatisticsList);

		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
//	@AutoLog(value = "误报策略管理-通过id查询")
	@Operation(summary = "误报策略管理-通过id查询", description = "误报策略管理-通过id查询")
	@GetMapping(value = "/queryById")
	@RequiresPermissions(value = "scap:distort:policy:list")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		ScaDistortPolicy scaDistortPolicy = scaDistortPolicyService.getById(id);
		if(scaDistortPolicy==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(scaDistortPolicy);
	}

	 /**
	  * 同步策略
	  *
	  * @param
	  * @return
	  */
	 @AutoLog(value = "误报策略管理-同步策略")
	 @Operation(summary = "误报策略管理-同步策略", description = "误报策略管理-同步策略")
	 @GetMapping(value = "/synchronizationPolicy")
	 public Result<?> synchronizationPolicy() {
		 if ( !scaDistortPolicyService.checkCanSynchronizationPolicy() ){
			 return Result.error("策略正在执行！");
		 }
		 scaDistortPolicyService.synchronizationPolicy();
		 return Result.OK("正在同步策略");
	 }

    /**
    * 导出excel
    *
    * @param jsonObject
    */
    @PostMapping(value = "/exportXls")
    @RequiresPermissions(value = "scap:distort:policy:export")
    public ModelAndView exportXls(@RequestBody JSONObject jsonObject) {
        return super.exportXls(jsonObject, ScaDistortPolicy.class, "误报策略管理");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    @RequiresPermissions(value = "scap:distort:policy:import")
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ScaDistortPolicy.class);
    }

}
