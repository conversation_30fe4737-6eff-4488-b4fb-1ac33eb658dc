package org.jeecg.modules.distortStatistics.service;

import org.jeecg.modules.distortStatistics.entity.ScaDistortStatistics;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.scheduling.annotation.Async;

/**
 * @Description: 源码规则分析统计表
 * @Author: jeecg-boot
 * @Date:   2022-04-09
 * @Version: V1.0
 */
public interface IScaDistortStatisticsService extends IService<ScaDistortStatistics> {

    /**
     * 判断是否可以进入事务处理
     * @return t
     */
    boolean checkCanGenerate();

    /**
     *  加载数据
     *  @return
     */
    @Async
    void generate();

}
