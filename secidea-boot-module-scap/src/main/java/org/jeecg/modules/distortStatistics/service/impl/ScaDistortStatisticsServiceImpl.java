package org.jeecg.modules.distortStatistics.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.math3.stat.interval.ConfidenceInterval;
import org.jeecg.common.util.ConfidenceIntervalUtils;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.distortStatistics.entity.ScaDistortPolicy;
import org.jeecg.modules.distortStatistics.entity.ScaDistortStatistics;
import org.jeecg.modules.distortStatistics.mapper.ScaDistortStatisticsMapper;
import org.jeecg.modules.distortStatistics.service.IScaDistortPolicyService;
import org.jeecg.modules.distortStatistics.service.IScaDistortStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 源码规则分析统计表
 * @Author: jeecg-boot
 * @Date:   2022-04-09
 * @Version: V1.0
 */
@Service
public class ScaDistortStatisticsServiceImpl extends ServiceImpl<ScaDistortStatisticsMapper, ScaDistortStatistics> implements IScaDistortStatisticsService {

    @Autowired
    private ScaDistortStatisticsMapper scaDistortStatisticsMapper;

    @Autowired
    private IScaDistortPolicyService scaDistortPolicyService;

    @Autowired
    private RedisUtil redisUtil;

    private String lockKey = "scap:distort:statistics:generate";


    @Override
    public boolean checkCanGenerate() {

        // 增加事务处理机制
        if ( redisUtil.hasKey(lockKey) ){
            return false;
        }
        return true;

    }

    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void generate(){
        // 加锁
        redisUtil.set(lockKey,"1",600);

        try {
            // 查询所有数据，对已有记录的数据执行更新操作
            Map<String,ScaDistortStatistics> map = new HashMap<>(0);
            List<ScaDistortStatistics> scaDistortStatisticsList = list(new LambdaQueryWrapper<ScaDistortStatistics>().select(ScaDistortStatistics::getId, ScaDistortStatistics::getSrcRuleId, ScaDistortStatistics::getSinkRuleId,ScaDistortStatistics::getDescription));
            if ( !scaDistortStatisticsList.isEmpty() ){
                map = new HashMap<>(scaDistortStatisticsList.size());
                for (ScaDistortStatistics scaDistortStatistics : scaDistortStatisticsList) {
                    String key = scaDistortStatistics.getSrcRuleId() + "_" + scaDistortStatistics.getSinkRuleId();
                    if ( !map.containsKey(key) ){
                        map.put(key,scaDistortStatistics);
                    }
                }
            }
            List<ScaDistortStatistics> addList = new ArrayList<>();
            List<ScaDistortStatistics> updateList = new ArrayList<>();
            List<String> deleteIdList = new ArrayList<>();

            List<ScaDistortStatistics> scaScanResultAllData = scaDistortStatisticsMapper.getScaScanResultAllData();
            Map<String, ScaDistortStatistics> finalMap = map;
            List<String> isUseKeyList = new ArrayList<>(scaScanResultAllData.size());

            scaScanResultAllData.forEach(o->{
                // 计算误报率 和 置信区间
                if ( o.getTotalCount() - o.getTypeOrigin() != 0 ){
                    o.setDistortRate( o.getNowDistortCount() * 100 / ( o.getTotalCount() - o.getTypeOrigin() ) );
                    int numberOfTrials = o.getTotalCount() - o.getTypeOrigin();
                    int numberOfSuccesses = o.getNowDistortCount();

                    if ( numberOfTrials >= numberOfSuccesses && numberOfTrials > 0 && numberOfSuccesses > 0 ){
                        ConfidenceInterval interval = ConfidenceIntervalUtils.createInterval(numberOfTrials, numberOfSuccesses, 0.95);
                        o.setConfidenceIntervalLeft((int)(interval.getLowerBound()*100))
                                .setConfidenceIntervalRight((int)(interval.getUpperBound()*100));
                    }else {
                        o.setConfidenceIntervalLeft(0).setConfidenceIntervalRight(100);
                    }
                } else {
                    o.setConfidenceIntervalLeft(0).setConfidenceIntervalRight(100);
                }
                String key = o.getSrcRuleId() + "_" + o.getSinkRuleId();
                // 判断是否存在
                List<ScaDistortPolicy> policyList = scaDistortPolicyService.list(new LambdaQueryWrapper<ScaDistortPolicy>().eq(ScaDistortPolicy::getName,o.getName()).eq(ScaDistortPolicy::getSinkRuleId, o.getSinkRuleId()).eq(ScaDistortPolicy::getSrcRuleId, o.getSrcRuleId()).eq(ScaDistortPolicy::getToolId, o.getToolId()));
                if ( !policyList.isEmpty() ){
                    o.setIsExclude(1);
                } else {
                    o.setIsExclude(0);
                }
                if ( finalMap.containsKey(key) ){
                    o.setId( finalMap.get(key).getId() );
                    updateList.add(o);
                } else {
                    addList.add(o);
                }
                isUseKeyList.add(key);
            });
            // 如果原来数据库中的数据存在统计出来依旧没有的，执行删除
            scaDistortStatisticsList.forEach(o->{
                String key = o.getSrcRuleId() + "_" + o.getSinkRuleId();
                if ( !isUseKeyList.contains(key) ){
                    deleteIdList.add(o.getId());
                }
            });
            if ( !addList.isEmpty() ){
                saveBatch(addList);
            }
            if ( !updateList.isEmpty() ){
                updateBatchById(updateList);
            }
            log.debug(String.format("删除数据，共%s条",deleteIdList.size()));

            if ( !deleteIdList.isEmpty() ){
                removeByIds(deleteIdList);
            }

        } catch ( Exception e ){
            e.printStackTrace();
            // 解锁
            if ( redisUtil.hasKey(lockKey) ){
                redisUtil.del(lockKey);
            }
        }

        // 解锁
        if ( redisUtil.hasKey(lockKey) ){
            redisUtil.del(lockKey);
        }

        // 将结果更新到误报策略管理


    }



}
