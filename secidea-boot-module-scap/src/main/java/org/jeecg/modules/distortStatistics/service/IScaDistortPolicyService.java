package org.jeecg.modules.distortStatistics.service;

import org.jeecg.modules.distortStatistics.entity.ScaDistortPolicy;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.scheduling.annotation.Async;

/**
 * @Description: 误报策略管理
 * @Author: Secidea One
 * @Date:   2022-05-19
 * @Version: V4.0
 */
public interface IScaDistortPolicyService extends IService<ScaDistortPolicy> {

    boolean checkCanSynchronizationPolicy();
    @Async
    void synchronizationPolicy();
}
