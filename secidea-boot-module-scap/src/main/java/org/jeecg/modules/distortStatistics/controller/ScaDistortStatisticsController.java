package org.jeecg.modules.distortStatistics.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.IUpdateStatus;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.StringUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.distortStatistics.entity.ScaDistortPolicy;
import org.jeecg.modules.distortStatistics.entity.ScaDistortStatistics;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.distortStatistics.service.IScaDistortPolicyService;
import org.jeecg.modules.distortStatistics.service.IScaDistortStatisticsService;
import org.jeecg.modules.tableDict.service.ITableDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 源码规则分析统计表
 * @Author: jeecg-boot
 * @Date:   2022-04-09
 * @Version: V1.0
 */
@Tag(name = "源码规则分析统计表")
@RestController
@RequestMapping("/scaDistortStatistics")
@Slf4j
public class ScaDistortStatisticsController extends JeecgController<ScaDistortStatistics, IScaDistortStatisticsService> {
	@Autowired
	private IScaDistortStatisticsService scaDistortStatisticsService;
	@Autowired
	private IScaDistortPolicyService scaDistortPolicyService;

	@Autowired
	private RedisUtil redisUtil;
	private String generateKey = "lockKey:redis:lock:scap:distort:statistics:generate";

	@Autowired
	private ITableDictService tableDictService;

	/**
	 * 分页列表查询
	 *
	 * @param scaDistortStatistics
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
//	@AutoLog(value = "源码规则分析统计表-分页列表查询")
	@Operation(summary = "源码规则分析统计表-分页列表查询", description = "源码规则分析统计表-分页列表查询")
	@GetMapping(value = "/list")
	@RequiresPermissions(value = "scap:distort:statistics:list")
	public Result<?> queryPageList(ScaDistortStatistics scaDistortStatistics,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<ScaDistortStatistics> queryWrapper = QueryGenerator.initQueryWrapper(scaDistortStatistics, req.getParameterMap());
		Page<ScaDistortStatistics> page = new Page<ScaDistortStatistics>(pageNo, pageSize);
		IPage<ScaDistortStatistics> pageList = scaDistortStatisticsService.page(page, queryWrapper);
		if ( !scaDistortStatisticsService.checkCanGenerate() ){
			return Result.OK("999",pageList);
		}
		return Result.OK(pageList);
	}

	 @AutoLog(value = "源码规则分析统计表-添加")
	 @Operation(summary = "源码规则分析统计表-添加", description = "源码规则分析统计表-添加")
	 @GetMapping(value = "/generate")
	 @RequiresPermissions(value = "scap:distort:statistics:generate")
	 public Result<?> generate() {

 		 if ( scaDistortStatisticsService.checkCanGenerate() ){
			 scaDistortStatisticsService.generate();
			 return Result.OK("正在处理，请稍后查询！");
		 } else {
			 return Result.error(999,"数据正在处理中，请稍后再处理！");
		 }

	 }

	 @AutoLog(value = "源码规则分析统计表-屏蔽规则")
	 @Operation(summary = "源码规则分析统计表-屏蔽规则", description = "源码规则分析统计表-屏蔽规则")
	 @PutMapping(value = "/updateIsExclude")
	 @RequiresPermissions(value = "scap:distort:statistics:generate")
	 public Result<?> updateIsExclude(@RequestBody @Validated(value = {IUpdateStatus.class}) ScaDistortStatistics scaDistortStatistics) {
		 String ids = scaDistortStatistics.getId();
		 if ( StringUtils.isBlank(ids) || ids.contains(",,") ){
			 throw new JeecgBootException("数据异常");
		 }
		 if ( ids.endsWith(",") ){
			 ids = ids.substring(0,ids.length()-1);
		 }
		 List<String> idList = Arrays.asList(ids.split(","));
		 for (String id : idList) {
			 if ( StringUtils.isBlank(id) ){
			 	return Result.error("操作失败");
			 }
		 }
		 scaDistortStatisticsService.update(
				 new ScaDistortStatistics().setIsExclude(scaDistortStatistics.getIsExclude()).setDescription(scaDistortStatistics.getDescription()),
				 new LambdaQueryWrapper<ScaDistortStatistics>().in(ScaDistortStatistics::getId,idList)
		 );

		 // 检查数据是否插入过
		 List<ScaDistortStatistics> scaDistortStatisticsList = scaDistortStatisticsService.list(new LambdaQueryWrapper<ScaDistortStatistics>().in(ScaDistortStatistics::getId, idList));
		 scaDistortStatisticsList.forEach(o->{
			 ScaDistortPolicy policy = scaDistortPolicyService.getOne(new LambdaQueryWrapper<ScaDistortPolicy>().eq(ScaDistortPolicy::getSinkRuleId, o.getSinkRuleId()).eq(ScaDistortPolicy::getSrcRuleId, o.getSrcRuleId()).eq(ScaDistortPolicy::getToolId, o.getToolId()));
			 if ( oConvertUtils.isEmpty(policy) ){
			 	// 不存在，插入数据
				 policy = new ScaDistortPolicy();
				 policy.setName(o.getName())
						 .setSinkRuleId(o.getSinkRuleId())
						 .setSrcRuleId(o.getSrcRuleId())
						 .setToolId(o.getToolId())
						 .setDescription(o.getDescription()==null?"":o.getDescription())
						 .setFlag(o.getFlag())
						 .setStatus(scaDistortStatistics.getIsExclude());
				 scaDistortPolicyService.save(policy);
			 } else {
				// 存在，只更新状态
				 policy.setStatus(scaDistortStatistics.getIsExclude())
						 .setFlag(o.getFlag())
						 .setDescription(o.getDescription()==null?"":o.getDescription());
				 scaDistortPolicyService.updateById(policy);
			 }
		 });

		 return Result.OK("状态更新成功！");
	 }

	/**
	 *   添加
	 *
	 * @param scaDistortStatistics
	 * @return
	 */
	@AutoLog(value = "源码规则分析统计表-添加")
	@Operation(summary = "源码规则分析统计表-添加", description = "源码规则分析统计表-添加")
//	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody ScaDistortStatistics scaDistortStatistics) {
		scaDistortStatisticsService.save(scaDistortStatistics);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param scaDistortStatistics
	 * @return
	 */
	@AutoLog(value = "源码规则分析统计表-编辑")
	@Operation(summary = "源码规则分析统计表-编辑", description = "源码规则分析统计表-编辑")
//	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody ScaDistortStatistics scaDistortStatistics) {
		scaDistortStatisticsService.updateById(scaDistortStatistics);
		return Result.OK("编辑成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
//	@AutoLog(value = "源码规则分析统计表-通过id查询")
	@Operation(summary = "源码规则分析统计表-通过id查询", description = "源码规则分析统计表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		ScaDistortStatistics scaDistortStatistics = scaDistortStatisticsService.getById(id);
		if(scaDistortStatistics==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(scaDistortStatistics);
	}

    /**
    * 导出excel
    *
    * @param jsonObject
    */
    @PostMapping(value = "/exportXls")
	@RequiresRoles(value = {"admin","dev_admin"}, logical = Logical.OR)
    public ModelAndView exportXls(@RequestBody JSONObject jsonObject) {
        return super.exportXls(jsonObject, ScaDistortStatistics.class, "源码规则分析统计表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
	@RequiresPermissions(value = "scap:distort:statistics:export")
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ScaDistortStatistics.class);
    }

}
