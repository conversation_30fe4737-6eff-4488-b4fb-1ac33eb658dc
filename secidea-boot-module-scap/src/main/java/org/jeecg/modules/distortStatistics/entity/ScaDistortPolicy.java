package org.jeecg.modules.distortStatistics.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.jeecg.common.aspect.annotation.IEdit;
import org.jeecg.common.aspect.annotation.IUpdateStatus;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotNull;

/**
 * @Description: 误报策略管理
 * @Author: Secidea One
 * @Date:   2022-05-19
 * @Version: V4.0
 */
@Data
@TableName("sca_distort_policy")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "sca_distort_policy对象", description = "误报策略管理")
public class ScaDistortPolicy implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
	@NotNull(message = "id不能为空",groups = {IUpdateStatus.class})
    @Schema(name = "主键")
    private java.lang.String id;
	/**创建人*/
    @Schema(name = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(name = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(name = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(name = "更新日期")
    private java.util.Date updateTime;
	/**所属群组*/
    @Schema(name = "所属群组")
    private java.lang.String sysOrgCode;
	/**缺陷名称*/
	@Excel(name = "缺陷名称", width = 15)
    @Schema(name = "缺陷名称")
    private java.lang.String name;
	/**Sink规则id*/
	@Excel(name = "Sink规则id", width = 15)
    @Schema(name = "Sink规则id")
    private java.lang.String sinkRuleId;
	/**Source规则id*/
	@Excel(name = "Source规则id", width = 15)
    @Schema(name = "Source规则id")
    private java.lang.String srcRuleId;
	/**工具id*/
	@Excel(name = "工具id", width = 15)
    @Schema(name = "工具id")
    private java.lang.String toolId;
	/**flag*/
	@Excel(name = "flag", width = 15)
    @Schema(name = "flag")
    private java.lang.String flag;
	/**是否屏蔽*/
	@Excel(name = "是否屏蔽", width = 15)
    @Schema(name = "是否屏蔽")
    private java.lang.Integer status;
//    @TableField(updateStrategy= FieldStrategy.IGNORED)
    @Excel(name = "备注", width = 15)
    @Schema(name = "备注")
    private String description;
}
