package org.jeecg.modules.distortStatistics.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.StringUtils;
import org.jeecg.modules.distortStatistics.entity.ScaDistortPolicy;
import org.jeecg.modules.distortStatistics.mapper.ScaDistortPolicyMapper;
import org.jeecg.modules.distortStatistics.service.IScaDistortPolicyService;
import org.jeecg.modules.sectest.scap.entity.ScaScanResult;
import org.jeecg.modules.sectest.scap.service.IScaScanResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 误报策略管理
 * @Author: Secidea One
 * @Date: 2022-05-19
 * @Version: V4.0
 */
@Service
public class ScaDistortPolicyServiceImpl extends ServiceImpl<ScaDistortPolicyMapper, ScaDistortPolicy> implements IScaDistortPolicyService {

    @Autowired
    private IScaScanResultService scaScanResultService;
    @Autowired
    private RedisUtil redisUtil;

    private String lockKey = "scap:distort:policy:synchronizationPolicy";

    @Override
    public boolean checkCanSynchronizationPolicy() {

        // 增加事务处理机制
        if ( redisUtil.hasKey(lockKey) ){
            return false;
        }
        return true;

    }

    @Override
    public void synchronizationPolicy() {
        redisUtil.set(lockKey,"1",0);
        try {
            LambdaQueryWrapper<ScaDistortPolicy> scaDistortPolicyLambdaQueryWrapper = new LambdaQueryWrapper<>();
            scaDistortPolicyLambdaQueryWrapper.select(ScaDistortPolicy::getSinkRuleId, ScaDistortPolicy::getSrcRuleId, ScaDistortPolicy::getFlag);
            scaDistortPolicyLambdaQueryWrapper.eq(ScaDistortPolicy::getStatus, 1);
            List<ScaDistortPolicy> scaDistortPolicies = this.list(scaDistortPolicyLambdaQueryWrapper);
            LambdaQueryWrapper<ScaScanResult> scaScanResultLambdaQueryWrapper = new LambdaQueryWrapper<>();
            scaScanResultLambdaQueryWrapper.select(ScaScanResult::getId, ScaScanResult::getRuleId, ScaScanResult::getSrcRuleId, ScaScanResult::getFlag);
            List<ScaScanResult> scaScanResultList = new ArrayList<>();
            List<ScaScanResult> scaScanResults = scaScanResultService.list(scaScanResultLambdaQueryWrapper);
            for (ScaScanResult scaScanResult : scaScanResults) {
                int count = 0;
                String scanFlag = scaScanResult.getFlag();
                String scanSrcRuleId = scaScanResult.getSrcRuleId();
                String scanSinkRuleId = scaScanResult.getRuleId();
                for (ScaDistortPolicy scaDistortPolicy : scaDistortPolicies) {
                    String sinkRuleId = scaDistortPolicy.getSinkRuleId();
                    String srcRuleId = scaDistortPolicy.getSrcRuleId();
                    String flag = scaDistortPolicy.getFlag();
                    if ("*".equals(sinkRuleId)) {
                        if ("*".equals(srcRuleId)) {
                            if (StringUtils.isNotBlank(scanFlag) && scanFlag.equals(flag)) {
                                count++;
                                break;
                            }
                        } else if (StringUtils.isNotBlank(scanSrcRuleId) && scanSrcRuleId.equals(srcRuleId)) {
                            count++;
                            break;
                        }
                    } else {
                        if (scanSrcRuleId != null && scanSinkRuleId != null && (scanSrcRuleId + scanSinkRuleId).equals(srcRuleId + sinkRuleId)) {
                            count++;
                            break;
                        }
                    }
                }
                if (count > 0){
                    scaScanResult.setNowRiskLevel(5);
                    scaScanResultList.add(scaScanResult);
                }
            }
            scaScanResultList = scaScanResultList.stream().distinct().collect(Collectors.toList());
            scaScanResultService.updateBatchById(scaScanResultList);
        } catch (Exception e) {
            // 解锁
            if (redisUtil.hasKey(lockKey)) {
                redisUtil.del(lockKey);
            }
        }
        // 解锁
        if ( redisUtil.hasKey(lockKey) ){
            redisUtil.del(lockKey);
        }
    }
}
