package org.jeecg.modules.distortStatistics.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.jeecg.common.aspect.annotation.IUpdateStatus;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * @Description: 源码规则分析统计表
 * @Author: jeecg-boot
 * @Date:   2022-04-09
 * @Version: V1.0
 */
@Data
@TableName("sca_distort_statistics")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "sca_distort_statistics对象", description = "源码规则分析统计表")
public class ScaDistortStatistics implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(name = "主键")
    @NotNull(message = "id不能为空", groups = {IUpdateStatus.class})
    private String id;
	/**创建人*/
    @Schema(name = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(name = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(name = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(name = "更新日期")
    private Date updateTime;
	/**所属群组*/
    @Schema(name = "所属群组")
    private String sysOrgCode;
	/**规则id*/
	@Excel(name = "Sink规则ID", width = 15)
    @Schema(name = "规则id")
    private String sinkRuleId;
	/**规则源id*/
	@Excel(name = "Source规则ID", width = 15)
    @Schema(name = "规则源id")
//    @Dict(dictTable = "sca_rule_flag", dicText = "flag", dicCode = "rule_id")
    private String srcRuleId;
    @Excel(name = "Flag", width = 15)
    @Schema(name = "flag")
    private String flag;
	/**缺陷分类*/
	@Excel(name = "缺陷分类", width = 15)
    @Schema(name = "缺陷分类")
    private String category;
	/**缺陷名称*/
	@Excel(name = "缺陷名称", width = 15)
    @Schema(name = "缺陷名称")
    private String name;
    /**原始高危数*/
    @Excel(name = "原始高危数", width = 15)
    @Schema(name = "原始高危数")
    private Integer originalHighCount;
    /**原始中危数*/
    @Excel(name = "原始中危数", width = 15)
    @Schema(name = "原始中危数")
    private Integer originalMediumCount;
    /**原始低危数*/
    @Excel(name = "原始低危数", width = 15)
    @Schema(name = "原始低危数")
    private Integer originalLowCount;
    /**原始误报数*/
    @Excel(name = "原始误报数", width = 15)
    @Schema(name = "原始误报数")
    private Integer originalDistortCount;
	/**高危数*/
	@Excel(name = "高危数", width = 15)
    @Schema(name = "高危数")
    private Integer nowHighCount;
	/**中危数*/
	@Excel(name = "中危数", width = 15)
    @Schema(name = "中危数")
    private Integer nowMediumCount;
	/**低危数*/
	@Excel(name = "低危数", width = 15)
    @Schema(name = "低危数")
    private Integer nowLowCount;
	/**误报数*/
	@Excel(name = "误报数", width = 15)
    @Schema(name = "误报数")
    private Integer nowDistortCount;
    @Excel(name = "总数", width = 15)
    @Schema(name = "总数")
    private Integer totalCount;
	/**是否确认已做审计，1 未审计 2 全局规则 3 人工*/
	@Excel(name = "是否确认已做审计，1 未审计", width = 15)
    @Schema(name = "未审计")
    private Integer typeOrigin;
    @Excel(name = "是否确认已做审计，2 全局规则 ", width = 15)
    @Schema(name = "全局规则")
    private Integer typeRule;
    @Excel(name = "是否确认已做审计，3 人工", width = 15)
    @Schema(name = "人工")
    private Integer typeManual;
    @Excel(name = "是否确认已做审计，4 人工", width = 15)
    @Schema(name = "已验证")
    private Integer typeConfirm;
	/**误报率*/
	@Excel(name = "误报率", width = 15)
    @Schema(name = "误报率")
    private Integer distortRate;
	/**置信区间*/
	/*@Excel(name = "置信区间", width = 15)
    @Schema(name = "置信区间")
    private String confidenceInterval;*/
    @Excel(name = "置信区间_左", width = 15)
    @Schema(name = "置信区间")
    private Integer confidenceIntervalLeft;
    @Excel(name = "置信区间_右", width = 15)
    @Schema(name = "置信区间")
    private Integer confidenceIntervalRight;
    @Excel(name = "工具代号", width = 15)
    @Schema(name = "工具代号")
    private String toolId;
    @TableField(updateStrategy= FieldStrategy.IGNORED)
    @Excel(name = "备注", width = 15)
    @Schema(name = "备注")
    private String description;

    @Excel(name = "是否屏蔽", width = 15, dicCode = "yn")
    @Dict(dicCode = "yn")
    @Schema(name = "是否屏蔽")
    @NotNull(message = "状态不能为空", groups = {IUpdateStatus.class})
    private Integer isExclude;
}
