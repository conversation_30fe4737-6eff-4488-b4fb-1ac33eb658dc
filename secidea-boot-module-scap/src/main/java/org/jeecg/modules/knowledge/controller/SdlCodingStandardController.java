package org.jeecg.modules.knowledge.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.ApiVerifyInterfaceUtils;
import org.jeecg.common.util.CommonUtil;
import org.jeecg.common.util.StringUtils;
import org.jeecg.common.util.verificationInterfaceEntity;
import org.jeecg.modules.base.entity.IdsParam;
import org.jeecg.modules.knowledge.entity.SdlCodingStandard;
import org.jeecg.modules.knowledge.entity.SdlScaScanItem;
import org.jeecg.modules.knowledge.entity.SdlStandardReqItems;
import org.jeecg.modules.knowledge.service.ISdlCodingStandardService;
import org.jeecg.modules.knowledge.service.ISdlScaScanItemService;
import org.jeecg.modules.knowledge.service.ISdlStandardReqItemsService;
import org.jeecg.modules.knowledge.utils.myapi.ListStandNamesReq;
import org.jeecg.modules.knowledge.utils.myapi.ListStandNamesResp;
import org.jeecg.modules.sectest.scap.entity.ScaProject;
import org.jeecg.modules.sectest.scap.entity.ScaProjectVersion;
import org.jeecg.modules.sectest.scap.service.IScaProjectService;
import org.jeecg.modules.sectest.scap.service.IScaProjectVersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

/**
 * @Description: 安全编码合规要求
 * @Author: jeecg-boot
 * @Date:   2021-08-01
 * @Version: V1.0
 */
@Tag(name = "安全编码合规要求")
@RestController
@RequestMapping("/sdlCodingStandard")
@Slf4j
public class SdlCodingStandardController extends JeecgController<SdlCodingStandard, ISdlCodingStandardService> {
	@Autowired
	private ISdlCodingStandardService sdlCodingStandardService;
	@Autowired
	private ISdlStandardReqItemsService sdlStandardReqItemsService;
	@Autowired
	private ISdlScaScanItemService sdlScaScanItemService;
	@Autowired
	private IScaProjectService scaProjectService;
	@Autowired
	private IScaProjectVersionService scaProjectVersionService;
	@Autowired
	private ApiVerifyInterfaceUtils apiVerifyInterfaceUtils;

	/**
	 * 分页列表查询
	 *
	 * @param sdlCodingStandard
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
//	@AutoLog(value = "安全编码合规要求-分页列表查询")
	@Operation(summary = "安全编码合规要求-分页列表查询", description = "安全编码合规要求-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(SdlCodingStandard sdlCodingStandard,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		Map<String, String[]> parameterMap = new HashMap(req.getParameterMap());
		parameterMap.remove("column");
		parameterMap.remove("order");
		QueryWrapper<SdlCodingStandard> queryWrapper = QueryGenerator.initQueryWrapper(sdlCodingStandard, parameterMap);
		queryWrapper.lambda().orderByDesc(SdlCodingStandard::getActiveStatus).orderByAsc(SdlCodingStandard::getSortOrder);
		Page<SdlCodingStandard> page = new Page<SdlCodingStandard>(pageNo, pageSize);
		IPage<SdlCodingStandard> pageList = sdlCodingStandardService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	 /**
	  * 列表查询
	  *
	  * @return
	  */
//	 @AutoLog(value = "安全编码合规要求-列表查询")
	 @Operation(summary = "安全编码合规要求-列表查询", description = "安全编码合规要求-列表查询")
	 @GetMapping(value = "/listAll")
	 public Result<?> listAll() {
		 return Result.OK(sdlCodingStandardService.list(new LambdaQueryWrapper<SdlCodingStandard>()
				 .select(SdlCodingStandard::getId, SdlCodingStandard::getStandardName, SdlCodingStandard::getActiveStatus)
				 .eq(SdlCodingStandard::getStatus, 2)
				 .orderByDesc(SdlCodingStandard::getActiveStatus)
				 .orderByAsc(SdlCodingStandard::getSortOrder)));
	 }

	@Operation(summary = "安全编码合规要求-列出所有合规要求名称", description = "安全编码合规要求-列出所有合规要求名称")
	@PostMapping("/myapis/getStandardNameList")
	public Object listSdlCodingStandNames(@RequestBody ListStandNamesReq listStandNamesReq) {
		ListStandNamesResp resp = new ListStandNamesResp("0", "", null);
		verificationInterfaceEntity verificationInterfaceEntity = apiVerifyInterfaceUtils.apiVerifyInterface(
			JSONObject.parseObject(JSONObject.toJSONString(listStandNamesReq)));
		String code = verificationInterfaceEntity.getCode();
		if ("0".equals(code)) {
			resp.setMsg(verificationInterfaceEntity.getMsg());
			return resp;
		}

		List<SdlCodingStandard> standards = sdlCodingStandardService.list(
			new LambdaQueryWrapper<SdlCodingStandard>()
				.select(SdlCodingStandard::getStandardName)
				.eq(SdlCodingStandard::getStatus, 2)
				.orderByDesc(SdlCodingStandard::getActiveStatus)
				.orderByAsc(SdlCodingStandard::getSortOrder));
		List<String> standardNames = standards.stream().map(SdlCodingStandard::getStandardName)
			.collect(Collectors.toList());
		resp.setCode("1");
		resp.setResult(standardNames);
		return resp;
	}

	/**
	 * 查询模板名称是否可用
	 *
	 * @param id
	 * @param standardName
	 * @return
	 */
	@Operation(summary = "安全编码合规要求-查询模板名称是否可用", description = "安全编码合规要求-查询模板名称是否可用")
	@GetMapping(value = "/isTheStandardNameAvailable")
	public Result<?> isTheStandardNameAvailable(@RequestParam(name = "id", required = false) String id, @RequestParam(name = "standardName", required = true) String standardName) {
		LambdaQueryWrapper<SdlCodingStandard> lambdaQueryWrapper = new LambdaQueryWrapper<>();
		lambdaQueryWrapper.eq(SdlCodingStandard::getStandardName, standardName);
		if (StringUtils.isNotBlank(id)) {
			lambdaQueryWrapper.ne(SdlCodingStandard::getId, id);
		}
		long num = sdlCodingStandardService.count(lambdaQueryWrapper);
		if (num == 0) {
			return Result.OK("模板名称可用！");
		} else {
			return Result.error("模板名称已经存在！");
		}
	}

	/**
	 *   添加
	 *
	 * @param sdlCodingStandard
	 * @return
	 */
	@AutoLog(value = "安全编码合规要求-添加")
	@Operation(summary = "安全编码合规要求-添加", description = "安全编码合规要求-添加")
	@PostMapping(value = "/add")
	@RequiresPermissions(value="scap:sdl:coding:standard:add")
	public Result<?> add(@RequestBody SdlCodingStandard sdlCodingStandard) {
		if (sdlCodingStandardService.count(new LambdaQueryWrapper<SdlCodingStandard>().eq(SdlCodingStandard::getStandardName, sdlCodingStandard.getStandardName())) > 0) {
			return Result.error("模板名称重复，请检查！");
		}
		//如果要激活某个条目，则先屏蔽其他的条目
		if(sdlCodingStandard.getActiveStatus() !=null && sdlCodingStandard.getActiveStatus()==1){
			QueryWrapper<SdlCodingStandard> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("active_status", 1);
			List<SdlCodingStandard> sdlSiteManagerList = sdlCodingStandardService.list(queryWrapper);
			for (SdlCodingStandard siteMsg:sdlSiteManagerList) {
				siteMsg.setActiveStatus(0);
				sdlCodingStandardService.updateById(siteMsg);
			}
		}
		sdlCodingStandard.setEditable(true);
		sdlCodingStandardService.save(sdlCodingStandard);
		String templateId = sdlCodingStandard.getTemplateId();
		if (StringUtils.isNotBlank(templateId)) {
			SdlCodingStandard standard = sdlCodingStandardService.getById(templateId);
			if (standard == null){
				return Result.error("模板不存在！");
			}
			List<SdlScaScanItem> sdlScaScanItemList = new ArrayList<>();
			LambdaQueryWrapper<SdlStandardReqItems> sdlStandardReqItemsLambdaQueryWrapper = new LambdaQueryWrapper<>();
			sdlStandardReqItemsLambdaQueryWrapper.eq(SdlStandardReqItems::getStandardId, standard.getId());
			List<SdlStandardReqItems> sdlStandardReqItemsList = sdlStandardReqItemsService.list(sdlStandardReqItemsLambdaQueryWrapper);
			for (SdlStandardReqItems sdlStandardReqItems : sdlStandardReqItemsList) {
				String id = sdlStandardReqItems.getId();
				String uuid = CommonUtil.uuid();
				sdlStandardReqItems.setStandardId(sdlCodingStandard.getId());
				sdlStandardReqItems.setStandardName(sdlCodingStandard.getStandardName());
				sdlStandardReqItems.setCreateBy(null);
				sdlStandardReqItems.setCreateTime(null);
				sdlStandardReqItems.setUpdateBy(null);
				sdlStandardReqItems.setUpdateTime(null);
				sdlStandardReqItems.setSysOrgCode(null);
				sdlStandardReqItems.setId(uuid);
				LambdaQueryWrapper<SdlScaScanItem> sdlScaScanItemLambdaQueryWrapper = new LambdaQueryWrapper<>();
				sdlScaScanItemLambdaQueryWrapper.eq(SdlScaScanItem::getStandardItemId, id);
				List<SdlScaScanItem> sdlScaScanItems = sdlScaScanItemService.list(sdlScaScanItemLambdaQueryWrapper);
				for (SdlScaScanItem sdlScaScanItem : sdlScaScanItems) {
					sdlScaScanItem.setCreateBy(null);
					sdlScaScanItem.setCreateTime(null);
					sdlScaScanItem.setUpdateBy(null);
					sdlScaScanItem.setUpdateTime(null);
					sdlScaScanItem.setSysOrgCode(null);
					sdlScaScanItem.setId(null);
					sdlScaScanItem.setStandardItemId(uuid);
					sdlScaScanItem.setStandardItemName(sdlStandardReqItems.getStandardName());
				}
				sdlScaScanItemList.addAll(sdlScaScanItems);
			}
			sdlStandardReqItemsService.saveBatch(sdlStandardReqItemsList);
			sdlScaScanItemService.saveBatch(sdlScaScanItemList);
		}
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param sdlCodingStandard
	 * @return
	 */
	@AutoLog(value = "安全编码合规要求-编辑")
	@Operation(summary = "安全编码合规要求-编辑", description = "安全编码合规要求-编辑")
	@PutMapping(value = "/edit")
	@RequiresPermissions(value="scap:sdl:coding:standard:edit")
	public Result<?> edit(@RequestBody SdlCodingStandard sdlCodingStandard) {
		SdlCodingStandard standard = sdlCodingStandardService.getById(sdlCodingStandard.getId());
		if (Objects.isNull(standard)) {
			return Result.error("数据不存在");
		}
		if (!Boolean.TRUE.equals(standard.getEditable())) {
			// 如果是内置模板，设置默认操作时不拦截
			if (standard.getActiveStatus().equals(sdlCodingStandard.getActiveStatus())) {
				return Result.error("内置编码合规要求不允许修改");
			}
		}
		//如果要激活某个条目，则先屏蔽其他的条目
		if(sdlCodingStandard.getActiveStatus() !=null && sdlCodingStandard.getActiveStatus()==1){
			QueryWrapper<SdlCodingStandard> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("active_status", 1);
			List<SdlCodingStandard> sdlSiteManagerList = sdlCodingStandardService.list(queryWrapper);
			for (SdlCodingStandard siteMsg:sdlSiteManagerList) {
				siteMsg.setActiveStatus(0);
				sdlCodingStandardService.updateById(siteMsg);
			}
		}
		sdlCodingStandardService.updateById(sdlCodingStandard);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "安全编码合规要求-通过id删除")
	@Operation(summary = "安全编码合规要求-通过id删除", description = "安全编码合规要求-通过id删除")
	@DeleteMapping(value = "/delete")
	@RequiresPermissions(value="scap:sdl:coding:standard:delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		SdlCodingStandard standard = sdlCodingStandardService.getById(id);
		if (Objects.isNull(standard)) {
			return Result.error("数据不存在");
		}
		if (!Boolean.TRUE.equals(standard.getEditable())) {
			return Result.error("内置编码合规要求不允许删除");
		}
		sdlCodingStandardService.removeById(id);
		LambdaQueryWrapper<SdlStandardReqItems> sdlStandardReqItemsLambdaQueryWrapper = new LambdaQueryWrapper<>();
		sdlStandardReqItemsLambdaQueryWrapper.eq(SdlStandardReqItems::getStandardId, id);
		List<SdlStandardReqItems> sdlStandardReqItemsList = sdlStandardReqItemsService.list(sdlStandardReqItemsLambdaQueryWrapper);
		if (sdlStandardReqItemsList != null && !sdlStandardReqItemsList.isEmpty()){
			sdlStandardReqItemsService.delBatchMain(sdlStandardReqItemsList.stream().map(SdlStandardReqItems::getId).collect(Collectors.toList()));
		}
		//获取对标默认
		SdlCodingStandard sdlCodingStandard = sdlCodingStandardService.getOne(
				new LambdaQueryWrapper<SdlCodingStandard>().eq(SdlCodingStandard::getActiveStatus, 1).last("limit 1")
		);
		if (sdlCodingStandard != null) {
			//修改项目对标
			LambdaQueryWrapper<ScaProject> scaProjectLambdaQueryWrapper = new LambdaQueryWrapper<>();
			List<ScaProject> scaProjects = scaProjectService.list(scaProjectLambdaQueryWrapper.select(ScaProject::getProjectid).clone().eq(ScaProject::getStandardTemplate, id));
			if (scaProjects != null && !scaProjects.isEmpty()) {
				List<String> projectIds = scaProjects.stream().map(ScaProject::getProjectid).collect(Collectors.toList());
				Lists.partition(projectIds, CommonConstant.SQL_IN_BATCH_SIZE).forEach(projectIdsList -> {
					scaProjectService.update(new ScaProject().setStandardTemplate(sdlCodingStandard.getId()),
							scaProjectLambdaQueryWrapper.clone().in(ScaProject::getProjectid, projectIdsList));
				});
			}
			//修改任务对标
			LambdaQueryWrapper<ScaProjectVersion> scaProjectVersionLambdaQueryWrapper = new LambdaQueryWrapper<>();
			List<ScaProjectVersion> scaProjectVersions = scaProjectVersionService.list(scaProjectVersionLambdaQueryWrapper.select(ScaProjectVersion::getProjectversionid).clone().eq(ScaProjectVersion::getStandardTemplate, id));
			if (scaProjectVersions != null && !scaProjectVersions.isEmpty()){
				List<Integer> projectVersionIds = scaProjectVersions.stream().map(ScaProjectVersion::getProjectversionid).collect(Collectors.toList());
				Lists.partition(projectVersionIds, CommonConstant.SQL_IN_BATCH_SIZE).forEach(projectVersionIdsList -> {
					scaProjectVersionService.update(new ScaProjectVersion().setStandardTemplate(sdlCodingStandard.getId()),
							scaProjectVersionLambdaQueryWrapper.clone().in(ScaProjectVersion::getProjectversionid,
									projectVersionIdsList));
				});
			}
		}
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param idsParam
	 * @return
	 */
	@AutoLog(value = "安全编码合规要求-批量删除")
	@Operation(summary = "安全编码合规要求-批量删除", description = "安全编码合规要求-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	@RequiresPermissions(value="scap:sdl:coding:standard:delete")
	public Result<?> deleteBatch(@RequestBody @Validated IdsParam idsParam) {
		List<String> idList = Arrays.asList(idsParam.getIds().split(","));

		List<SdlCodingStandard> sdlCodingStandards = sdlCodingStandardService.listByIds(idList);
		for (SdlCodingStandard standard : sdlCodingStandards) {
			if (!Boolean.TRUE.equals(standard.getEditable())) {
				return Result.error("内置编码合规要求不允许删除");
			}
		}
		this.sdlCodingStandardService.removeByIds(idList);
		LambdaQueryWrapper<SdlStandardReqItems> sdlStandardReqItemsLambdaQueryWrapper = new LambdaQueryWrapper<>();
		sdlStandardReqItemsLambdaQueryWrapper.in(SdlStandardReqItems::getStandardId, idList);
		List<SdlStandardReqItems> sdlStandardReqItemsList = sdlStandardReqItemsService.list(sdlStandardReqItemsLambdaQueryWrapper);
		if (sdlStandardReqItemsList != null && !sdlStandardReqItemsList.isEmpty()){
			sdlStandardReqItemsService.delBatchMain(sdlStandardReqItemsList.stream().map(SdlStandardReqItems::getId).collect(Collectors.toList()));
		}
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
//	@AutoLog(value = "安全编码合规要求-通过id查询")
	@Operation(summary = "安全编码合规要求-通过id查询", description = "安全编码合规要求-通过id查询")
	@GetMapping(value = "/queryById")
	@RequiresPermissions(value="scap:sdl:coding:standard:list")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		SdlCodingStandard sdlCodingStandard = sdlCodingStandardService.getById(id);
		if(sdlCodingStandard==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sdlCodingStandard);
	}

    /**
    * 导出excel
    *
    * @param jsonObject
    */
    @PostMapping(value = "/exportXls")
	@RequiresPermissions(value="scap:sdl:coding:standard:export")
    public ModelAndView exportXls(@RequestBody JSONObject jsonObject) {
        return super.exportXls(jsonObject, SdlCodingStandard.class, "安全编码合规要求");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PostMapping(value = "/importExcel")
	@RequiresPermissions(value="scap:sdl:coding:standard:import")
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SdlCodingStandard.class);
    }

}
