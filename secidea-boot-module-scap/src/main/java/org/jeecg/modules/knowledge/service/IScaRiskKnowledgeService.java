package org.jeecg.modules.knowledge.service;

import org.jeecg.modules.knowledge.entity.ScaRiskKnowledge;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.knowledge.utils.dto.riskknowledge.ScaRiskKnowledgeReq;
import org.jeecg.modules.knowledge.utils.dto.riskknowledge.ScaRiskKnowledgeResp;

import java.util.List;
import java.util.Set;

/**
 * @Description: 代码缺陷知识库
 * @Author: jeecg-boot
 * @Date:   2021-08-28
 * @Version: V1.0
 */
public interface IScaRiskKnowledgeService extends IService<ScaRiskKnowledge> {

    /**
     * 保存代码缺陷知识库，并保存关联的缺陷模版关联项sdl_sca_scan_item
     *
     * @param scaRiskKnowledgeReq 用户提交的知识库信息
     */
    void saveRiskKnowledge(ScaRiskKnowledgeReq scaRiskKnowledgeReq);

    /**
     * 更新代码缺陷知识库，并更新关联的缺陷模版关联项sdl_sca_scan_item
     *
     * @param scaRiskKnowledgeReq 用户提交的知识库信息
     */
    void updateRiskKnowledge(ScaRiskKnowledgeReq scaRiskKnowledgeReq);

    /**
     * 获取缺陷知识库项，填充关联的缺陷模板项信息
     *
     * @param id              缺陷知识库项Id
     * @param fillAllReqItems 是否填充该模板的所有安全项
     * @return scaRiskKnowledge响应信息
     */
    ScaRiskKnowledgeResp getRiskKnowledgeById(String id, Boolean fillAllReqItems);

    /**
     * 删除缺陷知识项，同时删除关联的缺陷模板项信息
     *
     * @param id 待删除的缺陷知识项Id
     */
    void removeRiskKnowledgeById(String id);

    /**
     * 批量删除缺陷知识项，同时删除关联的缺陷模板项信息
     *
     * @param idList 待删除的缺陷知识项Id集合
     */
    void removeRiskKnowledgeByIds(List<String> idList);

    /**
     * 将系统内置知识库记录重置为用户未修改前的信息
     * @param idList 需要重置的知识库记录ID集合
     */
    void resetRiskKnowledge(Set<String> idList);

    /**
     * 将系统全部内置知识库记录重置为未修改前的信息
     */
    void resetAllRiskKnowledge();
}
