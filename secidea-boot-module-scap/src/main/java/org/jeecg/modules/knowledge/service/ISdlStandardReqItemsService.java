package org.jeecg.modules.knowledge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.knowledge.entity.SdlStandardReqItems;
import org.jeecg.modules.sectest.scap.utils.report.SdlStandardReq;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 安全规范要求项
 * @Author: jeecg-boot
 * @Date:   2021-08-01
 * @Version: V1.0
 */
public interface ISdlStandardReqItemsService extends IService<SdlStandardReqItems> {

	/**
	 * 删除一对多
	 */
	public void delMain (String id);

	/**
	 * 批量删除一对多
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

	public List<SdlStandardReq> findSdlStandardReqListByStandardId (String standardId);

	public List<SdlStandardReqItems> listByStandardId(String standardId);


}
