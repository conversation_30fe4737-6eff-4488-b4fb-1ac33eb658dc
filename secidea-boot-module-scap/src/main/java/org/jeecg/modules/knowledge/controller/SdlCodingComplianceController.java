package org.jeecg.modules.knowledge.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.base.entity.IdsParam;
import org.jeecg.modules.knowledge.entity.SdlCodingCompliance;
import org.jeecg.modules.knowledge.service.ISdlCodingComplianceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: 安全编码合规
 * @Author: jeecg-boot
 * @Date:   2020-11-14
 * @Version: V1.0
 */
@Tag(name = "安全编码合规")
@RestController
@RequestMapping("/sdlCodingCompliance")
@Slf4j
public class SdlCodingComplianceController extends JeecgController<SdlCodingCompliance, ISdlCodingComplianceService> {
	@Autowired
	private ISdlCodingComplianceService sdlCodingComplianceService;

	/**
	 * 分页列表查询
	 *
	 * @param sdlCodingCompliance
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
//	@AutoLog(value = "安全编码合规-分页列表查询")
	@Operation(summary = "安全编码合规-分页列表查询", description = "安全编码合规-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(SdlCodingCompliance sdlCodingCompliance,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<SdlCodingCompliance> queryWrapper = QueryGenerator.initQueryWrapper(sdlCodingCompliance, req.getParameterMap());
		Page<SdlCodingCompliance> page = new Page<SdlCodingCompliance>(pageNo, pageSize);
		IPage<SdlCodingCompliance> pageList = sdlCodingComplianceService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param sdlCodingCompliance
	 * @return
	 */
	@AutoLog(value = "安全编码合规-添加")
	@Operation(summary = "安全编码合规-添加", description = "安全编码合规-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody SdlCodingCompliance sdlCodingCompliance) {
		sdlCodingComplianceService.save(sdlCodingCompliance);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param sdlCodingCompliance
	 * @return
	 */
	@AutoLog(value = "安全编码合规-编辑")
	@Operation(summary = "安全编码合规-编辑", description = "安全编码合规-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody SdlCodingCompliance sdlCodingCompliance) {
		sdlCodingComplianceService.updateById(sdlCodingCompliance);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "安全编码合规-通过id删除")
	@Operation(summary = "安全编码合规-通过id删除", description = "安全编码合规-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		sdlCodingComplianceService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param idsParam
	 * @return
	 */
	@AutoLog(value = "安全编码合规-批量删除")
	@Operation(summary = "安全编码合规-批量删除", description = "安全编码合规-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestBody @Validated IdsParam idsParam) {
		this.sdlCodingComplianceService.removeByIds(Arrays.asList(idsParam.getIds().split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
//	@AutoLog(value = "安全编码合规-通过id查询")
	@Operation(summary = "安全编码合规-通过id查询", description = "安全编码合规-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		SdlCodingCompliance sdlCodingCompliance = sdlCodingComplianceService.getById(id);
		if(sdlCodingCompliance==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sdlCodingCompliance);
	}

    /**
    * 导出excel
    *
    * @param jsonObject
    */
    @PostMapping(value = "/exportXls")
    public ModelAndView exportXls(@RequestBody JSONObject jsonObject) {
        return super.exportXls(jsonObject, SdlCodingCompliance.class, "安全编码合规");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SdlCodingCompliance.class);
    }

}
