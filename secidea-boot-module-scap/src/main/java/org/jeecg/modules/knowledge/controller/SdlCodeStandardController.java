package org.jeecg.modules.knowledge.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.base.entity.IdsParam;
import org.jeecg.modules.knowledge.entity.SdlCodeStandard;
import org.jeecg.modules.knowledge.service.ISdlCodeStandardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

 /**
 * @Description: 安全编码对标
 * @Author: jeecg-boot
 * @Date:   2021-06-30
 * @Version: V1.0
 */
@Tag(name = "安全编码对标")
@RestController
@RequestMapping("/sdlCodeStandard")
@Slf4j
public class SdlCodeStandardController extends JeecgController<SdlCodeStandard, ISdlCodeStandardService> {
	@Autowired
	private ISdlCodeStandardService sdlCodeStandardService;

	/**
	 * 分页列表查询
	 *
	 * @param sdlCodeStandard
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
//	@AutoLog(value = "安全编码对标-分页列表查询")
	@Operation(summary = "安全编码对标-分页列表查询", description = "安全编码对标-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(SdlCodeStandard sdlCodeStandard,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<SdlCodeStandard> queryWrapper = QueryGenerator.initQueryWrapper(sdlCodeStandard, req.getParameterMap());
		Page<SdlCodeStandard> page = new Page<SdlCodeStandard>(pageNo, pageSize);
		IPage<SdlCodeStandard> pageList = sdlCodeStandardService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param sdlCodeStandard
	 * @return
	 */
	@AutoLog(value = "安全编码对标-添加")
	@Operation(summary = "安全编码对标-添加", description = "安全编码对标-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody SdlCodeStandard sdlCodeStandard) {
		sdlCodeStandardService.save(sdlCodeStandard);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param sdlCodeStandard
	 * @return
	 */
	@AutoLog(value = "安全编码对标-编辑")
	@Operation(summary = "安全编码对标-编辑", description = "安全编码对标-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody SdlCodeStandard sdlCodeStandard) {
		sdlCodeStandardService.updateById(sdlCodeStandard);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "安全编码对标-通过id删除")
	@Operation(summary = "安全编码对标-通过id删除", description = "安全编码对标-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		sdlCodeStandardService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param idsParam
	 * @return
	 */
	@AutoLog(value = "安全编码对标-批量删除")
	@Operation(summary = "安全编码对标-批量删除", description = "安全编码对标-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestBody @Validated IdsParam idsParam) {
		this.sdlCodeStandardService.removeByIds(Arrays.asList(idsParam.getIds().split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
//	@AutoLog(value = "安全编码对标-通过id查询")
	@Operation(summary = "安全编码对标-通过id查询", description = "安全编码对标-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		SdlCodeStandard sdlCodeStandard = sdlCodeStandardService.getById(id);
		if(sdlCodeStandard==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sdlCodeStandard);
	}

    /**
    * 导出excel
    *
    * @param jsonObject
    */
    @PostMapping(value = "/exportXls")
    public ModelAndView exportXls(@RequestBody JSONObject jsonObject) {
        return super.exportXls(jsonObject, SdlCodeStandard.class, "安全编码对标");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, SdlCodeStandard.class);
    }

}
