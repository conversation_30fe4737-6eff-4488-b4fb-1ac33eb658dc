package org.jeecg.modules.knowledge.service.impl;

import org.jeecg.modules.knowledge.entity.SdlCodeDefectLib;
import org.jeecg.modules.knowledge.mapper.SdlCodeDefectLibMapper;
import org.jeecg.modules.knowledge.service.ISdlCodeDefectLibService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 代码缺陷知识库
 * @Author: jeecg-boot
 * @Date:   2020-11-09
 * @Version: V1.0
 */
@Service
public class SdlCodeDefectLibServiceImpl extends ServiceImpl<SdlCodeDefectLibMapper, SdlCodeDefectLib> implements ISdlCodeDefectLibService {

}
