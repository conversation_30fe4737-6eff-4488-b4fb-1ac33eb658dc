package org.jeecg.modules.knowledge.service;

import org.jeecg.modules.knowledge.entity.SdlCodingStandard;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 安全编码合规要求
 * @Author: jeecg-boot
 * @Date:   2021-08-01
 * @Version: V1.0
 */
public interface ISdlCodingStandardService extends IService<SdlCodingStandard> {
    SdlCodingStandard getSdlCodingStandardNameOrDefault(String name, String id);

    String getSdlCodingStandardNameOrDefault(String name);
}
