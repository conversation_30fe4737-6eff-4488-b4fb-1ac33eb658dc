package org.jeecg.modules.knowledge.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.base.entity.IdsParam;
import org.jeecg.modules.knowledge.entity.SdlCodeDefectLib;
import org.jeecg.modules.knowledge.entity.SdlCodeDefectLibSimple;
import org.jeecg.modules.knowledge.service.ISdlCodeDefectLibService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

 /**
 * @Description: 代码缺陷知识库
 * @Author: jeecg-boot
 * @Date:   2020-11-09
 * @Version: V1.0
 */
@Tag(name = "代码缺陷知识库")
@RestController
@RequestMapping("/sdlCodeDefectLib")
@Slf4j
public class SdlCodeDefectLibController extends JeecgController<SdlCodeDefectLib, ISdlCodeDefectLibService> {
	@Autowired
	private ISdlCodeDefectLibService sdlCodeDefectLibService;

	/**
	 * 分页列表查询
	 *
	 * @param sdlCodeDefectLib
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
//	@AutoLog(value = "代码缺陷知识库-分页列表查询")
	@Operation(summary = "代码缺陷知识库-分页列表查询", description = "代码缺陷知识库-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(SdlCodeDefectLib sdlCodeDefectLib,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<SdlCodeDefectLib> queryWrapper = QueryGenerator.initQueryWrapper(sdlCodeDefectLib, req.getParameterMap());
		Page<SdlCodeDefectLib> page = new Page<SdlCodeDefectLib>(pageNo, pageSize);
		IPage<SdlCodeDefectLib> pageList = sdlCodeDefectLibService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 * 查询全部数据 根据issueTypeZh 去重
	 *
	 * @param sdlCodeDefectLib
	 * @param req
	 * @return
	 */
//	@AutoLog(value = "代码缺陷知识库-查询全部")
	@Operation(summary = "代码缺陷知识库-查询全部", description = "代码缺陷知识库-查询全部")
//	@GetMapping(value = "/listAll")
	public Result<?> queryListAll(SdlCodeDefectLib sdlCodeDefectLib, HttpServletRequest req) {
		QueryWrapper<SdlCodeDefectLib> queryWrapper = QueryGenerator.initQueryWrapper(sdlCodeDefectLib, req.getParameterMap());
		List<SdlCodeDefectLib> pageList = sdlCodeDefectLibService.list(queryWrapper);

		HashSet<String> set = new HashSet<>();
		List<SdlCodeDefectLibSimple> pageListNew = new ArrayList<>();
		for (SdlCodeDefectLib sdlCodeDefectLibChild : pageList){
			if (set.contains(sdlCodeDefectLibChild.getIssueTypeZh())){
				continue;
			}
			set.add(sdlCodeDefectLibChild.getIssueTypeZh());
			pageListNew.add(new SdlCodeDefectLibSimple()
					.setId(sdlCodeDefectLibChild.getId())
					.setIssueTypeZh(sdlCodeDefectLibChild.getIssueTypeZh()));
		}
		return Result.OK(pageListNew);
	}

	/**
	 *   添加
	 *
	 * @param sdlCodeDefectLib
	 * @return
	 */
	@AutoLog(value = "代码缺陷知识库-添加")
	@Operation(summary = "代码缺陷知识库-添加", description = "代码缺陷知识库-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody SdlCodeDefectLib sdlCodeDefectLib) {
		sdlCodeDefectLibService.save(sdlCodeDefectLib);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param sdlCodeDefectLib
	 * @return
	 */
	@AutoLog(value = "代码缺陷知识库-编辑")
	@Operation(summary = "代码缺陷知识库-编辑", description = "代码缺陷知识库-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody SdlCodeDefectLib sdlCodeDefectLib) {
		sdlCodeDefectLibService.updateById(sdlCodeDefectLib);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "代码缺陷知识库-通过id删除")
	@Operation(summary = "代码缺陷知识库-通过id删除", description = "代码缺陷知识库-通过id删除")
//	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		sdlCodeDefectLibService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param idsParam
	 * @return
	 */
	@AutoLog(value = "代码缺陷知识库-批量删除")
	@Operation(summary = "代码缺陷知识库-批量删除", description = "代码缺陷知识库-批量删除")
//	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestBody @Validated IdsParam idsParam) {
		this.sdlCodeDefectLibService.removeByIds(Arrays.asList(idsParam.getIds().split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
//	@AutoLog(value = "代码缺陷知识库-通过id查询")
	@Operation(summary = "代码缺陷知识库-通过id查询", description = "代码缺陷知识库-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		SdlCodeDefectLib sdlCodeDefectLib = sdlCodeDefectLibService.getById(id);
		if(sdlCodeDefectLib==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(sdlCodeDefectLib);
	}

}
