package org.jeecg.modules.knowledge.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.FileUtils;
import org.jeecg.common.util.StringUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.base.entity.IdsParam;
import org.jeecg.modules.knowledge.entity.*;
import org.jeecg.modules.knowledge.service.*;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
* @Description: 安全规范要求项
* @Author: jeecg-boot
* @Date:   2021-08-01
* @Version: V1.0
*/
@Tag(name = "安全规范要求项")
@RestController
@RequestMapping("/sdlStandardReqItems")
@Slf4j
public class SdlStandardReqItemsController extends JeecgController<SdlStandardReqItems, ISdlStandardReqItemsService> {

   @Autowired
   private ISdlStandardReqItemsService sdlStandardReqItemsService;

   @Autowired
   private ISdlScaScanItemService sdlScaScanItemService;

    @Autowired
    private IScaRiskKnowledgeService scaRiskKnowledgeService;

    @Autowired
    private ISdlCodeStandardService sdlCodeStandardService;

    @Autowired
    private ISdlCodingStandardService sdlCodingStandardService;


   /*---------------------------------主表处理-begin-------------------------------------*/

   /**
    * 分页列表查询
    * @param sdlStandardReqItems
    * @param pageNo
    * @param pageSize
    * @param req
    * @return
    */
//   @AutoLog(value = "安全规范要求项-分页列表查询")
   @Operation(summary = "安全规范要求项-分页列表查询", description = "安全规范要求项-分页列表查询")
   @GetMapping(value = "/list")
   public Result<?> queryPageList(SdlStandardReqItems sdlStandardReqItems,
                                  @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                  HttpServletRequest req) {
       QueryWrapper<SdlStandardReqItems> queryWrapper = QueryGenerator.initQueryWrapper(sdlStandardReqItems, req.getParameterMap());
       Page<SdlStandardReqItems> page = new Page<SdlStandardReqItems>(pageNo, pageSize);
       IPage<SdlStandardReqItems> pageList = sdlStandardReqItemsService.page(page, queryWrapper);
       for (SdlStandardReqItems items : pageList.getRecords()) {
           if (items.getStandardId() != null) {
               SdlCodingStandard byId = sdlCodingStandardService.getById(items.getStandardId());
               if (byId != null) {
                   items.setStandardName(byId.getStandardName());
               }
           }
       }
       return Result.OK(pageList);
   }

    /**
     * 获取缺陷模版下所有的sdlStandardItems
     *
     * @param standardId 检测缺陷模版ID
     * @return sdlStandardItems
     */
    @GetMapping("/listAllByStandardId")
    public Result<?> queryAll(@RequestParam(name = "standardId") String standardId) {
        SdlCodingStandard standard = sdlCodingStandardService.getById(standardId);
        if (Objects.isNull(standard)) {
            return Result.error("检测缺陷模版不存在");
        }
        LambdaQueryWrapper<SdlStandardReqItems> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SdlStandardReqItems::getStandardId, standardId);
        List<SdlStandardReqItems> sdlStandardReqItems = sdlStandardReqItemsService.list(wrapper);
        sdlStandardReqItems.forEach(item -> item.setStandardName(standard.getStandardName()));
        return Result.ok(sdlStandardReqItems);
    }

   /**
    *   添加
    * @param sdlStandardReqItems
    * @return
    */
   @AutoLog(value = "安全规范要求项-添加")
   @Operation(summary = "安全规范要求项-添加", description = "安全规范要求项-添加")
   @PostMapping(value = "/add")
   @RequiresPermissions(value="scap:sdl:coding:standard:req:items:add")
   public Result<?> add(@RequestBody SdlStandardReqItems sdlStandardReqItems) {
       assertStandardCanEdit(sdlStandardReqItems.getStandardId());
       if ( null == sdlStandardReqItems.getReqItemOrder() ){
           sdlStandardReqItems.setReqItemOrder(1);
       }
       sdlStandardReqItemsService.save(sdlStandardReqItems);
       return Result.OK("添加成功！");
   }

   /**
    *  编辑
    * @param sdlStandardReqItems
    * @return
    */
   @AutoLog(value = "安全规范要求项-编辑")
   @Operation(summary = "安全规范要求项-编辑", description = "安全规范要求项-编辑")
   @PutMapping(value = "/edit")
   @RequiresPermissions(value="scap:sdl:coding:standard:req:items:edit")
   public Result<?> edit(@RequestBody SdlStandardReqItems sdlStandardReqItems) {
       assertStandardCanEdit(sdlStandardReqItems.getStandardId());
       sdlStandardReqItemsService.updateById(sdlStandardReqItems);
       return Result.OK("编辑成功!");
   }

   /**
    * 通过id删除
    * @param id
    * @return
    */
   @AutoLog(value = "安全规范要求项-通过id删除")
   @Operation(summary = "安全规范要求项-通过id删除", description = "安全规范要求项-通过id删除")
   @DeleteMapping(value = "/delete")
   @RequiresPermissions(value="scap:sdl:coding:standard:req:items:delete")
   public Result<?> delete(@RequestParam(name="id",required=true) String id) {
       assertCanEditByStandardItemIds(Lists.newArrayList(id));
       sdlStandardReqItemsService.delMain(id);
       return Result.OK("删除成功!");
   }

   /**
    * 批量删除
    * @param idsParam
    * @return
    */
   @AutoLog(value = "安全规范要求项-批量删除")
   @Operation(summary = "安全规范要求项-批量删除", description = "安全规范要求项-批量删除")
   @DeleteMapping(value = "/deleteBatch")
   @RequiresPermissions(value="scap:sdl:coding:standard:req:items:delete")
   public Result<?> deleteBatch(@RequestBody @Validated IdsParam idsParam) {
       assertCanEditByStandardItemIds(Arrays.asList(idsParam.getIds().split(",")));
       this.sdlStandardReqItemsService.delBatchMain(Arrays.asList(idsParam.getIds().split(",")));
       return Result.OK("批量删除成功!");
   }

   /**
    * 导出
    * @return
    */
   @PostMapping(value = "/exportXls")
   @RequiresPermissions(value="scap:sdl:coding:standard:req:items:export")
   public ModelAndView exportXls(@RequestBody JSONObject jsonObject) {
       return super.exportXls(jsonObject, SdlStandardReqItems.class, "安全规范要求项");
   }

   /**
    * 导入
    * @return
    */
   @PostMapping(value = "/importExcel")
   @RequiresPermissions(value="scap:sdl:coding:standard:req:items:import")
   public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
       return super.importExcel(request, response, SdlStandardReqItems.class);
   }
   /*---------------------------------主表处理-end-------------------------------------*/


   /*--------------------------------子表处理-安全编码检测项-begin----------------------------------------------*/
   /**
    * 通过主表ID查询
    * @return
    */
//   @AutoLog(value = "安全编码检测项-通过主表ID查询")
   @Operation(summary = "安全编码检测项-通过主表ID查询", description = "安全编码检测项-通过主表ID查询")
   @GetMapping(value = "/listSdlScaScanItemByMainId")
   public Result<?> listSdlScaScanItemByMainId(SdlScaScanItem sdlScaScanItem,
                                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                   HttpServletRequest req) {
       QueryWrapper<SdlScaScanItem> queryWrapper = QueryGenerator.initQueryWrapper(sdlScaScanItem, req.getParameterMap());
       Page<SdlScaScanItem> page = new Page<SdlScaScanItem>(pageNo, pageSize);
       IPage<SdlScaScanItem> pageList = sdlScaScanItemService.page(page, queryWrapper);
       for (SdlScaScanItem items : pageList.getRecords()) {
           if (items.getStandardItemId() != null) {
               SdlStandardReqItems byId = sdlStandardReqItemsService.getById(items.getStandardItemId());
               if (byId != null) {
                   items.setStandardItemName(byId.getRequirementItem());
               }
           }
       }
       return Result.OK(pageList);
   }

    /**
     * 添加
     * @param sdlScaScanItem
     * @return
     */
    @AutoLog(value = "安全编码检测项-添加")
    @Operation(summary = "安全编码检测项-添加", description = "安全编码检测项-添加")
    @PostMapping(value = "/addSdlScaScanItem")
    @RequiresPermissions(value="scap:sdl:sca:scan:items:add")
    public Result<?> addSdlScaScanItem(@RequestBody SdlScaScanItem sdlScaScanItem) {
        assertCanEditByStandardItemIds(Lists.newArrayList(sdlScaScanItem.getStandardItemId()));
        List<String> issueTypeIdList = Arrays.asList(sdlScaScanItem.getIssueTypeId().split(","));
        List<ScaRiskKnowledge> scaRiskKnowledgeList = scaRiskKnowledgeService.listByIds(issueTypeIdList);
        for(ScaRiskKnowledge scaRiskKnowledge :  scaRiskKnowledgeList){
            sdlScaScanItem.setIssueTypeId(scaRiskKnowledge.getId());
            sdlScaScanItem.setIssueTypeZh(scaRiskKnowledge.getIssuetypezh());
            sdlScaScanItem.setIssueTypeEn(scaRiskKnowledge.getIssuetypeen());
            sdlScaScanItem.setCategory(scaRiskKnowledge.getCategory());
            sdlScaScanItem.setIssueAbstract(scaRiskKnowledge.getIssueabstract());
            sdlScaScanItem.setLanguageLower(scaRiskKnowledge.getLanguagelower());
            sdlScaScanItem.setLanguage(scaRiskKnowledge.getLanguage());
            sdlScaScanItem.setSeverityType(String.valueOf(scaRiskKnowledge.getSeveritytype()));
            Integer count = sdlScaScanItemService.countStandardKnowledgeId(sdlScaScanItem.getStandardId(), sdlScaScanItem.getIssueTypeId());
            if (count == 0) {
                SdlStandardReqItems sdlStandardReqItems = sdlStandardReqItemsService.getById(sdlScaScanItem.getStandardItemId());
                sdlScaScanItem.setStandardItemName(sdlStandardReqItems.getRequirementItem());
                sdlScaScanItemService.save(sdlScaScanItem);
                sdlScaScanItem.setId("");
            }
        }
        return Result.OK("添加成功！");
    }

   /**
    * 编辑
    * @param sdlScaScanItem
    * @return
    */
   @AutoLog(value = "安全编码检测项-编辑")
   @Operation(summary = "安全编码检测项-编辑", description = "安全编码检测项-编辑")
   @PutMapping(value = "/editSdlScaScanItem")
   @RequiresPermissions(value="scap:sdl:sca:scan:items:edit")
   public Result<?> editSdlScaScanItem(@RequestBody SdlScaScanItem sdlScaScanItem) {
       assertCanEditByStandardItemIds(Lists.newArrayList(sdlScaScanItem.getStandardItemId()));
       sdlScaScanItemService.updateById(sdlScaScanItem);
       return Result.OK("编辑成功!");
   }


//	 /**
//	  * 编辑
//	  * @param sdlScaScanItem
//	  * @return
//	  */
//	 @AutoLog(value = "安全编码检测项-编辑")
//	 @Operation(summary = "安全编码检测项-编辑", description = "安全编码检测项-编辑")
//	 @PutMapping(value = "/editSdlScaScanItem")
//	 public Result<?> editSdlScaScanItem(@RequestBody SdlScaScanItem sdlScaScanItem) {
//		 sdlScaScanItem.setId("");
//	 	List<SdlCodeStandard> sdlCodeStandardList = sdlCodeStandardService.list();
//	 	List<SdlStandardReqItems>  SdlStandardReqItemsList = sdlStandardReqItemsService.list();
//	 	for(SdlCodeStandard sdlCodeStandard : sdlCodeStandardList ){
//			for(SdlStandardReqItems sdlStandardReqItems : SdlStandardReqItemsList){
//				if(sdlCodeStandard.getSecReqItem().equals(sdlStandardReqItems.getRequirementItem())){
//					sdlScaScanItem.setSecReqItem(sdlStandardReqItems.getRequirementItem());
//					sdlScaScanItem.setStandardItemId(sdlStandardReqItems.getId());
//					sdlScaScanItem.setSecConType(sdlStandardReqItems.getDescription());
//					QueryWrapper<SdlCodeDefectLib> queryWrapper = new QueryWrapper<SdlCodeDefectLib>();
//					queryWrapper.eq("issue_type_zh", sdlCodeStandard.getCodeTestCase());
//					List<SdlCodeDefectLib> sdlCodeDefectLibList = sdlCodeDefectLibService.list(queryWrapper);
//					if(sdlCodeDefectLibList.size()==0){
//						System.out.println(sdlCodeStandard.getCodeTestCase());
//					}
//					if(sdlCodeDefectLibList.size()>0){
//						SdlCodeDefectLib sdlCodeDefectLib = sdlCodeDefectLibList.get(0);
//						sdlScaScanItem.setIssueTypeId(sdlCodeDefectLib.getId());
//						sdlScaScanItem.setIssueTypeZh(sdlCodeDefectLib.getIssueTypeZh());
//						sdlScaScanItem.setIssueTypeEn(sdlCodeDefectLib.getIssueTypeEn());
//						sdlScaScanItem.setCategory(sdlCodeDefectLib.getCategory());
//						sdlScaScanItem.setIssueAbstract(sdlCodeDefectLib.getIssueAbstract());
//						sdlScaScanItem.setLanguageLower(sdlCodeDefectLib.getLanguageLower());
//						sdlScaScanItem.setSeverityType(String.valueOf(sdlCodeDefectLib.getSeverityType()));
//						QueryWrapper<SdlScaScanItem> queryWrapper1 = new QueryWrapper<SdlScaScanItem>();
//						queryWrapper1.eq("issue_type_id", sdlScaScanItem.getIssueTypeId());
//						queryWrapper1.eq("standard_item_id", sdlScaScanItem.getStandardItemId());
//						if(sdlScaScanItemService.count(queryWrapper1) == 0){
//							sdlScaScanItemService.save(sdlScaScanItem);
//							sdlScaScanItem.setId("");
//						}
//					}
//				}
//			}
//		}
//		 return Result.OK("编辑成功!");
//	 }

   /**
    * 通过id删除
    * @param id
    * @return
    */
   @AutoLog(value = "安全编码检测项-通过id删除")
   @Operation(summary = "安全编码检测项-通过id删除", description = "安全编码检测项-通过id删除")
   @DeleteMapping(value = "/deleteSdlScaScanItem")
   @RequiresPermissions(value="scap:sdl:sca:scan:items:delete")
   public Result<?> deleteSdlScaScanItem(@RequestParam(name="id",required=true) String id) {
       SdlScaScanItem scaScanItem = sdlScaScanItemService.getById(id);
       if (Objects.isNull(scaScanItem)) {
           throw new JeecgBootException("安全编码检测项不存在");
       }
       assertCanEditByStandardItemIds(Lists.newArrayList(scaScanItem.getStandardItemId()));
       sdlScaScanItemService.removeById(id);
       return Result.OK("删除成功!");
   }

   /**
    * 批量删除
    * @param idsParam
    * @return
    */
   @AutoLog(value = "安全编码检测项-批量删除")
   @Operation(summary = "安全编码检测项-批量删除", description = "安全编码检测项-批量删除")
   @DeleteMapping(value = "/deleteBatchSdlScaScanItem")
   @RequiresPermissions(value="scap:sdl:sca:scan:items:delete")
   public Result<?> deleteBatchSdlScaScanItem(@RequestBody @Validated IdsParam idsParam) {
       List<String> scanItemIds = Arrays.asList(idsParam.getIds().split(","));
       if (CollectionUtils.isEmpty(scanItemIds)) {
           return Result.error("请选择需要删除的安全编码检测项！");
       }
       List<SdlScaScanItem> sdlScaScanItems = sdlScaScanItemService.listByIds(scanItemIds);
       Set<String> standardItemIds = sdlScaScanItems.stream().map(SdlScaScanItem::getStandardItemId).collect(Collectors.toSet());
       assertCanEditByStandardItemIds(standardItemIds);
       this.sdlScaScanItemService.removeByIds(scanItemIds);
       return Result.OK("批量删除成功!");
   }

   /**
    * 导出
    * @return
    */
   @PostMapping(value = "/exportSdlScaScanItem")
   @RequiresPermissions(value="scap:sdl:sca:scan:items:export")
   public ModelAndView exportSdlScaScanItem(@RequestBody JSONObject jsonObject) {
        Map<String, String[]> requestParamMap = QueryGenerator.getRequestParamMapForJsonObject(jsonObject);
        SdlScaScanItem sdlScaScanItem = QueryGenerator.getBaseEntity(jsonObject, SdlScaScanItem.class);
        // Step.1 组装查询条件
        QueryWrapper<SdlScaScanItem> queryWrapper = QueryGenerator.initQueryWrapper(sdlScaScanItem, requestParamMap);
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // Step.2 获取导出数据
        List<SdlScaScanItem> pageList = sdlScaScanItemService.list(queryWrapper);
        List<SdlScaScanItem> exportList = null;

        // 过滤选中数据
        String selections = jsonObject.getString("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            exportList = pageList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
        } else {
            exportList = pageList;
        }

        // Step.3 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "安全编码检测项"); //此处设置的filename无效 ,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.CLASS, SdlScaScanItem.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams(null, "安全编码检测项", ExcelType.XSSF));
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        return mv;
   }

   /**
    * 导入
    * @return
    */
   @RequestMapping(value = "/importSdlScaScanItem/{mainId}")
   @RequiresPermissions(value="scap:sdl:sca:scan:items:import")
   public Result<?> importSdlScaScanItem(HttpServletRequest request, HttpServletResponse response, @PathVariable("mainId") String mainId) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<SdlScaScanItem> list = ExcelImportUtil.importExcel(file.getInputStream(), SdlScaScanItem.class, params);
                for (SdlScaScanItem temp : list) {
                   temp.setStandardItemId(mainId);
                }
                long start = System.currentTimeMillis();
                sdlScaScanItemService.saveBatch(list);
                log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
                return Result.OK("文件导入成功！数据行数：" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.error("文件导入失败！");
   }

   /*--------------------------------子表处理-安全编码检测项-end----------------------------------------------*/


    @RequiresRoles(value = {"admin","dev_admin"}, logical = Logical.OR)
    @GetMapping("autoUpdateSdlScaScanItem")
    public Result<?> autoUpdateSdlScaScanItem(){

        // 1. 先找到sdl_sca_scan_item 最后一个Id
//        SdlScaScanItem sdlScaScanItem = sdlScaScanItemService.getOne(new LambdaQueryWrapper<SdlScaScanItem>().orderByDesc(SdlScaScanItem::getId).last("limit 1"));
//        Long startId = Long.valueOf(sdlScaScanItem.getId()) + 1;
        Long startId = Long.valueOf("1466126452616794113");

        // 所有sdlStandReqItems 集合，并转为map key value 形式储存
        List<SdlStandardReqItems> sdlStandardReqItemsList = sdlStandardReqItemsService.list(new LambdaQueryWrapper<SdlStandardReqItems>().likeRight(SdlStandardReqItems::getRequirementItem, "CWE"));
        Map<String,String> sdlStandardReqItemsMap = new HashMap(sdlStandardReqItemsList.size());
        for (SdlStandardReqItems sdlStandardReqItems : sdlStandardReqItemsList) {
            sdlStandardReqItemsMap.put(sdlStandardReqItems.getRequirementItem(),sdlStandardReqItems.getId());
        }


        // 读取文件
//        String fileContent = "System Information Leak: XPath Error\tCWE ID 209\n" +
//                "Axis 2 Misconfiguration: Debug Information\tCWE ID 215\n";
        String fileContent = FileUtils.readFile(new File("D:\\testProgram\\secidea_scan\\cwe.txt"));
        String []fileLineContent = fileContent.split("\r\n");
        for (String fileLine : fileLineContent) {
            if ( !StringUtils.isBlank(fileLine) ){
                String[] fileLine2 = fileLine.split("\t");
                // 查找数据库是否存在该数据
                String issueTypeEn = fileLine2[0];
                String standardItemName = fileLine2[1];
                long count = sdlScaScanItemService.count(new LambdaQueryWrapper<SdlScaScanItem>().eq(SdlScaScanItem::getIssueTypeEn,issueTypeEn).eq(SdlScaScanItem::getStandardItemName,standardItemName));
                // 新增
                if ( count == 0 ){
                    boolean success = true;
                    // 先找到CWE编号的数据
                    if ( sdlStandardReqItemsMap.containsKey(standardItemName) ){
                        // 输出正确结果
                        // 制作需要插入的数据
                        SdlScaScanItem param = new SdlScaScanItem();
                        param/*.setId(String.valueOf(startId))*/
                                .setCreateBy("admin")
                                .setCreateTime(new Date())
                                .setSysOrgCode("A01")
                                .setStandardItemName(standardItemName)
                                .setStandardItemId(sdlStandardReqItemsMap.get(standardItemName));

                        // 根据 issueTypeEn 获取 sca_risk_knowledge 获取所需数据
                        List<ScaRiskKnowledge> scaRiskKnowledgeList = scaRiskKnowledgeService.list(new LambdaQueryWrapper<ScaRiskKnowledge>().eq(ScaRiskKnowledge::getIssuetypeen, issueTypeEn));
                        if ( scaRiskKnowledgeList.isEmpty() ){
                            // 输出错误结果
                            FileUtils.writeToFile("D:\\testProgram\\secidea_scan\\error1.txt",fileLine + "\r\n" ,true );
                            success = false;
                        } else {
                            // TODO 去重
                            List<String> beUseList = new ArrayList<>();

                            for (ScaRiskKnowledge scaRiskKnowledge : scaRiskKnowledgeList) {
                                param.setId(String.valueOf(startId))
                                        .setStandardItemId(sdlStandardReqItemsMap.get(standardItemName))
                                        .setIssueTypeZh(scaRiskKnowledge.getIssuetypezh())
                                        .setIssueTypeEn(scaRiskKnowledge.getIssuetypeen())
                                        .setCategory(scaRiskKnowledge.getCategory())
                                        .setSeverityType(scaRiskKnowledge.getCategory())
//                                        .setLanguageLower(scaRiskKnowledge.getLanguagelower())
                                        .setIsSuit(1)
                                        .setIssueAbstract(scaRiskKnowledge.getIssueabstract())
                                        .setIssueTypeId(scaRiskKnowledge.getId());
                                // 插入数据
                                String mapKey = param.getStandardItemId() + param.getIssueTypeZh();
                                if ( !beUseList.contains(mapKey) ){
                                    sdlScaScanItemService.save(param);
                                    beUseList.add(mapKey);
                                }

                                startId ++;
                            }
                        }
                        if ( success ){
                            FileUtils.writeToFile("D:\\testProgram\\secidea_scan\\success1.txt",fileLine + "\r\n" ,true );
                        }

                    }else {
                        // 输出错误结果
                        FileUtils.writeToFile("D:\\testProgram\\secidea_scan\\error1.txt",fileLine + "\r\n" ,true );
                    }



                }
            }
        }



        return Result.OK();
    }

    @RequiresRoles(value = {"admin","dev_admin"}, logical = Logical.OR)
    @GetMapping("autoUpdateSdlScaScanItemTop10")
    public Result<?> autoUpdateSdlScaScanItemTop10(){

        // 1. 先找到sdl_sca_scan_item 最后一个Id
//        SdlScaScanItem sdlScaScanItem = sdlScaScanItemService.getOne(new LambdaQueryWrapper<SdlScaScanItem>().orderByDesc(SdlScaScanItem::getId).last("limit 1"));
//        Long startId = Long.valueOf(sdlScaScanItem.getId()) + 1;
        Long startId = Long.valueOf("1468126452616794113");

        // 所有sdlStandReqItems 集合，并转为map key value 形式储存
        List<SdlStandardReqItems> sdlStandardReqItemsList = sdlStandardReqItemsService.list(new LambdaQueryWrapper<SdlStandardReqItems>().eq(SdlStandardReqItems::getStandardId,"1448914228730683393"));
        Map<String,String> sdlStandardReqItemsMap = new HashMap(sdlStandardReqItemsList.size());

        for (SdlStandardReqItems sdlStandardReqItems : sdlStandardReqItemsList) {
            sdlStandardReqItemsMap.put(sdlStandardReqItems.getRequirementItem(),sdlStandardReqItems.getId());
        }


        // 读取文件
//        String fileContent = "System Information Leak: XPath Error\tCWE ID 209\n" +
//                "Axis 2 Misconfiguration: Debug Information\tCWE ID 215\n";
        String fileContent = FileUtils.readFile(new File("D:\\testProgram\\secidea_scan\\top10.txt"));
        String []fileLineContent = fileContent.split("\r\n");
        for (String fileLine : fileLineContent) {
            if ( !StringUtils.isBlank(fileLine) ){
                String[] fileLine2 = fileLine.split("\t");
                // 查找数据库是否存在该数据
                String issueTypeEn = fileLine2[0];
                String standardItemName = fileLine2[1];
                // 更新名称，防止对不上if ( )
                for (SdlStandardReqItems sdlStandardReqItems : sdlStandardReqItemsList) {
                    if ( sdlStandardReqItems.getStandardName().startsWith(standardItemName.split("-")[0]) ){
                        standardItemName = sdlStandardReqItems.getStandardName();
                        break;
                    }
                }

                long count = sdlScaScanItemService.count(new LambdaQueryWrapper<SdlScaScanItem>().eq(SdlScaScanItem::getIssueTypeEn,issueTypeEn).eq(SdlScaScanItem::getStandardItemName,standardItemName));
                // 新增
                if ( count == 0 ){
                    boolean success = true;
                    // 先找到CWE编号的数据
                    if ( sdlStandardReqItemsMap.containsKey(standardItemName) ){
                        // 输出正确结果
                        // 制作需要插入的数据
                        SdlScaScanItem param = new SdlScaScanItem();
                        param/*.setId(String.valueOf(startId))*/
                                .setCreateBy("admin")
                                .setCreateTime(new Date())
                                .setSysOrgCode("A01")
                                .setStandardItemName(standardItemName)
                                .setStandardItemId(sdlStandardReqItemsMap.get(standardItemName));

                        // 根据 issueTypeEn 获取 sca_risk_knowledge 获取所需数据
                        List<ScaRiskKnowledge> scaRiskKnowledgeList = scaRiskKnowledgeService.list(new LambdaQueryWrapper<ScaRiskKnowledge>().eq(ScaRiskKnowledge::getIssuetypeen, issueTypeEn));
                        if ( scaRiskKnowledgeList.isEmpty() ){
                            // 输出错误结果
                            FileUtils.writeToFile("D:\\testProgram\\secidea_scan\\error10.txt",fileLine + "\r\n" ,true );
                            success = false;
                        } else {
                            // TODO 去重
                            List<String> beUseList = new ArrayList<>();

                            for (ScaRiskKnowledge scaRiskKnowledge : scaRiskKnowledgeList) {
                                param.setId(String.valueOf(startId))
                                        .setStandardItemId(sdlStandardReqItemsMap.get(standardItemName))
                                        .setIssueTypeZh(scaRiskKnowledge.getIssuetypezh())
                                        .setIssueTypeEn(scaRiskKnowledge.getIssuetypeen())
                                        .setCategory(scaRiskKnowledge.getCategory())
                                        .setSeverityType(scaRiskKnowledge.getCategory())
//                                        .setLanguageLower(scaRiskKnowledge.getLanguagelower())
                                        .setIsSuit(1)
                                        .setIssueAbstract(scaRiskKnowledge.getIssueabstract())
                                        .setIssueTypeId(scaRiskKnowledge.getId());
                                // 插入数据
                                String mapKey = param.getStandardItemId() + param.getIssueTypeZh();
                                if ( !beUseList.contains(mapKey) ){
                                    sdlScaScanItemService.save(param);
                                    beUseList.add(mapKey);
                                }

                                startId ++;
                            }
                        }
                        if ( success ){
                            FileUtils.writeToFile("D:\\testProgram\\secidea_scan\\success10.txt",fileLine + "\r\n" ,true );
                        }

                    }else {
                        // 输出错误结果
                        FileUtils.writeToFile("D:\\testProgram\\secidea_scan\\error10.txt",fileLine + "\r\n" ,true );
                    }



                }
            }
        }



        return Result.OK();
    }

    /**
     * 判断对应的缺陷模板可以编辑，如果模板不存在或不能编辑，抛出异常
     *
     * @param standardId 缺陷模板Id
     */
    private void assertStandardCanEdit(String standardId) {
        if (StringUtils.isBlank(standardId)) {
            throw new JeecgBootException("缺陷模板ID不存在!");
        }
        SdlCodingStandard standard = sdlCodingStandardService.getById(standardId);
        if (Objects.isNull(standard)) {
            throw new JeecgBootException("缺陷模板不存在!");
        }
        if (!Boolean.TRUE.equals(standard.getEditable())) {
            throw new JeecgBootException("内置缺陷模板不允许修改!");
        }
    }

    private void assertCanEditByStandardItemIds(Collection<String> standardItemIds) {
        if (CollectionUtils.isEmpty(standardItemIds)) {
            throw new JeecgBootException("安全规范要求项ID为空");
        }
        List<SdlStandardReqItems> sdlStandardReqItems = sdlStandardReqItemsService.listByIds(standardItemIds);
        if (CollectionUtils.isEmpty(sdlStandardReqItems)) {
            throw new JeecgBootException("安全规范要求项为空");
        }
        Set<String> standardIds = sdlStandardReqItems.stream().map(SdlStandardReqItems::getStandardId).collect(Collectors.toSet());
        List<SdlCodingStandard> sdlCodingStandards = sdlCodingStandardService.listByIds(standardIds);
        for (SdlCodingStandard sdlCodingStandard : sdlCodingStandards) {
            if (!Boolean.TRUE.equals(sdlCodingStandard.getEditable())) {
                throw new JeecgBootException("内置缺陷模板不允许修改!");
            }
        }
    }


}
