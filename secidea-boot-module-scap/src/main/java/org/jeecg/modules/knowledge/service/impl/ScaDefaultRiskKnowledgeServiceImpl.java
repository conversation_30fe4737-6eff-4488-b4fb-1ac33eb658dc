package org.jeecg.modules.knowledge.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.knowledge.entity.ScaDefaultRiskKnowledge;
import org.jeecg.modules.knowledge.mapper.ScaDefaultRiskKnowledgeMapper;
import org.jeecg.modules.knowledge.service.IScaDefaultRiskKnowledgeService;
import org.springframework.stereotype.Service;

/**
 * 内置默认代码缺陷知识库 service实现
 */
@Service
public class ScaDefaultRiskKnowledgeServiceImpl extends ServiceImpl<ScaDefaultRiskKnowledgeMapper, ScaDefaultRiskKnowledge> implements IScaDefaultRiskKnowledgeService {
}
