package org.jeecg.modules.knowledge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.knowledge.entity.SdlStandardReqItems;
import org.jeecg.modules.knowledge.mapper.SdlScaScanItemMapper;
import org.jeecg.modules.knowledge.mapper.SdlStandardReqItemsMapper;
import org.jeecg.modules.knowledge.service.ISdlStandardReqItemsService;
import org.jeecg.modules.sectest.scap.utils.report.SdlStandardReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 安全规范要求项
 * @Author: jeecg-boot
 * @Date:   2021-08-01
 * @Version: V1.0
 */
@Service
public class SdlStandardReqItemsServiceImpl extends ServiceImpl<SdlStandardReqItemsMapper, SdlStandardReqItems> implements ISdlStandardReqItemsService {

	@Autowired
	private SdlStandardReqItemsMapper sdlStandardReqItemsMapper;
	@Autowired
	private SdlScaScanItemMapper sdlScaScanItemMapper;

	@Override
	@Transactional
	public void delMain(String id) {
		sdlScaScanItemMapper.deleteByMainId(id);
		sdlStandardReqItemsMapper.deleteById(id);
	}

	@Override
	@Transactional
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			sdlScaScanItemMapper.deleteByMainId(id.toString());
			sdlStandardReqItemsMapper.deleteById(id);
		}
	}

	@Override
	public List<SdlStandardReq> findSdlStandardReqListByStandardId (String standardId){
		return this.getBaseMapper().findSdlStandardReqListByStandardId(standardId);
	}

	@Override
	@Cacheable(cacheNames= "sdl:standard:req:items:id", key="#standardId")
	public List<SdlStandardReqItems> listByStandardId(String standardId) {
		return list(new LambdaQueryWrapper<SdlStandardReqItems>().eq(SdlStandardReqItems::getStandardId,standardId));
	}

}
