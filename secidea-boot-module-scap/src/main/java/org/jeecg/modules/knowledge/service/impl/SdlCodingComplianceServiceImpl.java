package org.jeecg.modules.knowledge.service.impl;

import org.jeecg.modules.knowledge.entity.SdlCodingCompliance;
import org.jeecg.modules.knowledge.mapper.SdlCodingComplianceMapper;
import org.jeecg.modules.knowledge.service.ISdlCodingComplianceService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 安全编码合规
 * @Author: jeecg-boot
 * @Date:   2020-11-14
 * @Version: V1.0
 */
@Service
public class SdlCodingComplianceServiceImpl extends ServiceImpl<SdlCodingComplianceMapper, SdlCodingCompliance> implements ISdlCodingComplianceService {

}
