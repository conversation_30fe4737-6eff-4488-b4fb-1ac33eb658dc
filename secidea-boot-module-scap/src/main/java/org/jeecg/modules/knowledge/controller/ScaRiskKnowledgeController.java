package org.jeecg.modules.knowledge.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.secidea.Knowledge;
import com.secidea.ScapKnowledge;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.StringUtils;
import org.jeecg.common.util.encryption.JasyptUtil;
import org.jeecg.common.util.encryption.SimpleEncryptionUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.base.entity.IdsParam;
import org.jeecg.modules.knowledge.entity.ScaRiskKnowledge;
import org.jeecg.modules.knowledge.service.IScaRiskKnowledgeService;
import org.jeecg.modules.knowledge.utils.dto.riskknowledge.ResetRiskKnowledgeReq;
import org.jeecg.modules.knowledge.utils.dto.riskknowledge.ScaRiskKnowledgeReq;
import org.jeecg.modules.knowledge.utils.dto.riskknowledge.ScaRiskKnowledgeResp;
import org.jeecg.modules.language.entity.ScaLanguage;
import org.jeecg.modules.language.service.IScaLanguageService;
import org.jeecg.modules.sectest.scap.entity.ScaProjectOnlyRiskKnowledge;
import org.jeecg.modules.sectest.scap.entity.ScaScanResult;
import org.jeecg.modules.sectest.scap.entity.ScaTaskResult;
import org.jeecg.modules.sectest.scap.service.IScaProjectOnlyRiskKnowledgeService;
import org.jeecg.modules.sectest.scap.service.IScaScanResultService;
import org.jeecg.modules.sectest.scap.service.IScaTaskResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 代码缺陷知识库
 * @Author: jeecg-boot
 * @Date:   2021-08-28
 * @Version: V1.0
 */
@Tag(name = "代码缺陷知识库")
@RestController
@RequestMapping("/scaRiskKnowledge")
@Slf4j
public class ScaRiskKnowledgeController extends JeecgController<ScaRiskKnowledge, IScaRiskKnowledgeService> {
	@Autowired
	private IScaRiskKnowledgeService scaRiskKnowledgeService;
	@Autowired
	private IScaProjectOnlyRiskKnowledgeService scaProjectOnlyRiskKnowledgeService;
	@Autowired
	private IScaLanguageService scaLanguageService;
	@Autowired
	private IScaTaskResultService scaTaskResultService;
	@Autowired
	private IScaScanResultService scaScanResultService;
	@Autowired
	private ISysBaseAPI sysBaseAPI;
	@Value(value = "${jeecg.path.upload}")
	private String uploadpath;
	/**
	 * 分页列表查询
	 *
	 * @param scaRiskKnowledge
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
//	@AutoLog(value = "代码缺陷知识库-分页列表查询")
	@Operation(summary = "代码缺陷知识库-分页列表查询", description = "代码缺陷知识库-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(ScaRiskKnowledge scaRiskKnowledge,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<ScaRiskKnowledge> queryWrapper = QueryGenerator.initQueryWrapper(scaRiskKnowledge, req.getParameterMap());
		Page<ScaRiskKnowledge> page = new Page<ScaRiskKnowledge>(pageNo, pageSize);
		String noInScaProjectOnlyRiskKnowledgeForProjectId = req.getParameter("noInScaProjectOnlyRiskKnowledgeForProjectId");
		if ( !StringUtils.isBlank( noInScaProjectOnlyRiskKnowledgeForProjectId ) ){
			// 关联 sca_project_only_risk表
			List<ScaProjectOnlyRiskKnowledge> scaProjectOnlyRiskKnowledgeList = scaProjectOnlyRiskKnowledgeService.list(new LambdaQueryWrapper<ScaProjectOnlyRiskKnowledge>().eq(ScaProjectOnlyRiskKnowledge::getScaProjectId, noInScaProjectOnlyRiskKnowledgeForProjectId).select(ScaProjectOnlyRiskKnowledge::getScaRiskKnowledgeId));
			if ( !scaProjectOnlyRiskKnowledgeList.isEmpty() ){
				List<String> noInIds = new ArrayList<>(scaProjectOnlyRiskKnowledgeList.size());
				scaProjectOnlyRiskKnowledgeList.forEach(o->{
					noInIds.add(o.getScaRiskKnowledgeId());
				});
				queryWrapper.lambda().notIn(ScaRiskKnowledge::getId,noInIds);
			}
		}
		IPage<ScaRiskKnowledge> pageList = scaRiskKnowledgeService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	@GetMapping(value = "/getIssueTypeZhList")
	public Result<?> getIssueTypeZhList(ScaRiskKnowledge scaRiskKnowledge, HttpServletRequest req) {
		List<String> resultList = new ArrayList<>();
		QueryWrapper<ScaRiskKnowledge> queryWrapper = QueryGenerator.initQueryWrapper(scaRiskKnowledge, req.getParameterMap());
		queryWrapper.lambda().select(ScaRiskKnowledge::getIssuetypezh).groupBy(ScaRiskKnowledge::getIssuetypezh);
		List<ScaRiskKnowledge> scaRiskKnowledgeList = scaRiskKnowledgeService.list(queryWrapper);
		if (!scaRiskKnowledgeList.isEmpty()) {
			resultList = scaRiskKnowledgeList.stream().map(ScaRiskKnowledge::getIssuetypezh).collect(Collectors.toList());
		}
		return Result.OK(resultList);
	}

	@Operation(summary = "代码缺陷知识库-字典接口", description = "代码缺陷知识库-字典接口")
	@GetMapping(value = "/queryDictList")
	public Result<?> queryDictList(ScaRiskKnowledge scaRiskKnowledge){
		if ( StringUtils.isBlank(scaRiskKnowledge.getLanguagelower()) ){
			return Result.error("数据异常");
		}
		LambdaQueryWrapper<ScaRiskKnowledge> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(ScaRiskKnowledge::getLanguagelower, scaRiskKnowledge.getLanguagelower())
				.select(ScaRiskKnowledge::getId, ScaRiskKnowledge::getRiskLevel,
						ScaRiskKnowledge::getIssuetypezh, ScaRiskKnowledge::getIssuetypeen);
		return Result.OK(scaRiskKnowledgeService.list(queryWrapper));
	}

	/**
	 * 添加
	 *
	 * @param scaRiskKnowledgeReq 接口接收缺陷知识库信息
	 * @return 处理结果
	 */
	@AutoLog(value = "代码缺陷知识库-添加")
	@Operation(summary = "代码缺陷知识库-添加", description = "代码缺陷知识库-添加")
	@PostMapping(value = "/add")
    @RequiresPermissions(value="scap:risk:knowledge:add")
	public Result<?> add(@RequestBody ScaRiskKnowledgeReq scaRiskKnowledgeReq) {
		setLanguageLower(scaRiskKnowledgeReq);
		scaRiskKnowledgeService.saveRiskKnowledge(scaRiskKnowledgeReq);
		return Result.OK("添加成功！");
	}

	/**
	 * 编辑
	 *
	 * @param scaRiskKnowledgeReq 接口接收缺陷知识库信息
	 * @return 处理结果
	 */
	@AutoLog(value = "代码缺陷知识库-编辑")
	@Operation(summary = "代码缺陷知识库-编辑", description = "代码缺陷知识库-编辑")
	@PutMapping(value = "/edit")
    @RequiresPermissions(value="scap:risk:knowledge:edit")
	public Result<?> edit(@RequestBody ScaRiskKnowledgeReq scaRiskKnowledgeReq) {
		setLanguageLower(scaRiskKnowledgeReq);
		scaRiskKnowledgeService.updateRiskKnowledge(scaRiskKnowledgeReq);
		return Result.OK("编辑成功!");
	}

	private void setLanguageLower(ScaRiskKnowledge scaRiskKnowledge) {
		if (!StringUtils.isBlank(scaRiskKnowledge.getLanguage())) {
			ScaLanguage scaLanguage = scaLanguageService.getOne(new LambdaQueryWrapper<ScaLanguage>().eq(ScaLanguage::getLanguage, scaRiskKnowledge.getLanguage()));
			if (oConvertUtils.isEmpty(scaLanguage)) {
				throw new JeecgBootException("不存在的语言");
			}
			scaRiskKnowledge.setLanguagelower(scaLanguage.getLanguagelower());
		}
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "代码缺陷知识库-通过id删除")
	@Operation(summary = "代码缺陷知识库-通过id删除", description = "代码缺陷知识库-通过id删除")
	@DeleteMapping(value = "/delete")
    @RequiresPermissions(value="scap:risk:knowledge:delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		scaRiskKnowledgeService.removeRiskKnowledgeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param idsParam
	 * @return
	 */
	@AutoLog(value = "代码缺陷知识库-批量删除")
	@Operation(summary = "代码缺陷知识库-批量删除", description = "代码缺陷知识库-批量删除")
	@DeleteMapping(value = "/deleteBatch")
    @RequiresPermissions(value="scap:risk:knowledge:delete")
	public Result<?> deleteBatch(@RequestBody @Validated IdsParam idsParam) {
		this.scaRiskKnowledgeService.removeRiskKnowledgeByIds(Arrays.asList(idsParam.getIds().split(",")));
		return Result.OK("批量删除成功!");
	}

	@AutoLog(value = "代码缺陷知识库-重置知识库记录")
	@Operation(summary = "代码缺陷知识库-重置知识库记录", description = "代码缺陷知识库-重置知识库记录")
	@PostMapping(value = "/reset")
	@RequiresPermissions(value="scap:risk:knowledge:reset")
	public Result<?> resetRiskKnowledge(@RequestBody ResetRiskKnowledgeReq resetRiskKnowledgeReq) {
		Set<String> idSet = org.springframework.util.StringUtils.commaDelimitedListToSet(resetRiskKnowledgeReq.getIds());
		this.scaRiskKnowledgeService.resetRiskKnowledge(idSet);
		return Result.ok("重置知识库记录成功!");
	}

	@AutoLog(value = "代码缺陷知识库-重置全量知识库")
	@Operation(summary = "代码缺陷知识库-重置全量知识库", description = "代码缺陷知识库-重置全量知识库")
	@PostMapping(value = "/resetAll")
	@RequiresPermissions(value="scap:risk:knowledge:reset")
	public Result<?> resetAllRiskKnowledge() {
		this.scaRiskKnowledgeService.resetAllRiskKnowledge();
		return Result.ok("重置全量知识库成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@Operation(summary = "代码缺陷知识库-通过id查询", description = "代码缺陷知识库-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
		return getById(id);
	}

	public Result<?> getIdByTaskAndResultId(String taskId, String resultId, String id) {
		if (StringUtils.isBlank(taskId) || StringUtils.isBlank(resultId) || StringUtils.isBlank(id)) {
			return Result.error("无权限查询");
		}
		if (scaTaskResultService.count(new LambdaQueryWrapper<ScaTaskResult>()
				.eq(ScaTaskResult::getScapTaskId, taskId).eq(ScaTaskResult::getScapResultId, resultId)) == 0) {
			return Result.error("无权限查询");
		}
		ScaScanResult scaScanResult = scaScanResultService.getById(resultId);
		if (StringUtils.isBlank(scaScanResult.getKnowledgeId()) || !id.equals(scaScanResult.getKnowledgeId())) {
			return Result.error("无权限查询");
		}
		return getById(id);
	}

	public Result<?> getById(String id) {
		// 知识库使用模式
		String encryptRK = sysBaseAPI.getSysConfigParamByKey("encryptRK");
		// 加密模式
		if ("2".equals(encryptRK)) {
			Knowledge knowledge = new Knowledge();
			ScapKnowledge scapKnowledge = knowledge.queryById(id);
			if (scapKnowledge == null) {
				return Result.error("未找到对应数据");
			}
			scapKnowledge.setIssueabstract(JasyptUtil.decrypt(scapKnowledge.getIssueabstract()));
			scapKnowledge.setIssuedescription(JasyptUtil.decrypt(scapKnowledge.getIssuedescription()));
			scapKnowledge.setIssuesolution(JasyptUtil.decrypt(scapKnowledge.getIssuesolution()));
			return Result.OK(scapKnowledge);
		}
		// 数据库模式
		else {
			ScaRiskKnowledge knowledge = scaRiskKnowledgeService.getById(id);
			if (Objects.isNull(knowledge)) {
				return Result.error("未找到对应数据");
			}
			return Result.OK(knowledge);
		}
	}

	@Operation(summary = "代码缺陷知识库-通过id查询知识库和模板关联", description = "代码缺陷知识库-通过id查询知识库和模板关联")
	@GetMapping(value = "/queryKnowledgeAndTemplateById")
	public Result<?> queryKnowledgeAndTemplateById(@RequestParam(name = "id") String id,
												   @RequestParam(name = "fillAllReqItems", defaultValue = "false") Boolean fillAllReqItems) {
		// 知识库使用模式
		String encryptRK = sysBaseAPI.getSysConfigParamByKey("encryptRK");
		// 加密模式
		if ("2".equals(encryptRK)) {
			Knowledge knowledge = new Knowledge();
			ScapKnowledge scapKnowledge = knowledge.queryById(id);
			if (scapKnowledge == null) {
				return Result.error("未找到对应数据");
			}
			scapKnowledge.setIssueabstract(JasyptUtil.decrypt(scapKnowledge.getIssueabstract()));
			scapKnowledge.setIssuedescription(JasyptUtil.decrypt(scapKnowledge.getIssuedescription()));
			scapKnowledge.setIssuesolution(JasyptUtil.decrypt(scapKnowledge.getIssuesolution()));
			return Result.OK(scapKnowledge);
		}
		// 数据库模式
		else {
			ScaRiskKnowledgeResp resp = scaRiskKnowledgeService.getRiskKnowledgeById(id, fillAllReqItems);
			if (Objects.isNull(resp)) {
				return Result.error("未找到对应数据");
			}
			return Result.OK(resp);
		}
	}

	@Operation(summary = "代码缺陷知识库-第三方规则同步", description = "代码缺陷知识库-第三方规则同步")
	@GetMapping(value = "/updateVulnThirdParty")
	public Result<?> updateVulnThirdParty(@RequestParam(name = "comPath", required = true) String comPath) {
		String filePath = uploadpath + File.separator + comPath;
		System.out.println("filePath:" + filePath);
		String fileType = filePath.substring(filePath.lastIndexOf(".") + 1).toLowerCase();
		if (!"json".equals(fileType)) {
			return Result.error("上传文件类型有误！！");
		}
		File file = new File(filePath);
		String input = null;
		try {
			input = FileUtils.readFileToString(file,"UTF-8");
		} catch (IOException e) {
			log.info("第三方规则同步，获取文件异常：" + e.getMessage());
			return Result.error("第三方规则同步，获取文件异常！");
		}
		JSONObject json = JSONObject.parseObject(input);
//		log.info("第三方规则同步：" + json.toJSONString());
		JSONArray rules = json.getJSONArray("rules");
		for (Object o : rules) {
			JSONObject jsonObject = (JSONObject) o;
			String key = "xc_" + jsonObject.getString("key");
			String nameZh = jsonObject.getString("name_zh");
			String nameEn = "xc_" + jsonObject.getString("name_en");
			String language = jsonObject.getString("language");
			String anAbstract = jsonObject.getString("abstract");
			String description = jsonObject.getString("description");
			String fix = jsonObject.getString("fix");
			ScaRiskKnowledge scaRiskKnowledge = new ScaRiskKnowledge();
			scaRiskKnowledge.setIssuetypezh(nameZh);
			scaRiskKnowledge.setIssuetypeen(nameEn);
			scaRiskKnowledge.setIssueabstract(anAbstract);
			scaRiskKnowledge.setIssuedescription(description);
			scaRiskKnowledge.setIssuesolution(fix);
			scaRiskKnowledge.setKingdom("Security Features");
			scaRiskKnowledge.setCategory(key);

			String languages = "Universal";
			String languageLower = "universal";
			switch (language) {
				case "php":
					languages = "PHP";
					languageLower = "php";
					break;
				case "cpp":
					languages = "C/C++";
					languageLower = "cc";
					break;
				case "java":
					languages = "Java/Jsp";
					languageLower = "javajsp";
					break;
				case "go":
					languages = "Go";
					languageLower = "go";
					break;
				case "js":
					languages = "JavaScript";
					languageLower = "javascript";
					break;
				case "py":
					languages = "Python";
					languageLower = "python";
					break;
				default:
					break;
			}
			scaRiskKnowledge.setLanguage(languages);
			scaRiskKnowledge.setLanguagelower(languageLower);
			scaRiskKnowledge.setFixrequirement("建议整改");
			scaRiskKnowledge.setSeveritytype(1);
			LambdaQueryWrapper<ScaRiskKnowledge> scaRiskKnowledgeLambdaQueryWrapper = new LambdaQueryWrapper<>();
			scaRiskKnowledgeLambdaQueryWrapper.eq(ScaRiskKnowledge::getIssuetypeen, nameEn);
			scaRiskKnowledgeService.saveOrUpdate(scaRiskKnowledge, scaRiskKnowledgeLambdaQueryWrapper);
		}
		//删除执行过的文件
		try {
			FileUtils.delete(file);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return Result.OK();
	}
    /**
    * 导出excel
    *
    * @param jsonObject
    */
    @PostMapping(value = "/exportXls")
    @RequiresPermissions(value="scap:risk:knowledge:export")
    public ModelAndView exportXls(@RequestBody JSONObject jsonObject) {
        return super.exportXls(jsonObject, ScaRiskKnowledge.class, "代码缺陷知识库");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @PostMapping(value = "/importExcel")
    @RequiresPermissions(value="scap:risk:knowledge:import")
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ScaRiskKnowledge.class);
    }

}
