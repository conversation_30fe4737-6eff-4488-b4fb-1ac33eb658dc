package org.jeecg.modules.knowledge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.StringUtils;
import org.jeecg.modules.knowledge.entity.*;
import org.jeecg.modules.knowledge.mapper.ScaRiskKnowledgeMapper;
import org.jeecg.modules.knowledge.service.*;
import org.jeecg.modules.knowledge.utils.dto.riskknowledge.ScaRiskKnowledgeReq;
import org.jeecg.modules.knowledge.utils.dto.riskknowledge.ScaRiskKnowledgeReq.AssociatedTemplateIdInfo;
import org.jeecg.modules.knowledge.utils.dto.riskknowledge.ScaRiskKnowledgeResp;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 代码缺陷知识库
 * @Author: jeecg-boot
 * @Date:   2021-08-28
 * @Version: V1.0
 */
@Service
public class ScaRiskKnowledgeServiceImpl extends ServiceImpl<ScaRiskKnowledgeMapper, ScaRiskKnowledge> implements IScaRiskKnowledgeService {

    @Autowired
    private ISdlScaScanItemService sdlScaScanItemService;

    @Autowired
    private ISdlStandardReqItemsService sdlStandardReqItemsService;

    @Autowired
    private ISdlCodingStandardService sdlCodingStandardService;

    @Autowired
    private IScaDefaultRiskKnowledgeService scaDefaultRiskKnowledgeService;

    @Autowired
    @Lazy
    private IScaRiskKnowledgeService scaRiskKnowledgeService;

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 用户自定义缺陷知识项 accuracy 的值
     */
    private static final String CUSTOM = "99";

    /**
     * 未修改的缺陷知识项 issuestate 的值
     */
    private static final Integer UNMODIFIED = 0;

    /**
     * 修改过的缺陷知识项 issuestate 的值
     */
    private static final Integer MODIFIED = 1;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRiskKnowledge(ScaRiskKnowledgeReq scaRiskKnowledgeReq) {
        // 保存缺陷知识库记录
        scaRiskKnowledgeReq.setIssuetypeen("SCAP-userAdd-en-" + IdWorker.getId());
        scaRiskKnowledgeReq.setCategory("SCAP-userAdd-category-" + IdWorker.getId());
        scaRiskKnowledgeReq.setAccuracy(CUSTOM);
        scaRiskKnowledgeReq.setIssuestate(UNMODIFIED);
        // 用户自定义的记录ID以 ’c‘ 开头
        scaRiskKnowledgeReq.setId("c" + IdWorker.getId());
        save(scaRiskKnowledgeReq);

        // 保存对应模版关联
        saveAssociatedScanItems(scaRiskKnowledgeReq, scaRiskKnowledgeReq.getAssociatedTemplates());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRiskKnowledge(ScaRiskKnowledgeReq scaRiskKnowledgeReq) {
        ScaRiskKnowledge knowledge = getById(scaRiskKnowledgeReq.getId());
        if (Objects.isNull(knowledge)) {
            throw new JeecgBootException("缺陷知识库记录不存在！");
        }

        if (!CUSTOM.equals(knowledge.getAccuracy())) {
            // 系统内置的缺陷知识库记录更新，仅允许更新缺陷描述和解决方案的内容
            ScaRiskKnowledge scaRiskKnowledge = new ScaRiskKnowledge();
            scaRiskKnowledge.setId(scaRiskKnowledgeReq.getId());
            scaRiskKnowledge.setIssuedescription(scaRiskKnowledgeReq.getIssuedescription());
            scaRiskKnowledge.setIssuesolution(scaRiskKnowledgeReq.getIssuesolution());
            scaRiskKnowledge.setIssuestate(MODIFIED);
            scaRiskKnowledge.setRiskLevel(scaRiskKnowledgeReq.getRiskLevel());
            updateById(scaRiskKnowledge);
            return;
        }

        // 保存缺陷知识库记录
        updateById(scaRiskKnowledgeReq);
        // 查询更新前缺陷知识库记录关联的缺陷模板项sdl_sca_scan_item
        LambdaQueryWrapper<SdlScaScanItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SdlScaScanItem::getIssueTypeId, scaRiskKnowledgeReq.getId());
        wrapper.select(SdlScaScanItem::getId, SdlScaScanItem::getStandardItemId);
        List<SdlScaScanItem> scaScanItems = sdlScaScanItemService.list(wrapper);

        Set<String> standardItemIds = scaRiskKnowledgeReq.getAssociatedTemplates()
                .stream()
                .map(AssociatedTemplateIdInfo::getStandardItemId)
                .collect(Collectors.toSet());

        // 删除更新中移除的模版项
        List<String> removedScanItemIds = scaScanItems
                .stream()
                .filter(item -> !standardItemIds.contains(item.getStandardItemId()))
                .map(SdlScaScanItem::getId)
                .collect(Collectors.toList());
        if (!removedScanItemIds.isEmpty()) {
            sdlScaScanItemService.removeBatchByIds(removedScanItemIds);
        }

        // 保存更新前不存在的模版项
        Set<String> existStandardItemIds = scaScanItems.stream().map(SdlScaScanItem::getStandardItemId).collect(Collectors.toSet());
        List<AssociatedTemplateIdInfo> toBeSavedTemplates = scaRiskKnowledgeReq.getAssociatedTemplates()
                .stream()
                .filter(template -> !existStandardItemIds.contains(template.getStandardItemId()))
                .collect(Collectors.toList());
        saveAssociatedScanItems(scaRiskKnowledgeReq, toBeSavedTemplates);

        // 更新原有关联模板项的相关信息
        List<SdlScaScanItem> existScanItems = scaScanItems
                .stream()
                .filter(item -> standardItemIds.contains(item.getStandardItemId()))
                .collect(Collectors.toList());
        for (SdlScaScanItem existScanItem : existScanItems) {
            existScanItem.setIssueTypeZh(scaRiskKnowledgeReq.getIssuetypezh());
            existScanItem.setIssueAbstract(scaRiskKnowledgeReq.getIssueabstract());
            existScanItem.setLanguageLower(scaRiskKnowledgeReq.getLanguagelower());
            existScanItem.setLanguage(scaRiskKnowledgeReq.getLanguage());
            existScanItem.setSeverityType(String.valueOf(scaRiskKnowledgeReq.getSeveritytype()));
        }
        sdlScaScanItemService.updateBatchById(existScanItems);
    }

    @Override
    public ScaRiskKnowledgeResp getRiskKnowledgeById(String id, Boolean fillAllReqItems) {
        ScaRiskKnowledge scaRiskKnowledge = getById(id);
        if (scaRiskKnowledge == null) {
            return null;
        }
        ScaRiskKnowledgeResp resp = new ScaRiskKnowledgeResp();
        BeanUtils.copyProperties(scaRiskKnowledge, resp);
        List<ScaRiskKnowledgeResp.AssociatedTemplate> associatedTemplates = new ArrayList<>();
        resp.setAssociatedTemplates(associatedTemplates);

        // 获取该缺陷知识项关联的缺陷模版项
        LambdaQueryWrapper<SdlScaScanItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SdlScaScanItem::getIssueTypeId, scaRiskKnowledge.getId());
        wrapper.select(SdlScaScanItem::getStandardItemId);
        List<SdlScaScanItem> scaScanItems = sdlScaScanItemService.list(wrapper);
        Set<String> standardItemIds = scaScanItems.stream().map(SdlScaScanItem::getStandardItemId).collect(Collectors.toSet());
        if (standardItemIds.isEmpty()) {
            return resp;
        }

        // 获取对应的安全规范要求项
        LambdaQueryWrapper<SdlStandardReqItems> standardReqItemsWrapper = new LambdaQueryWrapper<>();
        standardReqItemsWrapper.select(SdlStandardReqItems::getId, SdlStandardReqItems::getStandardId, SdlStandardReqItems::getRequirementItem);
        standardReqItemsWrapper.in(SdlStandardReqItems::getId, standardItemIds);
        List<SdlStandardReqItems> existReqItems = sdlStandardReqItemsService.list(standardReqItemsWrapper);
        Set<String> standardIds = existReqItems.stream().map(SdlStandardReqItems::getStandardId).collect(Collectors.toSet());
        if (standardIds.isEmpty()) {
            return resp;
        }
        Map<String, SdlStandardReqItems> itemIdReqItemMap = existReqItems.stream()
                .collect(Collectors.toMap(SdlStandardReqItems::getId, o -> o, (k1, k2) -> k1));

        // 获取对应的模板
        LambdaQueryWrapper<SdlCodingStandard> standardWrapper = new LambdaQueryWrapper<>();
        standardWrapper.select(SdlCodingStandard::getId, SdlCodingStandard::getStandardName);
        standardWrapper.eq(SdlCodingStandard::getStatus, 2);
        standardWrapper.in(SdlCodingStandard::getId, standardIds);
        List<SdlCodingStandard> standards = sdlCodingStandardService.list(standardWrapper);
        if (standards.isEmpty()) {
            return resp;
        }
        Map<String, SdlCodingStandard> codingStandardMap = standards.stream()
                .collect(Collectors.toMap(SdlCodingStandard::getId, o -> o, (k1, k2) -> k1));

        Map<String, List<SdlStandardReqItems>> standardReqItemsMap = new HashMap<>();
        if (fillAllReqItems) {
            LambdaQueryWrapper<SdlStandardReqItems> reqItemsWrapper = new LambdaQueryWrapper<>();
            reqItemsWrapper.in(SdlStandardReqItems::getStandardId, codingStandardMap.keySet());
            List<SdlStandardReqItems> sdlStandardReqItems = sdlStandardReqItemsService.list(reqItemsWrapper);
            standardReqItemsMap = sdlStandardReqItems.stream().collect(Collectors.groupingBy(SdlStandardReqItems::getStandardId));
        }

        for (String standardItemId : standardItemIds) {
            SdlStandardReqItems standardReqItems = itemIdReqItemMap.get(standardItemId);
            if (Objects.isNull(standardReqItems)) {
                continue;
            }
            SdlCodingStandard sdlCodingStandard = codingStandardMap.get(standardReqItems.getStandardId());
            if (Objects.isNull(sdlCodingStandard)) {
                continue;
            }
            ScaRiskKnowledgeResp.AssociatedTemplate template = new ScaRiskKnowledgeResp.AssociatedTemplate();
            template.setStandard(sdlCodingStandard);
            template.setStandardReqItems(standardReqItems);
            if (fillAllReqItems) {
                template.setAllStandardReqItems(standardReqItemsMap.get(sdlCodingStandard.getId()));
            }
            associatedTemplates.add(template);
        }
        return resp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeRiskKnowledgeById(String id) {
        ScaRiskKnowledge knowledge = getById(id);
        if (Objects.isNull(knowledge)) {
            return;
        }
        if (!CUSTOM.equals(knowledge.getAccuracy())) {
            throw new JeecgBootException("系统内置缺陷知识库信息不允许删除");
        }
        // 删除缺陷知识项
        removeById(knowledge.getId());
        // 删除缺陷模板项关联
        LambdaQueryWrapper<SdlScaScanItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SdlScaScanItem::getIssueTypeId, knowledge.getId());
        sdlScaScanItemService.remove(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeRiskKnowledgeByIds(List<String> idList) {
        List<ScaRiskKnowledge> knowledgeList = listByIds(idList);
        if (CollectionUtils.isEmpty(knowledgeList)) {
            return;
        }

        Set<ScaRiskKnowledge> systemKnowledgeList = knowledgeList
                .stream()
                .filter(knowledge -> !CUSTOM.equals(knowledge.getAccuracy()))
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(systemKnowledgeList)) {
            throw new JeecgBootException("待删除的缺陷知识项中包含系统缺陷知识项，删除失败！");
        }

        List<String> customKnowledgeIds = knowledgeList.stream()
                .filter(knowledge -> CUSTOM.equals(knowledge.getAccuracy()))
                .map(ScaRiskKnowledge::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customKnowledgeIds)) {
            return;
        }

        // 删除缺陷知识项
        removeByIds(customKnowledgeIds);
        // 删除缺陷模板项关联
        LambdaQueryWrapper<SdlScaScanItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SdlScaScanItem::getIssueTypeId, customKnowledgeIds);
        sdlScaScanItemService.remove(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetRiskKnowledge(Set<String> idSet) {
        if (CollectionUtils.isEmpty(idSet)) {
            return;
        }
        List<ScaDefaultRiskKnowledge> defaultKnowledgeList = scaDefaultRiskKnowledgeService.listByIds(idSet);
        if (CollectionUtils.isEmpty(defaultKnowledgeList)) {
            throw new JeecgBootException("默认知识库记录信息不存在，无法重置！");
        }
        ArrayList<ScaRiskKnowledge> updateKnowledgeList = new ArrayList<>();
        for (ScaDefaultRiskKnowledge scaDefaultRiskKnowledge : defaultKnowledgeList) {
            ScaRiskKnowledge scaRiskKnowledge = new ScaRiskKnowledge();
            BeanUtils.copyProperties(scaDefaultRiskKnowledge, scaRiskKnowledge);
            scaRiskKnowledge.setIssuestate(UNMODIFIED);
            updateKnowledgeList.add(scaRiskKnowledge);
        }
        updateBatchById(updateKnowledgeList);
    }

    @Override
    public void resetAllRiskKnowledge() {
        RLock lock = redissonClient.getLock("lock:scap:risk:knowledge:resetAll");
        boolean locked = lock.tryLock();
        if (!locked) {
            throw new JeecgBootException("正在重置知识库，请稍后重试!");
        }
        try {
            LambdaQueryWrapper<ScaRiskKnowledge> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ScaRiskKnowledge::getIssuestate, MODIFIED);
            List<ScaRiskKnowledge> modifiedKnowledgeList = list(wrapper);
            List<List<ScaRiskKnowledge>> partition = Lists.partition(modifiedKnowledgeList, 100);
            for (List<ScaRiskKnowledge> knowledgeList : partition) {
                List<String> ids = knowledgeList.stream().map(ScaRiskKnowledge::getId).collect(Collectors.toList());
                LambdaQueryWrapper<ScaDefaultRiskKnowledge> defaultKnowledgeWrapper = new LambdaQueryWrapper<>();
                defaultKnowledgeWrapper.in(ScaDefaultRiskKnowledge::getId, ids);
                List<ScaDefaultRiskKnowledge> defaultRiskKnowledgeList = scaDefaultRiskKnowledgeService.list(defaultKnowledgeWrapper);
                ArrayList<ScaRiskKnowledge> updateKnowledgeList = new ArrayList<>();
                for (ScaDefaultRiskKnowledge scaDefaultRiskKnowledge : defaultRiskKnowledgeList) {
                    ScaRiskKnowledge scaRiskKnowledge = new ScaRiskKnowledge();
                    BeanUtils.copyProperties(scaDefaultRiskKnowledge, scaRiskKnowledge);
                    scaRiskKnowledge.setIssuestate(UNMODIFIED);
                    updateKnowledgeList.add(scaRiskKnowledge);
                }
                scaRiskKnowledgeService.updateBatchById(updateKnowledgeList);
            }
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    private void saveAssociatedScanItems(ScaRiskKnowledge scaRiskKnowledge, List<AssociatedTemplateIdInfo> associatedTemplates) {
        if (CollectionUtils.isEmpty(associatedTemplates)) {
            return;
        }
        associatedTemplates.stream().filter(this::associatedTemplateNotBlank).forEach(associatedTemplate -> {
            SdlScaScanItem sdlScaScanItem = new SdlScaScanItem();
            sdlScaScanItem.setStandardItemId(associatedTemplate.getStandardItemId());
            sdlScaScanItem.setIssueTypeId(scaRiskKnowledge.getId());
            sdlScaScanItem.setIssueTypeZh(scaRiskKnowledge.getIssuetypezh());
            sdlScaScanItem.setIssueTypeEn(scaRiskKnowledge.getIssuetypeen());
            sdlScaScanItem.setCategory(scaRiskKnowledge.getCategory());
            sdlScaScanItem.setIssueAbstract(scaRiskKnowledge.getIssueabstract());
            sdlScaScanItem.setLanguageLower(scaRiskKnowledge.getLanguagelower());
            sdlScaScanItem.setLanguage(scaRiskKnowledge.getLanguage());
            sdlScaScanItem.setSeverityType(String.valueOf(scaRiskKnowledge.getSeveritytype()));
            Integer count = sdlScaScanItemService.countStandardKnowledgeId(associatedTemplate.getStandardId(), sdlScaScanItem.getIssueTypeId());
            if (count == 0) {
                SdlStandardReqItems sdlStandardReqItems = sdlStandardReqItemsService.getById(sdlScaScanItem.getStandardItemId());
                sdlScaScanItem.setStandardItemName(sdlStandardReqItems.getRequirementItem());
                sdlScaScanItemService.save(sdlScaScanItem);
            }
        });
    }

    private boolean associatedTemplateNotBlank(AssociatedTemplateIdInfo template) {
        return StringUtils.isNotBlank(template.getStandardId()) && StringUtils.isNotBlank(template.getStandardItemId());
    }
}
