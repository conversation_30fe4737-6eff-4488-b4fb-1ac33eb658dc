package org.jeecg.modules.knowledge.service;

import org.jeecg.modules.knowledge.entity.SdlScaScanItem;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 安全编码检测项
 * @Author: jeecg-boot
 * @Date:   2021-08-01
 * @Version: V1.0
 */
public interface ISdlScaScanItemService extends IService<SdlScaScanItem> {

	public List<SdlScaScanItem> selectByMainId(String mainId);

	public List<SdlScaScanItem> listByStandardItemId(String listByStandardItemId);

	/**
	 * 获取指定模板下指定缺陷知识的数量
	 * @param standardId 模板id
	 * @param knowledgeId 缺陷知识id
	 * @return 指定模板下指定缺陷知识的数量
	 */
    Integer countStandardKnowledgeId(String standardId, String knowledgeId);
}
