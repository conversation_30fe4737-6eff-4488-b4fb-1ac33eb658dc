package org.jeecg.modules.knowledge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.common.util.StringUtils;
import org.jeecg.modules.knowledge.entity.SdlCodingStandard;
import org.jeecg.modules.knowledge.mapper.SdlCodingStandardMapper;
import org.jeecg.modules.knowledge.service.ISdlCodingStandardService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 安全编码合规要求
 * @Author: jeecg-boot
 * @Date:   2021-08-01
 * @Version: V1.0
 */
@Service
public class SdlCodingStandardServiceImpl extends ServiceImpl<SdlCodingStandardMapper, SdlCodingStandard> implements ISdlCodingStandardService {

    @Override
    public SdlCodingStandard getSdlCodingStandardNameOrDefault(String name, String id) {
        SdlCodingStandard sdlCodingStandard = null;
        if (StringUtils.isNotBlank(name)) {
            sdlCodingStandard = this.getOne(new LambdaQueryWrapper<SdlCodingStandard>().eq(SdlCodingStandard::getStandardName, name));
        }
        if (sdlCodingStandard == null && StringUtils.isNotBlank(id)) {
            sdlCodingStandard = this.getById(id);
        }
        if (sdlCodingStandard == null) {
            sdlCodingStandard = this.getOne(new LambdaQueryWrapper<SdlCodingStandard>().eq(SdlCodingStandard::getActiveStatus, 1));
        }
        return sdlCodingStandard;
    }

    @Override
    public String getSdlCodingStandardNameOrDefault(String name) {
        SdlCodingStandard sdlCodingStandardNameOrDefault = getSdlCodingStandardNameOrDefault(name, null);
        if (sdlCodingStandardNameOrDefault != null) {
            return sdlCodingStandardNameOrDefault.getId();
        }
        return null;
    }
}
