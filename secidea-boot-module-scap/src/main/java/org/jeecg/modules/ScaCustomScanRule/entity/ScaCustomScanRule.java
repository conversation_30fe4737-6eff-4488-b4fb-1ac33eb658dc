package org.jeecg.modules.ScaCustomScanRule.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import org.jeecg.common.aspect.annotation.IAdd;
import org.jeecg.common.aspect.annotation.IEdit;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotNull;

/**
 * @Description: 自定义检测规则
 * @Author: Secidea One
 * @Date:   2023-03-02
 * @Version: V4.0
 */
@Data
@TableName("sca_custom_scan_rule")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "sca_custom_scan_rule对象", description = "自定义检测规则")
public class ScaCustomScanRule implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
	@NotNull(message = "id不能为空",groups = {IEdit.class})
    @Schema(name = "主键")
    private String id;
	/**创建人*/
    @Schema(name = "创建人")
    private String createBy;
	/**创建日期*/
    @Schema(name = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(name = "更新人")
    private String updateBy;
	/**更新日期*/
    @Schema(name = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @Schema(name = "所属部门")
    private String sysOrgCode;
	/**规则名称*/
	@Excel(name = "规则名称", width = 15)
    @Schema(name = "规则名称")
    private String name;
    @Excel(name = "缺陷ID", width = 15,dictTable ="sca_risk_knowledge",dicText = "issuetypezh",dicCode = "id")
    @Schema(name = "缺陷表关联ID")
//    @Dict(dictTable = "sca_risk_knowledge", dicText = "issuetypezh", dicCode = "id")
    @NotNull(message = "缺陷ID不能为空",groups = {IAdd.class,IEdit.class})
    private String riskKnowledgeId;
    /**风险中文名(name_cn)，对应默认知识库里面的风险中文名*/
    @Excel(name = "缺陷名称", width = 15)
    @Schema(name = "缺陷名称")
    private String riskNameZh;
	/**风险英文名(name_en)，对应默认知识库里面的风险英文名*/
	@Excel(name = "缺陷英文名", width = 15)
    @Schema(name = "缺陷英文名")
    private String riskNameEn;
	/**启用的语言+新增语言（配置类）  数据来自 上面language*/
	@Excel(name = "启用语言", width = 15)
    @Schema(name = "启用语言")
//    @Dict(dicCode = "sca_custom_scan_rule_language")
    @Dict(dictTable = "sca_language", dicText = "language", dicCode = "languagelower" , statusField = "enabled" ,status = "1 or 4")
    @NotNull(message = "语言不能为空",groups = {IAdd.class,IEdit.class})
    private String language;
	/**规则表达式(match)*/
	@Excel(name = "规则表达式", width = 15)
    @Schema(name = "规则表达式")
    @NotNull(message = "语言不能为空",groups = {IAdd.class,IEdit.class})
    private String matchExpression;
	/**规则表达式2的option   1：in-current-line  2：in-file  3：in-file-up  4：in-file-down*/
	@Excel(name = "规则表达式2的匹配模式", width = 15)
    @Dict(dicCode = "sca_custom_scan_rule_option")
    @Schema(name = "规则表达式2的匹配模式")
    @TableField(updateStrategy= FieldStrategy.IGNORED)
    private Integer match2Option;
	/**规则表达式2(match2)*/
	@Excel(name = "规则表达式2", width = 15)
    @Schema(name = "规则表达式2")
    @TableField(updateStrategy= FieldStrategy.IGNORED)
    private String match2Expression;
	/**修复规则1的option*/
	@Excel(name = "修复规则1的匹配模式", width = 15)
    @Dict(dicCode = "sca_custom_scan_rule_option")
    @Schema(name = "修复规则1的匹配模式")
    @TableField(updateStrategy= FieldStrategy.IGNORED)
    private Integer repairOption;
	/**规则表达式2(match2)*/
	@Excel(name = "规则表达式2", width = 15)
    @Schema(name = "规则表达式2")
    @TableField(updateStrategy= FieldStrategy.IGNORED)
    private String repairExpression;
	/**修复规则2的option*/
	@Excel(name = "修复规则2的匹配模式", width = 15)
    @Dict(dicCode = "sca_custom_scan_rule_option")
    @Schema(name = "修复规则2的匹配模式")
    @TableField(updateStrategy= FieldStrategy.IGNORED)
    private Integer repair2Option;
	/**修复规则2*/
	@Excel(name = "修复规则2", width = 15)
    @Schema(name = "修复规则2")
    @TableField(updateStrategy= FieldStrategy.IGNORED)
    private String repair2Expression;
	/**风险等级(level)   4：高危  3：中危  2：低危*/
	@Excel(name = "缺陷等级", width = 15, dicCode = "code_risk_level_default")
    @Dict(dicCode = "code_risk_level_default")
    @Schema(name = "缺陷等级(level)   4：高危  3：中危  2：低危")
    @NotNull(message = "缺陷等级不能为空",groups = {IAdd.class,IEdit.class})
    private Integer riskLevel;
	/**启用状态*/
	@Excel(name = "启用状态", width = 15,dicCode = "yn")
    @Dict(dicCode = "yn")
    @Schema(name = "启用状态")
    @NotNull(message = "语言不能为空",groups = {IAdd.class,IEdit.class})
    private Integer status;
	/**大小写敏感(case)*/
	@Excel(name = "大小写敏感", width = 15,dicCode = "yn")
    @Dict(dicCode = "yn")
    @Schema(name = "大小写敏感(case)")
    @NotNull(message = "大小写敏感不能为空",groups = {IAdd.class,IEdit.class})
    private Integer caseSensitive;
    @Excel(name = "备注", width = 15)
    @Schema(name = "备注")
    @TableField(updateStrategy= FieldStrategy.IGNORED)
    private String description;
}
