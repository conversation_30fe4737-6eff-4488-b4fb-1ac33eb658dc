package org.jeecg.modules.ScaCustomScanRule.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.aspect.annotation.IEdit;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.ScaCustomScanRule.entity.ScaCustomScanRule;
import org.jeecg.modules.ScaCustomScanRule.service.IScaCustomScanRuleService;
import org.jeecg.modules.base.entity.IdsParam;
import org.jeecg.modules.base.entity.UpdateStatusParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;

 /**
 * @Description: 自定义检测规则
 * @Author: Secidea One
 * @Date:   2023-03-02
 * @Version: V4.0
 */
@Tag(name = "自定义检测规则")
@RestController
@RequestMapping("/scaCustomScanRule")
@Slf4j
public class ScaCustomScanRuleController extends JeecgController<ScaCustomScanRule, IScaCustomScanRuleService> {
	@Autowired
	private IScaCustomScanRuleService scaCustomScanRuleService;

	/**
	 * 分页列表查询
	 * @param scaCustomScanRule
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	// @AutoLog(value = "自定义检测规则-分页列表查询")
	@Operation(summary = "自定义检测规则-分页列表查询", description = "自定义检测规则-分页列表查询")
	@GetMapping(value = "/list")
	@RequiresPermissions(value = "sca:custom:scan:rule:list")
	public Result<?> queryPageList(ScaCustomScanRule scaCustomScanRule,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<ScaCustomScanRule> queryWrapper = QueryGenerator.initQueryWrapper(scaCustomScanRule, req.getParameterMap());
		Page<ScaCustomScanRule> page = new Page<ScaCustomScanRule>(pageNo, pageSize);
//		addUserFiltering(queryWrapper);
		IPage<ScaCustomScanRule> pageList = scaCustomScanRuleService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 * 添加
	 * @param scaCustomScanRule
	 * @return
	 */
	@AutoLog(value = "自定义检测规则-添加")
	@Operation(summary = "自定义检测规则-添加", description = "自定义检测规则-添加")
	@PostMapping(value = "/add")
	@RequiresPermissions(value = "sca:custom:scan:rule:add")
	public Result<?> add(@RequestBody ScaCustomScanRule scaCustomScanRule) {
		scaCustomScanRuleService.save(scaCustomScanRule);
		return Result.OK("添加成功！");
	}

	/**
	 * 编辑
	 * @param scaCustomScanRule
	 * @return
	 */
	@AutoLog(value = "自定义检测规则-编辑")
	@Operation(summary = "自定义检测规则-编辑", description = "自定义检测规则-编辑")
	@PutMapping(value = "/edit")
	@RequiresPermissions(value = "sca:custom:scan:rule:edit")
	public Result<?> edit(@RequestBody @Validated(value = {IEdit.class}) ScaCustomScanRule scaCustomScanRule) {
//	    checkCanUseId(scaCustomScanRule.getId());
		scaCustomScanRuleService.updateById(scaCustomScanRule);
		return Result.OK("编辑成功!");
	}

	/**
	 * 通过id删除
	 * @param id
	 * @return
	 */
	@AutoLog(value = "自定义检测规则-通过id删除")
	@Operation(summary = "自定义检测规则-通过id删除", description = "自定义检测规则-通过id删除")
	@DeleteMapping(value = "/delete")
	@RequiresPermissions(value = "sca:custom:scan:rule:delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
//	    checkCanUseId(id);
		scaCustomScanRuleService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 * 批量删除
	 * @param idsParam
	 * @return
	 */
	@AutoLog(value = "自定义检测规则-批量删除")
	@Operation(summary = "自定义检测规则-批量删除", description = "自定义检测规则-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	@RequiresPermissions(value = "sca:custom:scan:rule:delete")
	public Result<?> deleteBatch(@RequestBody @Validated IdsParam idsParam) {
//	    checkCanUseIds(ids);
		this.scaCustomScanRuleService.removeByIds(Arrays.asList(idsParam.getIds().split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 * @param id
	 * @return
	 */
	// @AutoLog(value = "自定义检测规则-通过id查询")
	@Operation(summary = "自定义检测规则-通过id查询", description = "自定义检测规则-通过id查询")
	@GetMapping(value = "/queryById")
	@RequiresPermissions(value = "sca:custom:scan:rule:list")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		ScaCustomScanRule scaCustomScanRule = scaCustomScanRuleService.getById(id);
		if(scaCustomScanRule==null) {
			return Result.error("未找到对应数据");
		}
//		checkCanUseId(id);
		return Result.OK(scaCustomScanRule);
	}

    /**
    * 导出excel
    * @param jsonObject
    */
    @PostMapping(value = "/exportXls")
    @RequiresPermissions(value = "sca:custom:scan:rule:export")
    public ModelAndView exportXls(@RequestBody JSONObject jsonObject) {
        return super.exportXls(jsonObject, ScaCustomScanRule.class, "自定义检测规则");
    }

    /**
      * 通过excel导入数据
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    @RequiresPermissions(value = "sca:custom:scan:rule:import")
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ScaCustomScanRule.class);
    }


	 @PutMapping(value = "/updateStatus")
	 @RequiresPermissions(value = "sca:custom:scan:rule:edit")
	 public Result<?> updateStatus(@RequestBody UpdateStatusParam updateStatusParam) {
    	 scaCustomScanRuleService.updateStatus(new ArrayList<>(Arrays.asList(updateStatusParam.getIds().split(","))),updateStatusParam.getStatus());
//		 return super.updateStatus(updateStatusParam, ScaCustomScanRule.class, false);
		 return Result.OK("状态更新成功!");
	 }
 }
