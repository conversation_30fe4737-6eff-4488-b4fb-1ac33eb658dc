package org.jeecg.modules.ScaCustomScanRule.mapper;

import org.jeecg.modules.ScaCustomScanRule.entity.ScaCustomScanRule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * @Description: 自定义检测规则
 * @Author: Secidea One
 * @Date:   2023-03-02
 * @Version: V4.0
 */
public interface ScaCustomScanRuleMapper extends BaseMapper<ScaCustomScanRule> {

    /**
     * 更新状态
     * @param idList id列表
     * @param status 状态
     */
    void updateStatus(List<String> idList, Integer status);;

}
