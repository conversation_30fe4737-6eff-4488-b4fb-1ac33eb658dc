<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.ScaCustomScanRule.mapper.ScaCustomScanRuleMapper">

    <update id="updateStatus">
        update sca_custom_scan_rule set status = #{status} where id in
        <foreach collection="idList" item="id" open="(" close=")" separator="," >
            #{id}
        </foreach>
    </update>

</mapper>
