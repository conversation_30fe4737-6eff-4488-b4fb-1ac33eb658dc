package org.jeecg.modules.ScaCustomScanRule.service.impl;

import org.jeecg.modules.ScaCustomScanRule.entity.ScaCustomScanRule;
import org.jeecg.modules.ScaCustomScanRule.mapper.ScaCustomScanRuleMapper;
import org.jeecg.modules.ScaCustomScanRule.service.IScaCustomScanRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;

/**
 * @Description: 自定义检测规则
 * @Author: Secidea One
 * @Date:   2023-03-02
 * @Version: V4.0
 */
@Service
public class ScaCustomScanRuleServiceImpl extends ServiceImpl<ScaCustomScanRuleMapper, ScaCustomScanRule> implements IScaCustomScanRuleService {

    @Autowired
    private ScaCustomScanRuleMapper scaCustomScanRuleMapper;
    @Override
    public void updateStatus(List<String> idList, Integer status){
        scaCustomScanRuleMapper.updateStatus(idList,status);
    }
}
