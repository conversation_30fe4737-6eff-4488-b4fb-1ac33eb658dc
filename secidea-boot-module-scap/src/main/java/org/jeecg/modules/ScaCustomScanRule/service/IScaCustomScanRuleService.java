package org.jeecg.modules.ScaCustomScanRule.service;

import org.jeecg.modules.ScaCustomScanRule.entity.ScaCustomScanRule;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.base.entity.UpdateStatusParam;

import java.util.List;

/**
 * @Description: 自定义检测规则
 * @Author: Secidea One
 * @Date:   2023-03-02
 * @Version: V4.0
 */
public interface IScaCustomScanRuleService extends IService<ScaCustomScanRule> {

    /**
     * 更新状态
     * @param updateStatusParam
     */
    void updateStatus(List<String> idList, Integer status);

}
