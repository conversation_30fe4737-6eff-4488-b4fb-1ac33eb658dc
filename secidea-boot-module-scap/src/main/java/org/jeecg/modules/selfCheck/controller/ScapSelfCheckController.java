package org.jeecg.modules.selfCheck.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.StringUtils;
import org.jeecg.common.xxljob.entity.Resp;
import org.jeecg.common.xxljob.service.XxlJobService;
import org.jeecg.modules.sectest.scap.entity.ScaProjectVersion;
import org.jeecg.modules.sectest.scap.service.IScaProjectService;
import org.jeecg.modules.sectest.scap.service.IScaProjectVersionService;
import org.jeecg.modules.selfCheck.handler.JobSelfCheckHandler;
import org.jeecg.modules.selfCheck.handler.ReportTaskSelfCheckHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Scap 数据完整性自查接口
 * <AUTHOR>
 */
@RestController
@RequestMapping("/selfCheck")
@Slf4j
public class ScapSelfCheckController {

    @Autowired
    private ReportTaskSelfCheckHandler reportTaskSelfCheckHandler;
    @Autowired
    private XxlJobService xxlJobService;
    @Autowired
    private JobSelfCheckHandler jobSelfCheckHandler;
    @Autowired
    private IScaProjectService scaProjectService;
    @Autowired
    private IScaProjectVersionService scaProjectVersionService;

    @GetMapping("/reportTask")
    public Result<?> reportTask(){
        reportTaskSelfCheckHandler.handler();
        return Result.OK("报告自查完毕");
    }

    @GetMapping("/job")
    public Result<?> job(){
        Resp appNameIdByAppname = xxlJobService.getAppNameIdByAppname();
        if (appNameIdByAppname.getCode() == 404) {
            return Result.error("定时任务服务未启动,请联系管理员!");
        }
        jobSelfCheckHandler.handler();
        return Result.OK("定时任务自查完毕");
    }

    @GetMapping("/updateProjectRectifyTime")
    public Result<?> updateProjectRectifyTime(@RequestParam(name="projectVersionId",required=false) String projectVersionId){
        long time = System.currentTimeMillis();
        if ( !StringUtils.isBlank(projectVersionId) ){
            scaProjectService.updateRectifyTimeByProjectVersionId(projectVersionId);
            return Result.OK(String.format("整改期限修改完毕，耗时 %s ms",(System.currentTimeMillis()-time)));
        }
        List<ScaProjectVersion> scaProjectVersionList = scaProjectVersionService.list(new LambdaQueryWrapper<ScaProjectVersion>().eq(ScaProjectVersion::getStatus,4).and(i -> i.eq(ScaProjectVersion::getSign, 1).or().isNull(ScaProjectVersion::getSign)).select(ScaProjectVersion::getProjectversionid));
        if ( !scaProjectVersionList.isEmpty() ){
            for (ScaProjectVersion scaProjectVersion : scaProjectVersionList) {
                scaProjectService.updateRectifyTimeByProjectVersionId(String.valueOf(scaProjectVersion.getProjectversionid()));
            }
        }
        return Result.OK(String.format("整改期限修改完毕，耗时 %s ms",(System.currentTimeMillis()-time)));
    }

}
