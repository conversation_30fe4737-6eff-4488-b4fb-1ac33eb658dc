package org.jeecg.modules.selfCheck.handler;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.dto.message.XxlJobInfo;
import org.jeecg.common.xxljob.entity.Resp;
import org.jeecg.common.xxljob.service.XxlJobService;
import org.jeecg.modules.sectest.scap.entity.ScaProject;
import org.jeecg.modules.sectest.scap.service.IScaProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class JobSelfCheckHandler {

    @Autowired
    private IScaProjectService scaProjectService;
    @Autowired
    private XxlJobService xxlJobService;

    public void handler(){
        handler(null);
    }

    @XxlJob(value = "jobCheckHandler")
    public ReturnT<String> handler(String param){
        log.info("----------------定时任务自查开始处理-----------------: ");
        Resp resp = xxlJobService.loadJobInfoByAppName();
        List<String> jobIdList = new ArrayList<>();
        Map<String,XxlJobInfo> xxlJobInfoMap = new HashMap<>();
        if ( 200 == resp.getCode() ) {
            JSONObject resultJson = JSONObject.parseObject(resp.getRespBody().toString());
            JSONArray allJobTaskList = resultJson.getJSONArray("content");
            if ( allJobTaskList.size() > 0 ){
                allJobTaskList.forEach(o->{
                    XxlJobInfo xxlJobInfo = JSONObject.parseObject(o.toString(),XxlJobInfo.class);
                    if ( xxlJobInfo.getJobDesc().startsWith("代码检测：") || xxlJobInfo.getJobDesc().startsWith("代码检测：") ){
                        String id = String.valueOf(xxlJobInfo.getId());
                        jobIdList.add(id);
                        xxlJobInfoMap.put(id,xxlJobInfo);
                    }
                });
            }
        } else {
            return ReturnT.FAIL;
        }
        LambdaQueryWrapper<ScaProject> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.isNotNull(ScaProject::getXxlJobInfoId).or().ne(ScaProject::getXxlJobInfoId,"");
        List<ScaProject> scaProjectList = scaProjectService.list(lambdaQueryWrapper);
        if ( !scaProjectList.isEmpty() ){
            scaProjectList.forEach(o->{
                String id = String.valueOf(o.getXxlJobInfoId());
                if ( o.getIsTiming() == 0 ){
                    // 判断任务是否删除
                    if ( jobIdList.contains(id) ){
                        // 定时任务端存在任务，则删除job的任务
                        xxlJobService.delete(o.getXxlJobInfoId());
                        log.info("------定时任务端存在任务,删除任务ID: " + id);
                    } else {
                        // 定时任务端不存在任务 ， 删除项目标的的job相关信息
                        updateScaProjectByProjectId(o.setIsTiming(0).setXxlJobInfoId(0).setCronExpression(""));
                        log.info("------定时任务端不存在任务,任务ID" + id + " ,项目信息ID: " + o.getProjectid() + " ,项目名称: " + o.getProjectname());
                    }
                } else {
                    // 判断任务表达式是否一致
                    if ( xxlJobInfoMap.containsKey(id) ){
                        XxlJobInfo xxlJobInfo = xxlJobInfoMap.get(id);
                        // 不一致则更新
                        if ( null == xxlJobInfo.getScheduleConf() || !o.getCronExpression().equals(xxlJobInfo.getScheduleConf()) ){
                            xxlJobInfo.setJobCron(o.getCronExpression());
                            xxlJobService.saveXxl(xxlJobInfo);
                            log.info("------定时任务端与项目信息不一致,保存任务ID" + id + " ,项目信息ID: " + o.getProjectid() + " ,项目名称: " + o.getProjectname());
                        }
                        // 启动被停止的定时任务，保证可能因为状态不一致导致没有执行
                        xxlJobService.start(o.getXxlJobInfoId());
                    } else {
                        // 不存在则删除任务id
                        updateScaProjectByProjectId(o.setIsTiming(0).setXxlJobInfoId(0).setCronExpression(""));
                        log.info("------定时任务端不存在任务,任务ID" + id + " ,项目信息ID: " + o.getProjectid() + " ,项目名称: " + o.getProjectname());
                    }
                }
                // 将不需要处理的任务id去掉
                if ( jobIdList.contains(id) ){
                    jobIdList.remove(id);
                }
            });
        }
        // 最后将任务重多余的删了
        if ( !jobIdList.isEmpty() ){
            jobIdList.forEach(o->{
                xxlJobService.delete(Integer.valueOf(o));
            });
            log.info("------已删除多余定时任务ID: " + jobIdList);
        }
        log.info("----------------定时任务自查处理完毕-----------------: ");
        return ReturnT.SUCCESS;
    }

    private void updateScaProjectByProjectId(ScaProject scaProject) {
        scaProjectService.update(scaProject, new LambdaQueryWrapper<ScaProject>().eq(ScaProject::getProjectid, scaProject.getProjectid()));
    }
}
