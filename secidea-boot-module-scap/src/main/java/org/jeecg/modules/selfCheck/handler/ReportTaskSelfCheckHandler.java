package org.jeecg.modules.selfCheck.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.StringUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.knowledge.entity.SdlCodingStandard;
import org.jeecg.modules.knowledge.service.ISdlCodingStandardService;
import org.jeecg.modules.sectest.report.entity.ScaSavedReport;
import org.jeecg.modules.sectest.report.service.IScaSavedReportService;
import org.jeecg.modules.sectest.scap.entity.ScaProjectVersion;
import org.jeecg.modules.sectest.scap.service.IScaProjectVersionService;
import org.jeecg.modules.sectest.scap.utils.report.ReportParam;
import org.jeecg.modules.sectest.scap.utils.report.ReportTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.io.File;
import java.util.List;

/**
 * Scap 报告数据完整性自查接口
 * <AUTHOR>
 */
@Component
@Slf4j
public class ReportTaskSelfCheckHandler {

    @Value("${jeecg.path.upload}")
    private String upLoadPath;
    @Autowired
    private ReportTask reportTask;

    @Autowired
    private IScaSavedReportService scaSavedReportService;
    @Autowired
    private IScaProjectVersionService scaProjectVersionService;
    @Autowired
    private ISdlCodingStandardService sdlCodingStandardService;

    public void handler(){
        handler(null);
    }

    @XxlJob(value = "reportTaskSelfCheckHandler")
    public ReturnT<String> handler(String param){
        LambdaQueryWrapper<ScaSavedReport> lambdaQueryWrapper = new LambdaQueryWrapper<ScaSavedReport>();
        lambdaQueryWrapper.eq(ScaSavedReport::getStatus,3);
        // 先找到所有生成成功的记录
        List<ScaSavedReport> scaSavedReportList = scaSavedReportService.list(lambdaQueryWrapper);
        if ( !scaSavedReportList.isEmpty() ){
            log.info("------报告自查执行中------");
            scaSavedReportList.forEach(o->{
                // 判断文件是否存在 不存在则补充历史记录
                if ( StringUtils.isBlank(o.getReportpath()) || (!StringUtils.isBlank(o.getReportpath()) && !new File(upLoadPath + "/" + o.getReportpath()).exists() ) ){
                    // 判断任务id和项目是否存在
                    if ( scaProjectVersionService.count(new LambdaQueryWrapper<ScaProjectVersion>().eq(ScaProjectVersion::getProjectversionid,o.getProjectversionid())) == 0 ){
                        scaSavedReportService.removeById(o.getId());
                    } else {
                        log.info("---修复文件路径：" + o.getReportpath());
                        // 清理文件路径，重新生成
                        ReportParam reportParam = new ReportParam();
                        // 自定义参数传入
                        reportParam.setReportCodeType("0");
                        reportParam.setRiskLevelList("3,4");
                        // 原报告参数
                        reportParam.setReportType(o.getReporttype());
                        reportParam.setReportFileType(o.getFormat());
                        reportParam.setScapTaskId(String.valueOf(o.getProjectversionid()));
                        String standardType = "1478263567340830722";
                        SdlCodingStandard sdlCodingStandard = sdlCodingStandardService.getOne(new LambdaQueryWrapper<SdlCodingStandard>().eq(SdlCodingStandard::getStandardName,o.getTemplates()));
                        if ( !oConvertUtils.isEmpty(sdlCodingStandard) ){
                            standardType = sdlCodingStandard.getId();
                        }
                        reportParam.setStandardType(standardType);
                        reportTask.generateScapReportSync(reportParam,o.getId());
                    }

                }
            });
            log.info("------报告自查执行完成------");
        }
        return ReturnT.SUCCESS;
    }

}
