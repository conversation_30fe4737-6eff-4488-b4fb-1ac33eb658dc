package org.jeecg.modules.scaAuditHistory.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.aspect.annotation.IEdit;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.StringUtils;
import org.jeecg.common.util.encryption.SimpleEncryptionUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.base.entity.IdsParam;
import org.jeecg.modules.scaAuditHistory.entity.ScaAuditHistory;
import org.jeecg.modules.scaAuditHistory.service.IScaAuditHistoryService;
import org.jeecg.modules.sectest.scap.entity.ScaProject;
import org.jeecg.modules.sectest.scap.entity.ScaProjectVersion;
import org.jeecg.modules.sectest.scap.entity.ScaScanResult;
import org.jeecg.modules.sectest.scap.service.IScaProjectService;
import org.jeecg.modules.sectest.scap.service.IScaProjectVersionService;
import org.jeecg.modules.sectest.scap.service.IScaScanResultService;
import org.jeecg.modules.sectest.scap.service.IScaTaskResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

 /**
 * @Description: sca_audit_history
 * @Author: Secidea One
 * @Date:   2022-06-02
 * @Version: V4.0
 */
@Tag(name = "sca_audit_history")
@RestController
@RequestMapping("/scaAuditHistory")
@Slf4j
public class ScaAuditHistoryController extends JeecgController<ScaAuditHistory, IScaAuditHistoryService> {
	@Autowired
	private IScaAuditHistoryService scaAuditHistoryService;
	@Autowired
	private IScaProjectService scaProjectService;
	@Autowired
	private IScaProjectVersionService scaProjectVersionService;
	@Autowired
	private IScaTaskResultService scaTaskResultService;
	@Autowired
	private IScaScanResultService scaScanResultService;

	/**
	 * 分页列表查询
	 *
	 * @param scaAuditHistory
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
//	@AutoLog(value = "代码审核历史记录-分页列表查询")
	@Operation(summary = "代码审核历史记录-分页列表查询", description = "代码审核历史记录-分页列表查询")
	@GetMapping(value = "/list")
	@RequiresPermissions(value = {"scap:audit:history:list", "sca:scan:result:auth:log"}, logical = Logical.OR)
	public Result<?> queryPageList(ScaAuditHistory scaAuditHistory,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		String projectVersionId = scaAuditHistory.getProjectVersionId();
		if ( !oConvertUtils.isEmpty(projectVersionId) ){
			projectVersionId = SimpleEncryptionUtil.decryptId(projectVersionId);
			scaAuditHistory.setProjectVersionId(null);
		}
		QueryWrapper<ScaAuditHistory> queryWrapper = QueryGenerator.initQueryWrapper(scaAuditHistory, req.getParameterMap());
		Page<ScaAuditHistory> page = new Page<ScaAuditHistory>(pageNo, pageSize);
		// TODO 这里如果是查询指定任务的（不添加公用查询条件，仅效验所属任务是否可以查询即可）
		if ( StringUtils.isBlank(scaAuditHistory.getIssueId()) ){
			addUserFiltering(queryWrapper);
		} else {
			ScaScanResult scaScanResult = scaScanResultService.getById(scaAuditHistory.getIssueId());
			if ( oConvertUtils.isEmpty(scaScanResult) ){
				return Result.error("不存在的任务");
			}
		}
		String projectName = req.getParameter("projectName");
		if ( !StringUtils.isBlank(projectName) ){
			QueryWrapper<ScaProject> scaProjectQueryWrapper = new QueryWrapper<ScaProject>();
			scaProjectQueryWrapper.lambda().like(ScaProject::getProjectname,projectName);
//			addUserGroupFiltering2(scaProjectQueryWrapper);
			List<ScaProject> scaProjectList = scaProjectService.list(scaProjectQueryWrapper.lambda().orderByDesc(ScaProject::getCreationdate).select(ScaProject::getProjectid));
			if ( scaProjectList.isEmpty() ){
				queryWrapper.lambda().eq(ScaAuditHistory::getProjectId,null);
			} else {
				queryWrapper.lambda().in(ScaAuditHistory::getProjectId,scaProjectList.stream().map(ScaProject::getProjectid).collect(Collectors.toList()));
			}
		}
		String projectVersionName = req.getParameter("projectVersionName");
		if ( !StringUtils.isBlank(projectVersionName) ){
			QueryWrapper<ScaProjectVersion> scaProjectVersionQueryWrapper = new QueryWrapper<ScaProjectVersion>();
			scaProjectVersionQueryWrapper.lambda().like(ScaProjectVersion::getProjectversionname,projectVersionName);
//			addUserGroupFiltering2(scaProjectVersionQueryWrapper);
			List<ScaProjectVersion> scaProjectVersionList = scaProjectVersionService.list(scaProjectVersionQueryWrapper.lambda().orderByDesc(ScaProjectVersion::getCreationdate).select(ScaProjectVersion::getProjectversionid));
			if ( scaProjectVersionList.isEmpty() ){
				queryWrapper.lambda().eq(ScaAuditHistory::getProjectVersionId,null);
			} else {
				queryWrapper.lambda().in(ScaAuditHistory::getProjectVersionId,scaProjectVersionList.stream().map(ScaProjectVersion::getProjectversionid).collect(Collectors.toList()));
			}
		}
		if (!oConvertUtils.isEmpty(projectVersionId) && StringUtils.isBlank(scaAuditHistory.getIssueId())) {
			String createTimeBegin = req.getParameter("createTime_begin");
			String createTimeEnd = req.getParameter("createTime_end");
			Page<ScaAuditHistory> pageList = new Page<>(pageNo,pageSize);
			pageList = scaAuditHistoryService.queryPageList(pageList, projectVersionId, scaAuditHistory, createTimeBegin, createTimeEnd);
			return Result.OK(pageList);
		}
		IPage<ScaAuditHistory> pageList = scaAuditHistoryService.page(page, queryWrapper);
		return Result.OK(pageList);
	}


	/**
	 *   添加
	 *
	 * @param scaAuditHistory
	 * @return
	 */
//	@AutoLog(value = "代码审核历史记录-添加")
//	@Operation(summary = "代码审核历史记录-添加", description = "代码审核历史记录-添加")
//	@PostMapping(value = "/add")
//	@RequiresPermissions(value = "scap:audit:history:add")
	public Result<?> add(@RequestBody ScaAuditHistory scaAuditHistory) {
		scaAuditHistoryService.save(scaAuditHistory);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param scaAuditHistory
	 * @return
	 */
//	@AutoLog(value = "代码审核历史记录-编辑")
//	@Operation(summary = "代码审核历史记录-编辑", description = "代码审核历史记录-编辑")
//	@PutMapping(value = "/edit")
//	@RequiresPermissions(value = "scap:audit:history:edit")
	public Result<?> edit(@RequestBody @Validated(value = {IEdit.class}) ScaAuditHistory scaAuditHistory) {
		scaAuditHistoryService.updateById(scaAuditHistory);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "代码审核历史记录-通过id删除")
	@Operation(summary = "代码审核历史记录-通过id删除", description = "代码审核历史记录-通过id删除")
	@DeleteMapping(value = "/delete")
	@RequiresPermissions(value = "scap:audit:history:delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		scaAuditHistoryService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param idsParam
	 * @return
	 */
	@AutoLog(value = "代码审核历史记录-批量删除")
	@Operation(summary = "代码审核历史记录-批量删除", description = "代码审核历史记录-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	@RequiresPermissions(value = "scap:audit:history:delete")
	public Result<?> deleteBatch(@RequestBody @Validated IdsParam idsParam) {
		this.scaAuditHistoryService.removeByIds(Arrays.asList(idsParam.getIds().split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
//	@AutoLog(value = "代码审核历史记录-通过id查询")
	@Operation(summary = "代码审核历史记录-通过id查询", description = "代码审核历史记录-通过id查询")
	@GetMapping(value = "/queryById")
	@RequiresPermissions(value = "scap:audit:history:list")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		ScaAuditHistory scaAuditHistory = scaAuditHistoryService.getById(id);
		if(scaAuditHistory==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(scaAuditHistory);
	}

    /**
    * 导出excel
    *
    * @param jsonObject
    */
    @PostMapping(value = "/exportXls")
    @RequiresPermissions(value = "scap:audit:history:export")
    public ModelAndView exportXls(@RequestBody JSONObject jsonObject) {
        return super.exportXls(jsonObject, ScaAuditHistory.class, "源码审核日志");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
//    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
//    @RequiresPermissions(value = "scap:audit:history:import")
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ScaAuditHistory.class);
    }

}
