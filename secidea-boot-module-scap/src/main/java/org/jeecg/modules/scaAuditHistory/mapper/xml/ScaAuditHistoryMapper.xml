<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.scaAuditHistory.mapper.ScaAuditHistoryMapper">
    <select id="queryPageList" parameterType="org.jeecg.modules.scaAuditHistory.entity.ScaAuditHistory"  resultType="org.jeecg.modules.scaAuditHistory.entity.ScaAuditHistory">
        select h.* from sca_task_result r,sca_audit_history h
        where r.scap_result_id = h.issue_id
        <if test="taskId !=null and taskId!=''">
            and r.scap_task_id = #{taskId}
        </if>
        <if test="params.issueZhType!=null and params.issueZhType!=''">
            and h.issue_zh_type like concat(concat('%',#{params.issueZhType}),'%')
        </if>
        <if test="params.issuePath!=null and params.issuePath!=''">
            and h.issue_path like concat(concat('%',#{params.issuePath}),'%')
        </if>
        <if test="params.createBy!=null and params.createBy!=''">
            and h.create_by like concat(concat('%',#{params.createBy}),'%')
        </if>
        <if test="createTimeBegin !=null and createTimeBegin!=''">
            and h.create_time &gt;= #{createTimeBegin}
        </if>
        <if test="createTimeEnd !=null and createTimeEnd!=''">
            and h.create_time &lt;= #{createTimeEnd}
        </if>
        ORDER BY h.create_time desc, h.id desc
    </select>
</mapper>
