package org.jeecg.modules.scaAuditHistory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.scaAuditHistory.entity.ScaAuditHistory;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: sca_audit_history
 * @Author: Secidea One
 * @Date:   2022-06-02
 * @Version: V4.0
 */
public interface IScaAuditHistoryService extends IService<ScaAuditHistory> {
    Page<ScaAuditHistory> queryPageList(Page<ScaAuditHistory> page, String taskId, ScaAuditHistory scaAuditHistory,String createTimeBegin,String createTimeEnd);
}
