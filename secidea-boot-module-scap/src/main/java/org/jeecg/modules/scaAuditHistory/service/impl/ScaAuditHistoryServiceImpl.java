package org.jeecg.modules.scaAuditHistory.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.scaAuditHistory.entity.ScaAuditHistory;
import org.jeecg.modules.scaAuditHistory.mapper.ScaAuditHistoryMapper;
import org.jeecg.modules.scaAuditHistory.service.IScaAuditHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: sca_audit_history
 * @Author: Secidea One
 * @Date:   2022-06-02
 * @Version: V4.0
 */
@Service
public class ScaAuditHistoryServiceImpl extends ServiceImpl<ScaAuditHistoryMapper, ScaAuditHistory> implements IScaAuditHistoryService {

    @Autowired
    private ScaAuditHistoryMapper scaAuditHistoryMapper;
    @Override
    public Page<ScaAuditHistory> queryPageList(Page<ScaAuditHistory> page, String taskId, ScaAuditHistory scaAuditHistory,String createTimeBegin,String createTimeEnd) {
        return page.setRecords(scaAuditHistoryMapper.queryPageList(page, taskId, scaAuditHistory, createTimeBegin, createTimeEnd));
    }
}
