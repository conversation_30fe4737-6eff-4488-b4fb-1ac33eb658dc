package org.jeecg.modules.scaAuditHistory.mapper;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.scaAuditHistory.entity.ScaAuditHistory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: sca_audit_history
 * @Author: Secidea One
 * @Date:   2022-06-02
 * @Version: V4.0
 */
public interface ScaAuditHistoryMapper extends BaseMapper<ScaAuditHistory> {
    List<ScaAuditHistory> queryPageList(Page<ScaAuditHistory> page, @Param("taskId") String taskId,@Param("params")  ScaAuditHistory scaAuditHistory,@Param("createTimeBegin") String createTimeBegin,@Param("createTimeEnd") String createTimeEnd);
}
