package org.jeecg.modules.scaAuditHistory.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.common.aspect.annotation.IEdit;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description: sca_audit_history
 * @Author: Secidea One
 * @Date:   2022-06-02
 * @Version: V4.0
 */
@Data
@TableName("sca_audit_history")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(name = "sca_audit_history对象", description = "源码审核日志")
public class ScaAuditHistory implements Serializable {
    private static final long serialVersionUID = 1L;

    /**id*/
    @TableId(type = IdType.ASSIGN_ID)
    @NotNull(message = "id不能为空",groups = {IEdit.class})
    @Schema(name = "id")
    private java.lang.String id;
    /**项目id*/
    @Excel(name = "项目id", width = 15, dictTable = "sca_project", dicText = "projectname", dicCode = "projectid")
    @Schema(name = "项目id")
    @Dict(dictTable = "sca_project", dicText = "projectname", dicCode = "projectid")
    private java.lang.String projectId;
    /**任务id*/
    @Excel(name = "任务id", width = 15, dictTable = "sca_project_version", dicText = "projectversionname", dicCode = "projectversionid")
    @Schema(name = "任务id")
    @Dict(dictTable = "sca_project_version", dicText = "projectversionname", dicCode = "projectversionid")
    private java.lang.String projectVersionId;
    /**缺陷英文名*/
    @Excel(name = "缺陷英文名", width = 15)
    @Schema(name = "缺陷英文名")
    private java.lang.String issueEnType;
    /**缺陷名称*/
    @Excel(name = "缺陷名称", width = 15)
    @Schema(name = "缺陷名称")
    private java.lang.String issueZhType;
    /**问题编号*/
    @Excel(name = "问题编号", width = 15)
    @Schema(name = "问题编号")
    private java.lang.String issueId;
    /**原缺陷分类*/
//    @Excel(name = "原缺陷分类", width = 15)
    @Schema(name = "原缺陷分类")
    @Dict(dicCode = "issue_class")
    private java.lang.Integer originalIssueClass;
    /**确认缺陷分类*/
//    @Excel(name = "确认缺陷分类", width = 15)
    @Schema(name = "确认缺陷分类")
    @Dict(dicCode = "issue_class")
    private java.lang.Integer nowIssueClass;
    /**审核说明*/
    @Excel(name = "审核说明", width = 15)
    @Schema(name = "审核说明")
    private java.lang.String changeDesc;
//    @Excel(name = "审核说明", width = 15)
    @Schema(name = "审核说明")
    private java.lang.String description;
    /**原缺陷等级*/
    @Excel(name = "原缺陷等级", width = 15, dicCode = "code_risk_level")
    @Dict(dicCode = "code_risk_level")
    @Schema(name = "原缺陷等级")
    private java.lang.Integer originalRiskLevel;
    /**确认缺陷等级*/
    @Excel(name = "确认缺陷等级", width = 15, dicCode = "code_risk_level")
    @Dict(dicCode = "code_risk_level")
    @Schema(name = "确认缺陷等级")
    private java.lang.Integer nowRiskLevel;
    @Excel(name = "原确认处理状态", width = 15, dicCode = "process_status")
    @Dict(dicCode = "process_status")
    @Schema(name = "原确认处理状态")
    private java.lang.Integer originalProcessStatus;
    /**处理状态
     1. 未处理（误报、忽略）
     2. 已确认（高中低危）
     3. 已驳回（未修复）*/
    @Excel(name = "确认处理状态", width = 15, dicCode = "process_status")
    @Dict(dicCode = "process_status")
    @Schema(name = "确认处理状态")
    private java.lang.Integer nowProcessStatus;
    /**原始缺陷状态*/
    @Excel(name = "原始缺陷状态", width = 15, dicCode = "scap_process_type")
    @Dict(dicCode = "scap_process_type")
    private java.lang.Integer originalProcessType;
    /**确认缺陷状态*/
    @Excel(name = "确认缺陷状态", width = 15, dicCode = "scap_process_type")
    @Schema(name = "确认缺陷状态")
    @Dict(dicCode = "scap_process_type")
    private java.lang.Integer nowProcessType;
    @Excel(name = "分析类型", width = 15, dicCode = "analyzer_type")
    @Dict(dicCode = "analyzer_type")
    @Schema(name = "分析类型")
    private java.lang.Integer analyzerType;
    /**风险路径*/
    @Excel(name = "风险路径", width = 15)
    @Schema(name = "风险路径")
    private java.lang.String issuePath;
    /**所属群组*/
    @Schema(name = "所属群组")
    private java.lang.String sysOrgCode;
    /**创建人*/
    @Schema(name = "创建人")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private java.lang.String createBy;
    /**更新时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(name = "更新时间")
    private java.util.Date updateTime;
    /**更新人*/
    @Schema(name = "更新人")
    private java.lang.String updateBy;
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(name = "创建时间")
    private java.util.Date createTime;
    /**规则编号*/
    @Schema(name = "规则编号")
    private java.lang.String filterRulesId;
    /**规则名称*/
    @Schema(name = "规则名称")
    private java.lang.String filterRulesRulename;

    @TableField(exist = false)
    private String resultId;

}
