<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.secidea.boot</groupId>
        <artifactId>secidea-boot-parent</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>secidea-boot-module-scap</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.secidea.boot</groupId>
            <artifactId>secidea-boot-base-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.secidea.boot</groupId>
            <artifactId>secidea-boot-starter-cloud</artifactId>
        </dependency>
        <dependency>
            <groupId>com.secidea.boot</groupId>
            <artifactId>secidea-boot-starter-lock</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-openfeign-core</artifactId>
        </dependency>
        <!-- 引入定时任务依赖 -->
        <dependency>
            <groupId>com.secidea.boot</groupId>
            <artifactId>secidea-boot-starter-job</artifactId>
        </dependency>
        <!-- 自封装风险知识库 -->
        <dependency>
            <groupId>com.secidea</groupId>
            <artifactId>scap-risk-knowledge</artifactId>
            <version>1.0</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
