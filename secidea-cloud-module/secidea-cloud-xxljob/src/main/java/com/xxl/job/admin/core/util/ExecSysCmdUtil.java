package com.xxl.job.admin.core.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * Cmd命令执行工具类
 * <AUTHOR>
 */
@Slf4j
public class ExecSysCmdUtil {
    /**
     * 执行系统命令, 返回执行结果
     *
     * @param cmd 需要执行的命令
     * @param dir 执行命令的子进程的工作目录, null 表示和当前主进程工作目录相同
     */
    public static String execCmd(String cmd, File dir) throws Exception {
        StringBuilder result = new StringBuilder();
        Process process = null;
        try {
            // 执行命令, 返回一个子进程对象（命令在子进程中执行）
            process = Runtime.getRuntime().exec(cmd, null, dir);
            printMessage(process.getInputStream(), result);
            printMessage(process.getErrorStream(), result);
            // 方法阻塞, 等待命令执行完成（成功会返回0）
            process.waitFor();
        } finally {
            // 销毁子进程
            if (process != null) {
                process.destroy();
            }
        }

        // 返回执行结果
        log.info("返回执行结果：" + result);
        return result.toString();
    }

    /**
     * 执行系统命令, 返回执行结果
     *
     * @param cmd 需要执行的命令
     * @param dir 执行命令的子进程的工作目录, null 表示和当前主进程工作目录相同
     */
    public static String execCmd(String[] cmd, File dir) throws Exception {
        StringBuilder result = new StringBuilder();
        Process process = null;
        try {
            // 执行命令, 返回一个子进程对象（命令在子进程中执行）
            process = Runtime.getRuntime().exec(cmd, null, dir);
            printMessage(process.getInputStream(), result);
            printMessage(process.getErrorStream(), result);
            // 方法阻塞, 等待命令执行完成（成功会返回0）
            process.waitFor();
        } finally {
            // 销毁子进程
            if (process != null) {
                process.destroy();
            }
        }

        // 返回执行结果
        log.info("返回执行结果：" + result);
        return result.toString();
    }


    private static void printMessage(final InputStream input, StringBuilder result) {
        new Thread(() -> {
            Reader reader;
            reader = new InputStreamReader(input, StandardCharsets.UTF_8);
            BufferedReader bf = new BufferedReader(reader);
            String line;
            try {
                while ((line = bf.readLine()) != null) {
                    result.append(line).append('\n');
                }
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                closeStream(reader);
                closeStream(bf);
            }
        }).start();
    }

    @Async
    public String execAsyncCmd(String cmd, File dir) throws Exception {
        return execCmd(cmd, dir);
    }

    private static void closeStream(Closeable stream) {
        if (stream != null) {
            try {
                stream.close();
            } catch (Exception e) {
                // nothing
            }
        }
    }

}
