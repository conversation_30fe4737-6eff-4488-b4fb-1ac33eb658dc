package com.xxl.job.admin.service;

import com.xxl.job.admin.core.model.XxlJobUser;
import com.xxl.job.core.biz.model.ReturnT;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR> 2019-05-04 22:13:264
 */
public interface LoginService {

    public ReturnT<String> login(HttpServletRequest request, HttpServletResponse response, String username, String password, boolean ifRemember);

    public ReturnT<String> logout(HttpServletRequest request, HttpServletResponse response);

    public XxlJobUser ifLogin(HttpServletRequest request, HttpServletResponse response);

}
