package com.xxl.job.admin.controller;

import com.xxl.job.admin.controller.annotation.PermissionLimit;
import com.xxl.job.admin.controller.interceptor.PermissionInterceptor;
import com.xxl.job.admin.core.cron.CronExpression;
import com.xxl.job.admin.core.model.XxlJobGroup;
import com.xxl.job.admin.core.model.XxlJobInfo;
import com.xxl.job.admin.core.model.XxlJobUser;
import com.xxl.job.admin.core.thread.JobScheduleHelper;
import com.xxl.job.admin.core.util.I18nUtil;
import com.xxl.job.admin.dao.XxlJobGroupDao;
import com.xxl.job.admin.service.LoginService;
import com.xxl.job.admin.service.XxlJobService;
import com.xxl.job.core.biz.model.ReturnT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.Date;

@RestController
@RequestMapping("/api/jobinfo")
public class XxlJobAdminRestController {
    private static Logger logger = LoggerFactory.getLogger(XxlJobAdminRestController.class);

    @Autowired
    private XxlJobService xxlJobService;

    @Autowired
    private LoginService loginService;

    @Autowired
    private XxlJobGroupDao xxlJobGroupDao;

    @RequestMapping(value = "/save",method = RequestMethod.POST)
    @PermissionLimit(limit=false)
    public ReturnT<String> add(@RequestBody(required = true)XxlJobInfo jobInfo, HttpServletRequest request){

        // next trigger time (5s后生效，避开预读周期)
        long nextTriggerTime = 0;
        try {
            Date nextValidTime = new CronExpression(jobInfo.getJobCron()).getNextValidTimeAfter(new Date(System.currentTimeMillis() + JobScheduleHelper.PRE_READ_MS));
            if (nextValidTime == null) {
                return new ReturnT<String>(ReturnT.FAIL_CODE, I18nUtil.getString("jobinfo_field_cron_never_fire"));
            }
            nextTriggerTime = nextValidTime.getTime();
        } catch (ParseException e) {
            logger.error(e.getMessage(), e);
            return new ReturnT<String>(ReturnT.FAIL_CODE, I18nUtil.getString("jobinfo_field_cron_unvalid")+" | "+ e.getMessage());
        }

        jobInfo.setTriggerStatus(1);
        jobInfo.setTriggerLastTime(0);
        jobInfo.setTriggerNextTime(nextTriggerTime);
        // 设置定时任务类型和Corn表达式 2.3+
        jobInfo.setScheduleConf(jobInfo.getJobCron());
        jobInfo.setScheduleType("CRON");
        jobInfo.setMisfireStrategy("DO_NOTHING");
        jobInfo.setUpdateTime(new Date());
        XxlJobUser loginUser = PermissionInterceptor.getLoginUser(request);
        if (jobInfo.getId() == 0) {
            return xxlJobService.add(jobInfo, loginUser);
        } else {
            return xxlJobService.update(jobInfo, loginUser);
        }
    }

    @RequestMapping(value = "/loadJobInfoByAppName",method = RequestMethod.GET)
    @PermissionLimit(limit=false)
    public ReturnT<String> loadJobInfoByAppName(String appName) {
        return new ReturnT(xxlJobService.loadJobInfoByAppName(appName));
//        return xxlJobService.loadJobInfoByAppName(appName);
    }

    @RequestMapping(value = "/loadById",method = RequestMethod.GET)
    @PermissionLimit(limit=false)
    public XxlJobInfo loadById(int id) {
        return xxlJobService.loadById(id);
    }

    @RequestMapping(value = "/delete",method = RequestMethod.GET)
    @PermissionLimit(limit=false)
    public ReturnT<String> delete(int id) {
        return xxlJobService.remove(id);
    }

    @RequestMapping(value = "/start",method = RequestMethod.GET)
    @PermissionLimit(limit=false)
    public ReturnT<String> start(int id) {
        return xxlJobService.start(id);
    }

    @RequestMapping(value = "/stop",method = RequestMethod.GET)
    @PermissionLimit(limit=false)
    public ReturnT<String> stop(int id) {
        return xxlJobService.stop(id);
    }

    @RequestMapping(value="login", method=RequestMethod.GET)
    @PermissionLimit(limit=false)
    public ReturnT<String> loginDo(HttpServletRequest request, HttpServletResponse response, String userName, String password, String ifRemember){
        boolean ifRem = (ifRemember!=null && ifRemember.trim().length()>0 && "on".equals(ifRemember))?true:false;
        ReturnT<String> result= loginService.login(request, response, userName, password, ifRem);
        return result;
    }

    @RequestMapping(value="getAppNameIdByAppname", method=RequestMethod.GET)
    @PermissionLimit(limit=false)
    public ReturnT<XxlJobGroup> getAppNameIdByAppname(String appnameParam){
        XxlJobGroup appNameIdByAppname = xxlJobGroupDao.getAppNameIdByAppname(appnameParam);
        return appNameIdByAppname != null ? new ReturnT<XxlJobGroup>(appNameIdByAppname) : new ReturnT<XxlJobGroup>(ReturnT.FAIL_CODE, null);
    }
}
