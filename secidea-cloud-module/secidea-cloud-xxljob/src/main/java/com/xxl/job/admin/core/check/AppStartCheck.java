package com.xxl.job.admin.core.check;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Component;
import javax.sql.DataSource;

/**
 * 项目启动检查
 * <AUTHOR>
 */
@Component
@Slf4j
public class AppStartCheck implements ApplicationContextAware {

    private static ApplicationContext context;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        try {
            context = applicationContext;
            // ===== 在项目初始化bean后检验数据库连接是否
            DataSource dataSource = (DataSource) context.getBean("dataSource");
            dataSource.getConnection().close();
            log.info("数据库正常");
        } catch (Exception e) {
            e.printStackTrace();
            // ===== 当检测数据库连接失败时, 停止项目启动
            log.error("数据库异常，请检查数据库相关参数");
            ConfigurableApplicationContext ctx = (ConfigurableApplicationContext)context;
            ctx.close();
        }
    }

    public ApplicationContext getApplicationContext() {
        return context;
    }

}
