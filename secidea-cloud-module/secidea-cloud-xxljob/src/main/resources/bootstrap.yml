server:
  port: 9080
  servlet:
    context-path: /xxl-job-admin
spring:
  profiles:
    active: job
  main:
    allow-circular-references: true
  application:
    name: secidea-job
  cloud:
    nacos:
      config:
        file-extension: yaml
        prefix: jeecg-xxljob
        namespace: ${NACOS-NAMESPACE}
        server-addr: ${NACOS-SERVER-ADDR}
        username: ${NACOS-USERNAME}
        password: ${NACOS-PASSWORD}
        group: ${NACOS-GROUP}
NACOS-NAMESPACE: d643881d-a94f-49da-a359-b61e04fb5d74
NACOS-SERVER-ADDR: 127.0.0.1:8848
NACOS-USERNAME: nacos
NACOS-PASSWORD: ENC(UEyXm0xe55P3pVzFsUcSG1TNRDhvCIGVQ8s0SXt1Ypov8BviKlUixJUB6gGDO0s0)
NACOS-GROUP: DEFAULT_GROUP
