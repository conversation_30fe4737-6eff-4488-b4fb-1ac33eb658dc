<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>secidea-cloud-module</artifactId>
        <groupId>com.secidea.boot</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>secidea-cloud-system-start</artifactId>
    <description>System项目微服务启动</description>

    <dependencies>
        <!-- 引入jeecg-boot-starter-cloud依赖 -->
        <dependency>
            <groupId>com.secidea.boot</groupId>
            <artifactId>secidea-boot-starter-cloud</artifactId>
            <!--system模块需要排除jeecg-system-cloud-api-->
            <!-- 3.2版本号后，可选择是否排除jeecg-system-cloud-api，不排除会优先通过fegin调用接口 -->
            <exclusions>
                <exclusion>
                    <groupId>com.secidea.boot</groupId>
                    <artifactId>secidea-system-cloud-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.secidea.boot</groupId>
            <artifactId>secidea-boot-module-system</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
