package org.jeecg.common.system.api.fallback;

import com.alibaba.fastjson.JSONObject;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.dto.OnlineAuthDTO;
import org.jeecg.common.api.dto.SaveUserDepartIdsDTO;
import org.jeecg.common.api.dto.message.*;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.*;
import org.jeecg.modules.base.entity.ApplyAccessFileParam;
import org.jeecg.modules.base.entity.SysReportManager;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 进入fallback的方法 检查是否token未设置
 * <AUTHOR>
 */
@Slf4j
public class SysBaseAPIFallback implements ISysBaseAPI {

    @Setter
    private String cause;

    @Override
    public void sendSysAnnouncement(MessageDTO message) {
        log.error("发送消息失败 {}", cause);
    }

    @Override
    public void sendBusAnnouncement(BusMessageDTO message) {
        log.error("发送消息失败 {}", cause);
    }

    @Override
    public void sendTemplateAnnouncement(TemplateMessageDTO message) {
        log.error("发送消息失败 {}", cause);
    }

    @Override
    public void sendBusTemplateAnnouncement(BusTemplateMessageDTO message) {
        log.error("发送消息失败 {}", cause);
    }

    @Override
    public String parseTemplateByCode(TemplateDTO templateDTO) {
        log.error("通过模板获取消息内容失败 {}", cause);
        return null;
    }

    @Override
    public LoginUser getUserById(String id) {
        return null;
    }

    @Override
    public List<String> getRolesByUsername(String username) {
        return null;
    }

    @Override
    public List<String> getRoleCodeByUserId(String userId) {
        return null;
    }

    @Override
    public List<String> getDepartIdsByUsername(String username) {
        return null;
    }

    @Override
    public List<String> getDepartNamesByUsername(String username) {
        return null;
    }

    @Override
    public List<DictModel> queryDictItemsByCode(String code) {
        return null;
    }

    @Override
    public List<DictModel> queryAllDict() {
        return null;
    }

    @Override
    public List<SysCategoryModel> queryAllDSysCategory() {
        return null;
    }

    @Override
    public List<DictModel> queryTableDictItemsByCode(String table, String text, String code) {
        return null;
    }

    @Override
    public String querySysInfoLicense() {
        return null;
    }

    @Override
    public List<String> querySysRoleUser(String code) {
        return null;
    }

    @Override
    public List<JSONObject> querySysUser(String userName) {
        return null;
    }

    @Override
    public List<String> getUsernameListbyOrgCode(String orgCode) {
        return null;
    }

    @Override
    public SysDepositoryConfig getSysDepositoryConfigById(String id) {
        return null;
    }

    @Override
    public SysSourceAccount getSysSourceAccountById(String id) {
        return null;
    }

    @Override
    public SysSourceAccount getSysSourceAccount(SysSourceAccount sysSourceAccount) {
        return null;
    }

    @Override
    public void updateSysSourceAccountById(SysSourceAccount sysSourceAccount) {

    }

    @Override
    public void saveOrUpdateSysSourceAccount(SysSourceAccount sysSourceAccount) {

    }


    @Override
    public SysCodeRepository getSysCodeRepositoryById(String id) {
        return null;
    }

    @Override
    public SysCodeRepository getSysCodeRepository(SysCodeRepository sysCodeRepository) {
        return null;
    }

    @Override
    public void updateSysCodeRepositoryById(SysCodeRepository sysCodeRepository) {

    }

    @Override
    public void saveOrUpdateSysCodeRepository(SysCodeRepository sysCodeRepository) {

    }

    @Override
    public void saveOrUpdateSysGitBranche(SysGitBranche sysGitBranche) {

    }

    @Override
    public Long getMaxTenantRemainderTimeByTenantIds(String tenantIds) {
        return null;
    }

    @Override
    public String sysCodeSourceById(String id) {
        return null;
    }

    @Override
    public List<String> queryDeptSubordinateByCode(String orgCodes) {
        return null;
    }


    @Override
    public List<DictModel> queryAllDepartBackDictModel() {
        return null;
    }

    @Override
    public void updateSysAnnounReadFlag(String busType, String busId) {

    }

    @Override
    public List<DictModel> queryFilterTableDictInfo(String table, String text, String code, String filterSql) {
        return null;
    }

    @Override
    public List<String> queryTableDictByKeys(String table, String text, String code, String[] keyArray) {
        log.error("queryTableDictByKeys查询失败 {}", cause);
        return null;
    }

    @Override
    public List<ComboModel> queryAllUserBackCombo() {
        return null;
    }

    @Override
    public JSONObject queryAllUser(String userIds, Integer pageNo, int pageSize) {
        return null;
    }

    @Override
    public List<ComboModel> queryAllRole(String[] roleIds) {
        log.error("获取角色信息失败 {}", cause);
        return null;
    }

    @Override
    public List<String> getRoleIdsByUsername(String username) {
        return null;
    }

    @Override
    public String getDepartIdsByOrgCode(String orgCode) {
        return null;
    }

    @Override
    public List<SysDepartModel> getAllSysDepart() {
        return null;
    }

    @Override
    public DictModel getParentDepartId(String departId) {
        return null;
    }

    @Override
    public List<String> getDeptHeadByDepId(String deptId) {
        return null;
    }

    @Override
    public String getDeptNameByDepId(String deptId) {
        return null;
    }

    @Override
    public String getDeptIdByDepName(String departName) {
        return null;
    }

    @Override
    public void sendWebSocketMsg(String[] userIds, String cmd) {

    }

    @Override
    public List<LoginUser> queryAllUserByIds(String[] userIds) {
        return null;
    }

    @Override
    public void meetingSignWebsocket(String userId) {

    }

    @Override
    public List<LoginUser> queryUserByNames(String[] userNames) {
        return null;
    }

    @Override
    public Set<String> getUserRoleSet(String username) {
        return null;
    }

    @Override
    public Set<String> getAllUserRoleSet(String username) {
        return null;
    }

    @Override
    public String createSysReportManager(SysReportManager sysReportManager) {
        log.error("发送消息失败 {}", cause);
        return null;
    }

    @Override
    public void updateSysReportManager(SysReportManager sysReportManager) {
        log.error("发送消息失败 {}", cause);
    }

    @Override
    public List<String> getDepartCodesByUsername(String username) {
        log.error("查询用户所有群组code失败 {}", cause);
        return Collections.emptyList();
    }

    @Override
    public void saveUserDepartIds(SaveUserDepartIdsDTO saveUserDepartIdsDTO) {
    }

    @Override
    public void applyAccessFile(ApplyAccessFileParam applyAccessFileParam) {
        log.error("发送消息失败 {}", cause);
    }

    @Override
    public Set<String> getUserPermissionSet(String username) {
        return null;
    }

    @Override
    public boolean hasOnlineAuth(OnlineAuthDTO onlineAuthDTO) {
        return false;
    }

    @Override
    public SysDepartModel selectAllById(String id) {
        return null;
    }

    @Override
    public List<String> queryDeptUsersByUserId(String userId) {
        return null;
    }

    @Override
    public List<String> queryDeptUserNamesByUserId(String userId) {
        return null;
    }

    @Override
    public Set<String> queryUserRoles(String username) {
        return null;
    }

    @Override
    public Set<String> queryUserAuths(String username) {
        return null;
    }

    @Override
    public DynamicDataSourceModel getDynamicDbSourceById(String dbSourceId) {
        return null;
    }

    @Override
    public DynamicDataSourceModel getDynamicDbSourceByCode(String dbSourceCode) {
        return null;
    }

    @Override
    public LoginUser getUserByName(String username) {
        log.error("通过账号名获取当前登录用户信息 {}", cause);
        return null;
    }

    @Override
    public String translateDictFromTable(String table, String text, String code, String key) {
        return null;
    }

    @Override
    public String translateDict(String code, String key) {
        return null;
    }

    @Override
    public List<SysPermissionDataRuleModel> queryPermissionDataRule(String component, String requestPath, String username) {
        return null;
    }

    @Override
    public SysUserCacheInfo getCacheUser(String username) {
        log.error("获取用户信息失败 {}", cause);
        return null;
    }

    @Override
    public List<JSONObject> queryUsersByUsernames(String usernames) {
        return null;
    }

    @Override
    public List<JSONObject> queryUsersByIds(String ids) {
        return null;
    }

    @Override
    public List<SysDepartModel> queryDepartsByOrgCodes(Set<String> orgCodeList) {
        return null;
    }
    @Override
    public void sendEmailMsg(Object esEmail) {

    }

    @Override
    public List<Map> getDeptUserByOrgCode(String orgCode) {
        return null;
    }

    @Override
    public String sdlSiteManagerQueryName(boolean status) {
        return null;
    }

    @Override
    public Result<?> saveToOss(MultipartFile multipartFile, String path) {
        return null;
    }

    @Override
    public List<String> getAdminUser() {
        return null;
    }

    @Override
    public List<String> getCanUseUserNameList(String userName) {
        return null;
    }

    @Override
    public List<String> getAllUserWithoutAdmin() {
        return null;
    }

    @Override
    public SysConfigGitlab getGitlabSourceId(String sourceId) {
        log.error("发送消息失败 {}", cause);
        return null;
    }

    @Override
    public List<String> getRoleNameByUserName(String username) {
        return null;
    }

    @Override
    public List<String> getUserNameByRoleIdList(List<String> roleIdList) {
        return null;
    }

    @Override
    public String getSecretByUserId(String userId) {
        return null;
    }

    @Override
    public String getSysConfigParamByKey(String key) {
        log.error("发送消息失败 {}", cause);
        return null;
    }

    @Override
    public void setSysConfigParamByKey(SysConfigParam sysConfigParam) {

    }

    @Override
    public List<SysDepartModel> queryDepartsByIds(Set<String> departIdList) {
        return null;
    }

    @Override
    public List<LoginUser> getLoginUserListByDepartId(String departId) {
        return null;
    }
}
