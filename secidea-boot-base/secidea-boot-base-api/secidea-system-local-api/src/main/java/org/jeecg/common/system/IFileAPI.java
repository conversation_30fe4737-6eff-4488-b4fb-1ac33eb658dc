package org.jeecg.common.system;

import org.jeecg.common.api.vo.Result;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

/**
 *
 * <AUTHOR>
 */
public interface IFileAPI {

    /**
     * 保存文件到系统模块
     * @return
     */
    Result<?> saveToOss(@RequestPart("file") MultipartFile multipartFile, @RequestParam(required = false) String path);

}
