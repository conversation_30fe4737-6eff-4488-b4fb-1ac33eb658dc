package org.jeecg.common.system.api;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.common.api.CommonAPI;
import org.jeecg.common.api.dto.OnlineAuthDTO;
import org.jeecg.common.api.dto.SaveUserDepartIdsDTO;
import org.jeecg.common.api.dto.message.*;
import org.jeecg.common.system.vo.*;
import org.jeecg.modules.base.entity.ApplyAccessFileParam;
import org.jeecg.modules.base.entity.SysReportManager;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description  底层共通业务API，提供其他独立模块调用
 * <AUTHOR>
 * @Date 2019-4-20
 * @Version V1.0
 */
public interface ISysBaseAPI extends CommonAPI {


    /**
     * 1发送系统消息
     * @param message 使用构造器赋值参数 如果不设置category(消息类型)则默认为2 发送系统消息
     */
    void sendSysAnnouncement(MessageDTO message);

    /**
     * 2发送消息 附带业务参数
     * @param message 使用构造器赋值参数
     */
    void sendBusAnnouncement(BusMessageDTO message);

    /**
     * 3通过模板发送消息
     * @param message 使用构造器赋值参数
     */
    void sendTemplateAnnouncement(TemplateMessageDTO message);

    /**
     * 4通过模板发送消息 附带业务参数
     * @param message 使用构造器赋值参数
     */
    void sendBusTemplateAnnouncement(BusTemplateMessageDTO message);

    /**
     * 5通过消息中心模板，生成推送内容
     * @param templateDTO 使用构造器赋值参数
     * @return
     */
    String parseTemplateByCode(TemplateDTO templateDTO);

    /**
     * 6根据用户id查询用户信息
     * @param id
     * @return
     */
    LoginUser getUserById(String id);

    /**
     * 7通过用户账号查询角色集合
     * @param username
     * @return
     */
    List<String> getRolesByUsername(String username);

    /**
     * 7.1通过用户账号查询角色集合
     * @param userId
     * @return
     */
    List<String> getRoleCodeByUserId(@RequestParam("userId") String userId);

    /**
     * 8通过用户账号查询群组集合
     * @param username
     * @return 群组 id
     */
    List<String> getDepartIdsByUsername(String username);

    /**
     * 9通过用户账号查询群组 name
     * @param username
     * @return 群组 name
     */
    List<String> getDepartNamesByUsername(String username);



    /** 11查询所有的父级字典，按照create_time排序 */
    public List<DictModel> queryAllDict();

    /**
     * 12查询所有分类字典
     * @return
     */
    public List<SysCategoryModel> queryAllDSysCategory();


    /**
     * 14查询所有群组 作为字典信息 id -->value,departName -->text
     * @return
     */
    public List<DictModel> queryAllDepartBackDictModel();

    /**
     * 15根据业务类型及业务id修改消息已读
     * @param busType
     * @param busId
     */
    public void updateSysAnnounReadFlag(String busType, String busId);

    /**
     * 16查询表字典 支持过滤数据
     * @param table
     * @param text
     * @param code
     * @param filterSql
     * @return
     */
    public List<DictModel> queryFilterTableDictInfo(String table, String text, String code, String filterSql);

    /**
     * 17查询指定table的 text code 获取字典，包含text和value
     * @param table
     * @param text
     * @param code
     * @param keyArray
     * @return
     */
    @Deprecated
    public List<String> queryTableDictByKeys(String table, String text, String code, String[] keyArray);

    /**
     * 18查询所有用户 返回ComboModel
     * @return
     */
    public List<ComboModel> queryAllUserBackCombo();

    /**
     * 19分页查询用户 返回JSONObject
     * @return
     */
    public JSONObject queryAllUser(String userIds, Integer pageNo, Integer pageSize);

    /**
     * 20获取所有角色
     * @return
     */
    public List<ComboModel> queryAllRole();

    /**
     * 21获取所有角色 带参
     * roleIds 默认选中角色
     * @param roleIds
     * @return
     */
    public List<ComboModel> queryAllRole(String[] roleIds );

    /**
     * 22通过用户账号查询角色Id集合
     * @param username
     * @return
     */
    public List<String> getRoleIdsByUsername(String username);

    /**
     * 23通过群组编号查询群组id
     * @param orgCode
     * @return
     */
    public String getDepartIdsByOrgCode(String orgCode);

    /**
     * 24查询所有群组
     * @return
     */
    public List<SysDepartModel> getAllSysDepart();

    /**
     * 25查找父级群组
     * @param departId
     * @return
     */
    DictModel getParentDepartId(String departId);

    /**
     * 26根据群组Id获取群组负责人
     * @param deptId
     * @return
     */
    public List<String> getDeptHeadByDepId(String deptId);

    /**
     * 根据群组Id获取群组名称
     * @param deptId
     * @return
     */
    public String getDeptNameByDepId(String deptId);

    /**
     * 根据群组名称获取群组id
     * @param departName
     * @return
     */
    public String getDeptIdByDepName(String departName);

    /**
     * 27给指定用户发消息
     * @param userIds
     * @param cmd
     */
    public void sendWebSocketMsg(String[] userIds, String cmd);

    /**
     * 28根据id获取所有参与用户
     * userIds
     * @return
     */
    public List<LoginUser> queryAllUserByIds(String[] userIds);

    /**
     * 29将会议签到信息推动到预览
     * userIds
     * @return
     * @param userId
     */
    void meetingSignWebsocket(String userId);

    /**
     * 30根据name获取所有参与用户
     * userNames
     * @return
     */
    List<LoginUser> queryUserByNames(String[] userNames);


    /**
     * 31获取用户的角色集合
     * @param username
     * @return
     */
    Set<String> getUserRoleSet(String username);

    /**
     * 32获取用户的权限集合
     * @param username
     * @return
     */
    Set<String> getUserPermissionSet(String username);

    /**
     * 33判断是否有online访问的权限
     * @param onlineAuthDTO
     * @return
     */
    boolean hasOnlineAuth(OnlineAuthDTO onlineAuthDTO);

    /**
     * 34通过群组id获取群组全部信息
     */
    SysDepartModel selectAllById(String id);

    /**
     * 35根据用户id查询用户所属公司下所有用户ids
     * @param userId
     * @return
     */
    List<String> queryDeptUsersByUserId(String userId);

    /**
     * 根据用户id查询用户所属公司下所有用户names
     * @param userId
     * @return
     */
    List<String> queryDeptUserNamesByUserId(String userId);

    /**
     * 36根据多个用户账号(逗号分隔)，查询返回多个用户信息
     * @param usernames
     * @return
     */
    List<JSONObject> queryUsersByUsernames(String usernames);

    /**
     * 37根据多个用户ID(逗号分隔)，查询返回多个用户信息
     * @param ids
     * @return
     */
    List<JSONObject> queryUsersByIds(String ids);

    /**
     * 38根据多个群组编码(逗号分隔)，查询返回多个群组信息
     * @param orgCodeList
     * @return
     */
    List<SysDepartModel> queryDepartsByOrgCodes(Set<String> orgCodeList);

    /**
     * 39根据多个群组id(逗号分隔)，查询返回多个群组信息
     * @param departIdList
     * @return
     */
    List<SysDepartModel> queryDepartsByIds(Set<String> departIdList);

    /**
     * 40发送邮件消息
     * @param esEmail
     */
    void sendEmailMsg(Object esEmail);

    /**
     * 41 获取公司下级群组和公司下所有用户信息
     * @param orgCode
     */
    List<Map> getDeptUserByOrgCode(String orgCode);

    /**
     * 42 获取当前站点名称
     *
     */
    String sdlSiteManagerQueryName(boolean status);

    /**
     * 43 根据key获取配置参数sysConfigParam中的value
     *
     */
    String getSysConfigParamByKey(String key);

    void setSysConfigParamByKey(SysConfigParam sysConfigParam);

    /**
     * 45通过角色查询出用户邮箱
     * @return
     */
    List<String> querySysRoleUser(String code);

    /**
     * 46通过用户名称查询出邮箱
     * @return
     */
    List<JSONObject> querySysUser(String userName);

    /**
     * 47添加日志
     * @return
     */
    void sysLogAdd(Integer logType, String logContent, Integer operateType, String method, String requestParam,Long costTime);

    /**
     * 根据依赖仓库id获取依赖信息
     * @return
     */
    SysDepositoryConfig getSysDepositoryConfigById(@RequestParam("id") String id);

    /**
     * 根据系统检测账号id获取信息
     * @param id
     * @return
     */
    SysSourceAccount getSysSourceAccountById(@RequestParam("id") String id);

    /**
     * 根据系统检测账号id获取信息
     * @param sysSourceAccount
     * @return
     */
    SysSourceAccount getSysSourceAccount(SysSourceAccount sysSourceAccount);

    /**
     * 根据系统检测账号id更新信息
     * @param sysSourceAccount
     * @return
     */
    void updateSysSourceAccountById(SysSourceAccount sysSourceAccount);

    /**
     * 保存或修改检测账号信息
     * @param sysSourceAccount
     * @return
     */
    void saveOrUpdateSysSourceAccount(SysSourceAccount sysSourceAccount);

    /**
     * 根据代码仓库id获取信息
     * @param id
     * @return
     */
    SysCodeRepository getSysCodeRepositoryById(@RequestParam("id") String id);

    /**
     * 根据代码仓库id获取信息
     * @param sysCodeRepository
     * @return
     */
    SysCodeRepository getSysCodeRepository(SysCodeRepository sysCodeRepository);

    /**
     * 根据代码仓库id更新信息
     * @param sysCodeRepository
     * @return
     */
    void updateSysCodeRepositoryById(SysCodeRepository sysCodeRepository);

    /**
     * 保存或修改代码仓库信息
     * @param sysCodeRepository
     * @return
     */
    void saveOrUpdateSysCodeRepository(SysCodeRepository sysCodeRepository);

    /**
     * 保存或修改Git分支信息
     * @param sysGitBranche
     * @return
     */
    void saveOrUpdateSysGitBranche(SysGitBranche sysGitBranche);

    /**
     * 获取租户最大剩余时间
     * @param tenantIds
     * @return
     */
    Long getMaxTenantRemainderTimeByTenantIds(@RequestParam("tenantIds") String tenantIds);

    String sysCodeSourceQueryById(String id);

    /**
     * 根据群组编码查询下级id
     * @param orgCodes
     * @return
     */
    List<String> queryDeptSubordinateByCode(@RequestParam("orgCodes") String orgCodes);

    /**
     * 根据用户名查询角色名称 用户列表专用
     *
     * @param username
     * @return
     */
    List<String> getRoleNameByUserName(@RequestParam("username")String username);

    /**
     * 根据角色 ID 获取 userName
     * @param roleIdList
     * @return
     */
    List<String> getUserNameByRoleIdList(@RequestParam("roleIdList")List<String> roleIdList);

    /**
     * 根据userId获取秘钥
     * @param userId
     * @return
     */
    String getSecretByUserId(@RequestParam("userId")String userId);

    SysConfigGitlab getGitlabSourceId(String sourceUrl);

    /**
     * 根据群组ID获取用户，不包括下级群组的用户
     *
     * @param departId 群组ID
     * @return 用户列表
     */
    List<LoginUser> getLoginUserListByDepartId(String departId);

    /**
     * 创建系统报告
     * @param sysReportManager
     */
    String createSysReportManager(SysReportManager sysReportManager);

    /**
     * 更新系统报告
     * @param sysReportManager
     */
    void updateSysReportManager(SysReportManager sysReportManager);

    /**
     * 根据用户名查询所属的所有群组code集合
     *
     * @param username 用户名
     * @return 群组code集合
     */
    List<String> getDepartCodesByUsername(String username);

    /**
     * 添加用户和群组关联关系
     *
     * @param saveUserDepartIdsDTO 用户和群组id集合参数
     */
    void saveUserDepartIds(SaveUserDepartIdsDTO saveUserDepartIdsDTO);

    /**
     * 申请访问文件
     *
     * @param applyAccessFileParam 请求参数
     */
    void applyAccessFile(ApplyAccessFileParam applyAccessFileParam);
}
